#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os
from pathlib import Path

def extract_interfaces():
    """提取所有接口到独立文件"""
    
    # 读取原文档
    with open('copy-order-dev.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有接口标题
    interface_pattern = r'^#### (6\.[0-9]+\.[0-9]+) (.+?)$'
    interfaces = []
    
    lines = content.split('\n')
    for i, line in enumerate(lines):
        match = re.match(interface_pattern, line)
        if match:
            section_num = match.group(1)
            title = match.group(2)
            interfaces.append({
                'section': section_num,
                'title': title,
                'line_num': i,
                'full_title': line
            })
    
    print(f"找到 {len(interfaces)} 个接口")
    
    # 提取每个接口的内容
    for i, interface in enumerate(interfaces):
        # 确定接口内容的结束位置
        start_line = interface['line_num']
        
        # 找到下一个接口的开始位置，或者文档结束
        if i + 1 < len(interfaces):
            end_line = interfaces[i + 1]['line_num']
        else:
            # 找到下一个主要章节或文档结束
            end_line = len(lines)
            for j in range(start_line + 1, len(lines)):
                if re.match(r'^### [0-9]+\.[0-9]+', lines[j]) or re.match(r'^## [0-9]+', lines[j]):
                    end_line = j
                    break
        
        # 提取接口内容
        interface_content = '\n'.join(lines[start_line:end_line]).strip()
        
        # 确定文件路径
        section = interface['section']
        title = interface['title']
        
        # 根据接口类型确定目录
        if section.startswith('6.1.'):
            # 专家端接口
            directory = 'copy-api-doc/expert'
        elif section.startswith('6.2.'):
            # 用户端接口
            directory = 'copy-api-doc/user'
        elif section.startswith('6.3.'):
            # 通用接口
            directory = 'copy-api-doc/common'
        else:
            # 其他（枚举、错误码等）
            directory = 'copy-api-doc/common'
        
        # 生成文件名
        filename = generate_filename(section, title)
        filepath = os.path.join(directory, filename)
        
        # 创建目录
        os.makedirs(directory, exist_ok=True)
        
        # 写入文件
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(interface_content)
        
        print(f"已提取: {filepath}")

def generate_filename(section, title):
    """生成文件名"""
    # 移除特殊字符，保留中文和英文
    clean_title = re.sub(r'[^\w\u4e00-\u9fff\-]', '_', title)
    clean_title = re.sub(r'_+', '_', clean_title).strip('_')
    
    # 生成文件名
    filename = f"{section}_{clean_title}.md"
    return filename

if __name__ == '__main__':
    extract_interfaces()
