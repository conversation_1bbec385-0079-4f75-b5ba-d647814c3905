#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

def verify_extraction():
    """验证接口提取结果"""
    
    # 统计各目录文件数
    expert_files = len([f for f in os.listdir('copy-api-doc/expert') if f.endswith('.md')])
    user_files = len([f for f in os.listdir('copy-api-doc/user') if f.endswith('.md')])
    common_files = len([f for f in os.listdir('copy-api-doc/common') if f.endswith('.md')])
    
    total_files = expert_files + user_files + common_files
    
    print("📊 文件提取统计:")
    print(f"专家端接口 (expert/): {expert_files} 个文件")
    print(f"用户端接口 (user/): {user_files} 个文件")
    print(f"通用接口和其他 (common/): {common_files} 个文件")
    print(f"总计: {total_files} 个文件")
    print()
    
    # 检查文件内容完整性
    print("🔍 检查文件内容完整性:")
    
    # 检查几个关键文件
    key_files = [
        'copy-api-doc/expert/6.1.1_专家申请接口.md',
        'copy-api-doc/user/6.2.1_合约-交易专家列表接口.md',
        'copy-api-doc/common/6.3.1_交易专家等级列表接口.md'
    ]
    
    for file_path in key_files:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = len(content.split('\n'))
                has_api_path = 'api/' in content
                has_response = '响应示例' in content or 'json' in content
                
                print(f"✅ {file_path}")
                print(f"   - 行数: {lines}")
                print(f"   - 包含API路径: {has_api_path}")
                print(f"   - 包含响应示例: {has_response}")
        else:
            print(f"❌ {file_path} - 文件不存在")
    
    print()
    
    # 检查是否有空文件
    print("🔍 检查空文件:")
    empty_files = []
    
    for root, dirs, files in os.walk('copy-api-doc'):
        for file in files:
            if file.endswith('.md'):
                file_path = os.path.join(root, file)
                if os.path.getsize(file_path) < 100:  # 小于100字节认为可能是空文件
                    empty_files.append(file_path)
    
    if empty_files:
        print("⚠️  发现可能的空文件或内容过少的文件:")
        for file in empty_files:
            print(f"   - {file}")
    else:
        print("✅ 没有发现空文件")
    
    print()
    
    # 检查文件名格式
    print("🔍 检查文件名格式:")
    invalid_names = []
    
    for root, dirs, files in os.walk('copy-api-doc'):
        for file in files:
            if file.endswith('.md') and file != 'README.md':
                if not re.match(r'^6\.[0-9]+\.[0-9]+_.*\.md$', file):
                    invalid_names.append(os.path.join(root, file))
    
    if invalid_names:
        print("⚠️  发现格式不正确的文件名:")
        for file in invalid_names:
            print(f"   - {file}")
    else:
        print("✅ 所有文件名格式正确")
    
    print()
    print("✅ 接口文档提取完成！")
    print(f"📁 所有文件已保存到 copy-api-doc/ 目录")
    print(f"📖 查看 copy-api-doc/README.md 了解详细的文件结构")

if __name__ == '__main__':
    verify_extraction()
