#!/bin/bash

# 查找所有接口定义
grep -n "^#### 6\.[0-9]\.[0-9]" copy-order-dev.md > interfaces.txt

# 查找所有接口逻辑说明
grep -n "^**接口逻辑**：" copy-order-dev.md > logic.txt

# 输出结果
echo "接口总数: $(wc -l < interfaces.txt)"
echo "已有逻辑说明数: $(wc -l < logic.txt)"
echo "缺少逻辑说明数: $(($(wc -l < interfaces.txt) - $(wc -l < logic.txt)))"

# 输出缺少逻辑说明的接口
echo "缺少逻辑说明的接口:"
while read -r line; do
  interface_num=$(echo "$line" | cut -d' ' -f2)
  interface_name=$(echo "$line" | cut -d' ' -f3-)
  
  # 检查该接口是否有逻辑说明
  interface_line=$(echo "$line" | cut -d':' -f1)
  next_interface_line=$(grep -A1 "$interface_line" interfaces.txt | tail -n1 | cut -d':' -f1)
  
  if [ -z "$next_interface_line" ]; then
    next_interface_line=$(wc -l < copy-order-dev.md)
  fi
  
  if ! grep -q "**接口逻辑**：" <(sed -n "${interface_line},${next_interface_line}p" copy-order-dev.md); then
    echo "$interface_num $interface_name"
  fi
done < interfaces.txt
