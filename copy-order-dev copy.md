# 跟单模块开发文档（修正版）

## 1. 需求概述

跟单模块是一个完整的交易专家带单和用户跟单系统，支持现货和合约两种交易类型。系统包括交易专家申请、等级管理、数据统计、跟单模式选择等核心功能。

### 1.1 核心功能模块

- **交易专家申请与管理**：支持合约和现货交易专家申请
- **个人设置管理**：专家个性化配置和隐私设置
- **等级系统**：基于业绩的等级升降
- **跟单模式**：智能比例跟单和多元探索跟单
- **尊享模式**：白名单邀请的私享带单环境
- **数据统计与展示**：全面的带单和分润数据分析
- **关注与反馈**：用户关注和问题反馈机制

## 2. 数据库设计

### 2.1 cpx_user 表扩展

现有 `cpx_user` 表中已存在 `display_name` 字段，新增关联字段：

```sql
ALTER TABLE `cpx_user`
ADD COLUMN `contract_expert_id` bigint NULL COMMENT '合约交易专家ID',
ADD COLUMN `spot_expert_id` bigint NULL COMMENT '现货交易专家ID',
```

### 2.2 合约交易专家表（申请+设置合并）

```sql
CREATE TABLE `copy_contract_expert` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
    `introduction` text NOT NULL COMMENT '个人介绍',
    `transfer_from_account` tinyint NOT NULL COMMENT '划转资金来源账户，枚举：\App\Model\Enums\User\AccountType::class',
    `transfer_amount` decimal(20,8) NOT NULL COMMENT '划转金额（USDT）',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '申请状态：1-待审核，2-审核通过，3-审核拒绝',
    `review_remark` varchar(500) NULL COMMENT '审核备注',
    `reviewed_at` timestamp NULL COMMENT '审核时间',
    `reviewed_by` bigint unsigned NULL COMMENT '审核人ID',
    `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否开启带单：1-是，0-否',
    `show_total_assets` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示总资产：1-是，0-否',
    `show_expert_rating` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示专家评分及排名：1-是，0-否',
    `position_protection` tinyint NOT NULL DEFAULT '1' COMMENT '未结仓位保护：1-开启，0-关闭',
    `min_follow_amount` decimal(20,8) NULL COMMENT '最小跟单金额（USDT）',
    `recommend_params` json NULL COMMENT '推荐参数配置',
    `currency_ids` json NULL COMMENT '跟单币种 id 配置（支持多币种）',
    `profit_sharing_rate` decimal(8,2) NOT NULL DEFAULT '0.0000' COMMENT '分润比例 %',
    `level_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '专家等级ID',
    `rating` decimal(8,2) NULL COMMENT '专家评分',
    `exclusive_mode` tinyint NOT NULL DEFAULT '0' COMMENT '尊享模式：1-开启，0-关闭',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_level_id` (`level_id`),
    INDEX `idx_exclusive_mode` (`exclusive_mode`)
) COMMENT='合约交易专家表';
```

### 2.3 现货交易专家表（申请+设置合并）

```sql
CREATE TABLE `copy_spot_expert` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
    `introduction` text NOT NULL COMMENT '个人介绍',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '申请状态：1-待审核，2-审核通过，3-审核拒绝',
    `review_remark` varchar(500) NULL COMMENT '审核备注',
    `reviewed_at` timestamp NULL COMMENT '审核时间',
    `reviewed_by` bigint unsigned NULL COMMENT '审核人ID',
    `is_active` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启现货带单：1-是，0-否',
    `show_total_assets` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示总资产：1-是，0-否',
    `show_fund_composition` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示资金构成：1-是，0-否',
    `new_currency_auto_copy` tinyint NOT NULL DEFAULT '0' COMMENT '新上线的交易对自动开启带单：1-是，0-否',
    `position_protection` tinyint NOT NULL DEFAULT '1' COMMENT '未结仓位保护：1-开启，0-关闭',
    `min_follow_amount` decimal(20,8) NULL COMMENT '最小跟单金额（USDT）',
    `recommend_params` json NULL COMMENT '推荐参数配置',
    `currency_ids` json NULL COMMENT '跟单币种 id 配置（支持多币种）',
    `profit_sharing_rate` decimal(8,2) NOT NULL DEFAULT '0.0000' COMMENT '分润比例 %',
    `level_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '专家等级ID',
    `exclusive_mode` tinyint NOT NULL DEFAULT '0' COMMENT '尊享模式：1-开启，0-关闭',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_level_id` (`level_id`),
    INDEX `idx_exclusive_mode` (`exclusive_mode`)
) COMMENT='现货交易专家表';
```

**推荐参数（recommend_params） JSON 格式：**

```json
{
  "fixed_amount": {
    "amount": "100.00",
    "stop_loss_rate": "10.00",
    "take_profit_rate": "20.00",
    "max_copy_amount": "1000.00"
  },
  "multiplier": {
    "multiplier": "0.1",
    "stop_loss_rate": "10.00",
    "take_profit_rate": "20.00",
    "max_copy_amount": "1000.00"
  }
}
```

### 2.4 交易专家等级表（新增等级图标）

```sql
CREATE TABLE `copy_expert_level` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type` tinyint NOT NULL COMMENT '类型：1-合约，2-现货',
    `level` int NOT NULL COMMENT '等级',
    `name` varchar(50) NOT NULL COMMENT '等级名称',
    `icon` varchar(255) NULL COMMENT '等级图标',
    `condition_amount` decimal(20,8) NOT NULL COMMENT '条件一：带单金额（USDT）',
    `condition_follow_amount` decimal(20,8) NULL COMMENT '条件二：跟单者总跟单资金（USDT）',
    `condition_follow_count` int NULL COMMENT '条件二：跟单交易人数（人）',
    `max_follow_count` int NOT NULL COMMENT '最大可带单人数',
    `max_profit_rate` decimal(8,2) NOT NULL COMMENT '最大分润比例 %',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type_level` (`type`, `level`)
) COMMENT='交易专家等级表';
```

### 2.5 关注表

```sql
CREATE TABLE `copy_follow` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint unsigned NOT NULL COMMENT '关注者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_type` varchar(255) NOT NULL COMMENT '专家模型类名（多态关联）',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
    `created_at` timestamp NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_expert` (`user_id`, `expert_id`, `type`),
    INDEX `idx_expert` (`expert_id`, `expert_type`)
) COMMENT='关注表';
```

### 2.6 问题反馈表（合并问题反馈和身份撤销）

```sql
CREATE TABLE `copy_feedback` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_type` varchar(255) NOT NULL COMMENT '专家模型类名（多态关联）',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
    `feedback_type` tinyint NOT NULL COMMENT '反馈类型：1-问题反馈，2-身份撤销',
    `problem_type` varchar(50) NOT NULL COMMENT '问题类型',
    `content` text NOT NULL COMMENT '反馈内容',
    `refund_account_type` tinyint NULL COMMENT '资金退回账户类型：1-现货账户，2-合约账户（仅合约专家撤销）',
    `refund_amount` decimal(20,8) NULL COMMENT '退回金额（仅身份撤销）',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_expert` (`expert_id`, `expert_type`)
) COMMENT='问题反馈表';
```

### 2.7 合约跟单记录表

```sql
CREATE TABLE `copy_contract_order` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `mode` tinyint NOT NULL COMMENT '跟单模式：1-智能比例，2-多元探索',
    `expert_order_id` bigint unsigned NOT NULL COMMENT '专家订单ID（关联trade_perpetual_order）',
    `expert_position_id` bigint unsigned NOT NULL COMMENT '专家仓位ID（关联trade_perpetual_position）',
    `follower_order_id` bigint unsigned NOT NULL COMMENT '跟单者订单ID（关联trade_perpetual_order）',
    `follower_position_id` bigint unsigned NOT NULL COMMENT '跟单者仓位ID（关联trade_perpetual_position）',
    `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
    `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
    `copy_settings_snapshot` json NOT NULL COMMENT '跟单配置参数快照',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_follower_user_id` (`follower_user_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_expert_order_id` (`expert_order_id`, `expert_position_id`),
    INDEX `idx_follower_order_id` (`follower_order_id`, `follower_position_id`)
) COMMENT='合约跟单记录表';
```

### 2.8 合约跟单仓位记录表

```sql
CREATE TABLE `copy_contract_position` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `expert_position_id` bigint unsigned NOT NULL COMMENT '专家仓位ID（关联trade_perpetual_position）',
    `follower_position_id` bigint unsigned NOT NULL COMMENT '跟单者仓位ID（关联trade_perpetual_position）',
    `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
    `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
    `copy_settings_snapshot` json NOT NULL COMMENT '跟单配置参数快照',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_follower_user_id` (`follower_user_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_expert_position_id` (`expert_position_id`),
    INDEX `idx_follower_position_id` (`follower_position_id`)
) COMMENT='合约跟单仓位记录表';
```

### 2.9 现货跟单记录表

```sql
CREATE TABLE `copy_spot_order` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `mode` tinyint NOT NULL COMMENT '跟单模式：1-智能比例，2-多元探索',
    `expert_order_id` bigint unsigned NOT NULL COMMENT '专家订单ID（关联trade_spot_order）',
    `follower_order_id` bigint unsigned NOT NULL COMMENT '跟单者订单ID（关联trade_spot_order）',
    `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
    `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
    `copy_settings_snapshot` json NOT NULL COMMENT '跟单配置参数快照',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_follower_user_id` (`follower_user_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_expert_order_id` (`expert_order_id`),
    INDEX `idx_follower_order_id` (`follower_order_id`)
) COMMENT='现货跟单记录表';
```

### 2.10 合约跟单配置表

```sql
CREATE TABLE `copy_contract_user_setting` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `mode` tinyint NOT NULL COMMENT '跟单模式：1-智能比例，2-多元探索',
    `investment_amount` decimal(20,8) NULL COMMENT '投资金额（USDT）（智能比例模式）',
    `copy_type` tinyint NULL COMMENT '跟单方式：1-固定额度，2-倍率（多元探索模式）',
    `fixed_amount` decimal(20,8) NULL COMMENT '固定额度（USDT）（多元探索模式）',
    `rate` decimal(8,2) NULL COMMENT '倍率 %（多元探索模式）',
    `stop_loss_rate` decimal(8,2) NULL COMMENT '止损比例 %',
    `take_profit_rate` decimal(8,2) NULL COMMENT '止盈比例 %',
    `max_follow_amount` decimal(20,8) NULL COMMENT '最大跟随金额',
    `slippage_rate` decimal(8,2) NULL COMMENT '滑点比例 %',
    `auto_new_pairs` tinyint NOT NULL DEFAULT '0' COMMENT '自动跟随新币对：1-是，0-否',
    `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
    `copy_currencies` json NULL COMMENT '跟单币种配置（支持多币种）',
    `net_value_guardian` tinyint NOT NULL DEFAULT '0' COMMENT '净值守护者：1-开启，0-关闭（智能比例模式）',
    `max_loss_amount` decimal(20,8) NULL COMMENT '最大亏损金额（触发则解除跟单）（智能比例模式）',
    `min_net_value` decimal(20,8) NULL COMMENT '最小净值金额（触发则解除跟单）（智能比例模式）',
    `copy_all_positions` tinyint NOT NULL DEFAULT '0' COMMENT '跟单后是否复制全部仓位：1-是，0-否（智能比例模式）',
    `margin_mode` tinyint NOT NULL DEFAULT '1' COMMENT '保证金模式：1-跟随专家，2-全仓，3-逐仓',
    `leverage_mode` tinyint NOT NULL DEFAULT '1' COMMENT '杠杆设置：1-跟随专家，2-指定杠杆',
    `custom_leverage` int NULL COMMENT '自定义杠杆倍数（仅当leverage_mode=2时有效）',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-跟单中，2-暂停',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_follower_expert` (`follower_user_id`, `expert_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_status` (`status`)
) COMMENT='用户跟单配置表';
```

### 2.11 现货跟单配置表

```sql
CREATE TABLE `copy_spot_user_setting` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `fixed_amount` decimal(20,8) NULL COMMENT '固定额度（USDT）',
    `rate` decimal(8,2) NULL COMMENT '倍率 %',
    `stop_loss_rate` decimal(8,2) NULL COMMENT '止损比例 %',
    `take_profit_rate` decimal(8,2) NULL COMMENT '止盈比例 %',
    `max_follow_amount` decimal(20,8) NULL COMMENT '最大跟随金额',
    `auto_new_pairs` tinyint NOT NULL DEFAULT '0' COMMENT '自动跟随新币对：1-是，0-否',
    `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
    `copy_currencies` json NULL COMMENT '跟单币种配置（支持多币种）',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-跟单中，2-暂停',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_follower_expert` (`follower_user_id`, `expert_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_status` (`status`)
) COMMENT='用户跟单配置表';
```

### 2.12 合约多元探索跟单高级设置表

```sql
CREATE TABLE `copy_contract_advanced_setting` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `currency_id` bigint unsigned NOT NULL COMMENT '币种ID',
    `copy_type` tinyint NOT NULL COMMENT '跟单方式：1-固定额度，2-倍率',
    `fixed_amount` decimal(20,8) NULL COMMENT '固定额度（USDT）',
    `rate` decimal(8,2) NULL COMMENT '倍率 %',
    `stop_loss_rate` decimal(8,2) NULL COMMENT '止损比例 %',
    `take_profit_rate` decimal(8,2) NULL COMMENT '止盈比例 %',
    `max_follow_amount` decimal(20,8) NULL COMMENT '最大跟随金额',
    `slippage_rate` decimal(8,2) NULL COMMENT '滑点比例 %',
    `margin_mode` tinyint NOT NULL DEFAULT '1' COMMENT '保证金模式：1-跟随专家，2-全仓，3-逐仓',
    `leverage_mode` tinyint NOT NULL DEFAULT '1' COMMENT '杠杆设置：1-跟随专家，2-指定杠杆',
    `custom_leverage` int NULL COMMENT '自定义杠杆倍数（仅当leverage_mode=2时有效）',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_follower_expert_currency` (`follower_user_id`, `expert_id`, `currency_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_currency_id` (`currency_id`)
) COMMENT='合约多元探索跟单高级设置表';
```

### 2.13 现货多元探索跟单高级设置表

```sql
CREATE TABLE `copy_spot_advanced_setting` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `currency_id` bigint unsigned NOT NULL COMMENT '币种ID',
    `copy_type` tinyint NOT NULL COMMENT '跟单方式：1-固定额度，2-倍率',
    `fixed_amount` decimal(20,8) NULL COMMENT '固定额度（USDT）',
    `rate` decimal(8,2) NULL COMMENT '倍率 %',
    `stop_loss_rate` decimal(8,2) NULL COMMENT '止损比例 %',
    `take_profit_rate` decimal(8,2) NULL COMMENT '止盈比例 %',
    `max_follow_amount` decimal(20,8) NULL COMMENT '最大跟随金额',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_follower_expert_currency` (`follower_user_id`, `expert_id`, `currency_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_currency_id` (`currency_id`)
) COMMENT='现货多元探索跟单高级设置表';
```

### 2.14 尊享模式邀请表（新增分润比例字段）

```sql
CREATE TABLE `copy_exclusive_invitation` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_type` varchar(255) NOT NULL COMMENT '专家模型类名（多态关联）',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
    `title` varchar(100) NULL COMMENT '链接标题',
    `invite_code` varchar(32) NOT NULL COMMENT '邀请码',
    `max_count` int NULL COMMENT '最大邀请人数',
    `current_count` int NULL DEFAULT '0' COMMENT '当前邀请人数',
    `profit_sharing_rate` decimal(8,2) NULL COMMENT '分润比例 %',
    `expired_at` timestamp NULL COMMENT '过期时间',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_invite_code` (`invite_code`),
    INDEX `idx_expert` (`expert_id`, `expert_type`),
    INDEX `idx_expert_user_id` (`expert_user_id`)
) COMMENT='尊享模式邀请表';
```

### 2.15 尊享模式成员表（调整字段）

```sql
CREATE TABLE `copy_exclusive_member` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `invitation_id` bigint unsigned NOT NULL COMMENT '邀请记录ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_type` varchar(255) NOT NULL COMMENT '专家模型类名（多态关联）',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例（可单独修改）%',
    `joined_at` timestamp NOT NULL COMMENT '加入时间',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_invitation_follower` (`invitation_id`, `follower_user_id`),
    INDEX `idx_expert` (`expert_id`, `expert_type`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_follower_user_id` (`follower_user_id`)
) COMMENT='尊享模式成员表';
```

### 2.16 合约分润记录表

```sql
CREATE TABLE `copy_contract_profit_sharing` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `copy_position_id` bigint unsigned NOT NULL COMMENT '跟单仓位记录ID',
    `expert_position_id` bigint unsigned NOT NULL COMMENT '专家仓位ID（关联trade_perpetual_position）',
    `follower_position_id` bigint unsigned NOT NULL COMMENT '跟单者仓位ID（关联trade_perpetual_position）',
    `profit` decimal(20,8) NOT NULL COMMENT '盈亏金额',
    `profit_sharing` decimal(20,8) NOT NULL COMMENT '分润金额',
    `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
    `currency_id` bigint unsigned NOT NULL COMMENT '分润币种ID（默认USDT）',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_follower_user_id` (`follower_user_id`),
    INDEX `idx_copy_position_id` (`copy_position_id`),
    INDEX `idx_expert_position_id` (`expert_position_id`),
    INDEX `idx_follower_position_id` (`follower_position_id`)
) COMMENT='合约分润记录表';
```

### 2.17 现货分润记录表

```sql
CREATE TABLE `copy_spot_profit_sharing` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `copy_order_id` bigint unsigned NOT NULL COMMENT '跟单记录ID',
    `expert_order_id` bigint unsigned NOT NULL COMMENT '专家订单ID（关联trade_spot_order）',
    `follower_order_id` bigint unsigned NOT NULL COMMENT '跟单者订单ID（关联trade_spot_order）',
    `profit` decimal(20,8) NOT NULL COMMENT '盈亏金额',
    `profit_sharing` decimal(20,8) NOT NULL COMMENT '分润金额',
    `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
    `currency_id` bigint unsigned NOT NULL COMMENT '分润币种ID（默认USDT）',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_follower_user_id` (`follower_user_id`),
    INDEX `idx_copy_order_id` (`copy_order_id`),
    INDEX `idx_expert_order_id` (`expert_order_id`),
    INDEX `idx_follower_order_id` (`follower_order_id`)
) COMMENT='现货分润记录表';
```

### 2.18 分润比例修改记录表

```sql
CREATE TABLE `copy_profit_rate_log` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_type` varchar(255) NOT NULL COMMENT '专家模型类名（多态关联）',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
    `old_rate` decimal(8,2) NULL COMMENT '原分润比例 %',
    `new_rate` decimal(8,2) NOT NULL COMMENT '新分润比例 %',
    `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_expert` (`expert_id`, `expert_type`),
    INDEX `idx_expert_user_id` (`expert_user_id`)
) COMMENT='分润比例修改记录表';
```

### 2.19 合约交易专家统计表

```sql
CREATE TABLE `copy_contract_expert_statistics` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    -- 总盈利、7日总盈利、30日总盈利、90日总盈利、180日总盈利
    `total_profit` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '总盈利',
    `total_profit_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日总盈利',
    `total_profit_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日总盈利',
    `total_profit_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日总盈利',
    `total_profit_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日总盈利',
    -- 总亏损、7日总亏损、30日总亏损、90日总亏损、180日总亏损
    `total_loss` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '总亏损',
    `total_loss_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日总亏损',
    `total_loss_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日总亏损',
    `total_loss_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日总亏损',
    `total_loss_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日总亏损',
    -- 盈利订单数（仓位单位）、7日盈利订单数（仓位单位）、30日盈利订单数（仓位单位）、90日盈利订单数（仓位单位）、180日盈利订单数（仓位单位）
    `profit_order_count` int NOT NULL DEFAULT '0' COMMENT '盈利订单数',
    `profit_order_count_7d` int NOT NULL DEFAULT '0' COMMENT '7日盈利订单数',
    `profit_order_count_30d` int NOT NULL DEFAULT '0' COMMENT '30日盈利订单数',
    `profit_order_count_90d` int NOT NULL DEFAULT '0' COMMENT '90日盈利订单数',
    `profit_order_count_180d` int NOT NULL DEFAULT '0' COMMENT '180日盈利订单数',
    -- 亏损订单数（仓位单位）、7日亏损订单数（仓位单位）、30日亏损订单数（仓位单位）、90日亏损订单数（仓位单位）、180日亏损订单数（仓位单位）
    `loss_order_count` int NOT NULL DEFAULT '0' COMMENT '亏损订单数',
    `loss_order_count_7d` int NOT NULL DEFAULT '0' COMMENT '7日亏损订单数',
    `loss_order_count_30d` int NOT NULL DEFAULT '0' COMMENT '30日亏损订单数',
    `loss_order_count_90d` int NOT NULL DEFAULT '0' COMMENT '90日亏损订单数',
    `loss_order_count_180d` int NOT NULL DEFAULT '0' COMMENT '180日亏损订单数',
    -- 平均盈利、7日平均盈利、30日平均盈利、90日平均盈利、180日平均盈利
    `average_profit` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '平均盈利',
    `average_profit_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日平均盈利',
    `average_profit_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日平均盈利',
    `average_profit_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日平均盈利',
    `average_profit_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日平均盈利',
    -- 平均亏损、7日平均亏损、30日平均亏损、90日平均亏损、180日平均亏损
    `average_loss` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '平均亏损',
    `average_loss_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日平均亏损',
    `average_loss_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日平均亏损',
    `average_loss_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日平均亏损',
    `average_loss_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日平均亏损',
    -- 盈亏金额
    `profit` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '盈亏金额',
    `profit_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日盈亏金额',
    `profit_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日盈亏金额',
    `profit_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日盈亏金额',
    `profit_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日盈亏金额',
    -- 胜率、7日胜率、30日胜率、90日胜率、180日胜率
    `win_rate` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '胜率 %',
    `win_rate_7d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '7日胜率 %',
    `win_rate_30d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '30日胜率 %',
    `win_rate_90d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '90日胜率 %',
    `win_rate_180d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '180日胜率 %',
    -- 收益率、7日收益率、30日收益率、90日收益率、180日收益率
    `profit_rate` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '收益率 %',
    `profit_rate_7d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '7日收益率 %',
    `profit_rate_30d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '30日收益率 %',
    `profit_rate_90d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '90日收益率 %',
    `profit_rate_180d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '180日收益率 %',
    -- 跟单者收益、7日跟单者收益、30日跟单者收益、90日跟单者收益、180日跟单者收益
    `follower_profit` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '跟单者收益',
    `follower_profit_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日跟单者收益',
    `follower_profit_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日跟单者收益',
    `follower_profit_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日跟单者收益',
    `follower_profit_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日跟单者收益',
    -- 最大回撤、7日最大回撤、30日最大回撤、90日最大回撤、180日最大回撤
    `max_drawdown` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '最大回撤',
    `max_drawdown_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日最大回撤',
    `max_drawdown_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日最大回撤',
    `max_drawdown_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日最大回撤',
    `max_drawdown_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日最大回撤',
    -- 资产管理规模
    `aum` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '资产管理规模',
    -- 交易频率、7日交易频率、30日交易频率、90日交易频率、180日交易频率
    `trade_frequency` int NOT NULL DEFAULT '0' COMMENT '交易频率',
    `trade_frequency_7d` int NOT NULL DEFAULT '0' COMMENT '7日交易频率',
    `trade_frequency_30d` int NOT NULL DEFAULT '0' COMMENT '30日交易频率',
    `trade_frequency_90d` int NOT NULL DEFAULT '0' COMMENT '90日交易频率',
    `trade_frequency_180d` int NOT NULL DEFAULT '0' COMMENT '180日交易频率',
    -- 累计跟单人数
    `total_follower_count` int NOT NULL DEFAULT '0' COMMENT '累计跟单人数',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`)
) COMMENT='合约交易专家统计表';
```

### 2.20 现货交易专家统计表

```sql
CREATE TABLE `copy_spot_expert_statistics` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `profit` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '收益金额',
    `profit_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日收益金额',
    `profit_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日收益金额',
    `profit_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日收益金额',
    `profit_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日收益金额',
    `win_rate` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '胜率 %',
    `win_rate_7d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '7日胜率 %',
    `win_rate_30d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '30日胜率 %',
    `win_rate_90d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '90日胜率 %',
    `win_rate_180d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '180日胜率 %',
    `profit_rate` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '收益率 %',
    `profit_rate_7d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '7日收益率 %',
    `profit_rate_30d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '30日收益率 %',
    `profit_rate_90d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '90日收益率 %',
    `profit_rate_180d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '180日收益率 %',
    `follower_profit` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '跟单者收益',
    `follower_profit_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日跟单者收益',
    `follower_profit_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日跟单者收益',
    `follower_profit_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日跟单者收益',
    `follower_profit_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日跟单者收益',
    -- 资产管理规模
    `aum` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '资产管理规模',
    -- 交易频率、7日交易频率、30日交易频率、90日交易频率、180日交易频率
    `trade_frequency` int NOT NULL DEFAULT '0' COMMENT '交易频率',
    `trade_frequency_7d` int NOT NULL DEFAULT '0' COMMENT '7日交易频率',
    `trade_frequency_30d` int NOT NULL DEFAULT '0' COMMENT '30日交易频率',
    `trade_frequency_90d` int NOT NULL DEFAULT '0' COMMENT '90日交易频率',
    `trade_frequency_180d` int NOT NULL DEFAULT '0' COMMENT '180日交易频率',
    -- 累计跟单人数
    `total_follower_count` int NOT NULL DEFAULT '0' COMMENT '累计跟单人数',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`)
) COMMENT='现货交易专家统计表';
```

## 3. 多态关联说明

### 3.1 多态关联字段规范

- **expert_type**: 存储模型类名，如 `App\Model\Copy\CopyContractExpert`、`App\Model\Copy\CopySpotExpert`
- **expert_user_id**: 冗余字段，便于查询

### 3.2 多态关联实现说明

多态关联用于实现灵活的专家类型关联：

- 使用 morphTo 定义反向多态关联
- 使用 morphMany 定义正向多态关联
- 通过 expert_type 和 expert_id 字段建章关联关系
- 支持合约专家和现货专家的统一关联处理

## 4. 系统设置 Seeder 文件

### 4.1 配置组 Seeder

跟单设置配置组数据：

- 配置组名称：跟单设置
- 配置组标识：copy_order_setting
- 创建者：1（系统管理员）
- 备注：跟单模块相关配置

```php
<?php

declare(strict_types=1);

use Hyperf\Database\Seeders\Seeder;
use Hyperf\DbConnection\Db;

class CopyOrderConfigGroupSeeder extends Seeder
{
    public function run()
    {
        // 检查是否存在配置组
        $configGroup = Db::table('system_setting_config_group')->where('code', 'copy_order_setting')->first();
        if ($configGroup) {
            return;
        }

        Db::table('system_setting_config_group')->insert([
            'name' => '跟单设置',
            'code' => 'copy_order_setting',
            'icon' => null,
            'created_by' => 1,
            'updated_by' => null,
            'created_at' => now(),
            'updated_at' => now(),
            'remark' => '跟单模块相关配置'
        ]);
    }
}
```

### 4.2 配置项 Seeder

跟单系统配置项包括：

1. **合约专家申请是否需要审核**

   - 配置键：`contract_expert_need_review`
   - 输入类型：switch
   - 默认值：1（需要审核）

2. **现货专家申请是否需要审核**

   - 配置键：`spot_expert_need_review`
   - 输入类型：switch
   - 默认值：1（需要审核）

3. **合约专家最小划转资金**

   - 配置键：`contract_expert_min_transfer`
   - 输入类型：input
   - 默认值：100 USDT

4. **问题反馈类型预设**

   - 配置键：`feedback_problem_types`
   - 输入类型：keyValuePair
   - 预设值：技术问题、资金问题、策略问题、平台问题、其他问题

5. **合约交易专家展示条件**

   - 配置键：`contract_expert_display_conditions`
   - 输入类型：keyValuePair
   - 条件项：
     - currency_count（币对数量）：1
     - order_count（订单数量）：1
     - copy_account_assets（账户资产）：100 USDT

6. **现货交易专家展示条件**

   - 配置键：`spot_expert_display_conditions`
   - 输入类型：keyValuePair
   - 条件项：
     - currency_count（币对数量）：1
     - order_count（订单数量）：1
     - spot_account_assets（账户资产）：100 USDT

7. **合约跟单最小资金**

   - 配置键：`contract_min_follow_amount`
   - 输入类型：input
   - 默认值：100 USDT

8. **现货跟单最小资金**

   - 配置键：`spot_min_follow_amount`
   - 输入类型：input
   - 默认值：100 USDT

```php
<?php

declare(strict_types=1);

use Hyperf\Database\Seeders\Seeder;
use Hyperf\DbConnection\Db;

class CopyOrderConfigSeeder extends Seeder
{
    public function run()
    {
        $groupId = Db::table('system_setting_config_group')
            ->where('code', 'copy_order_setting')
            ->value('id');

        if (!$groupId) {
            $groupId = Db::table('system_setting_config_group')->insertGetId([
                'name' => '跟单设置',
                'code' => 'copy_order_setting',
                'icon' => null,
                'created_by' => 1,
                'updated_by' => null,
                'created_at' => now(),
                'updated_at' => now(),
                'remark' => '跟单模块相关配置'
            ]);
        }

        $configs = [
            // 1、合约专家申请是否需要审核
            [
                'group_id' => $groupId,
                'key' => 'contract_expert_need_review',
                'value' => '1',
                'name' => '合约专家申请是否需要审核',
                'input_type' => 'switch',
                'config_select_data' => json_encode([
                    ['label' => '需要审核', 'value' => '1'],
                    ['label' => '无需审核', 'value' => '0']
                ]),
                'sort' => 1,
                'remark' => '1-需要审核，0-无需审核',
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            // 2现货专家申请是否需要审核
            [
                'group_id' => $groupId,
                'key' => 'spot_expert_need_review',
                'value' => '1',
                'name' => '现货专家申请是否需要审核',
                'input_type' => 'switch',
                'config_select_data' => json_encode([
                    ['label' => '需要审核', 'value' => '1'],
                    ['label' => '无需审核', 'value' => '0']
                ]),
                'sort' => 2,
                'remark' => '1-需要审核，0-无需审核',
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            // 3合约专家合约账户最小划转资金
            [
                'group_id' => $groupId,
                'key' => 'contract_expert_min_transfer',
                'value' => '100',
                'name' => '合约专家合约账户最小划转资金',
                'input_type' => 'input',
                'config_select_data' => null,
                'sort' => 3,
                'remark' => '最小划转金额（USDT）',
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            // 4问题反馈类型预设
            [
                'group_id' => $groupId,
                'key' => 'feedback_problem_types',
                'value' => null,
                'name' => '问题反馈类型预设',
                'input_type' => 'keyValuePair',
                'config_select_data' => json_encode([
                    ['label' => '技术问题', 'value' => '技术问题'],
                    ['label' => '资金问题', 'value' => '资金问题'],
                    ['label' => '策略问题', 'value' => '策略问题'],
                    ['label' => '平台问题', 'value' => '平台问题'],
                    ['label' => '其他问题', 'value' => '其他问题']
                ]),
                'sort' => 4,
                'remark' => '问题反馈可选类型',
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            // 5合约交易专家展示条件
            [
                'group_id' => $groupId,
                'key' => 'contract_expert_display_conditions',
                'value' => null,
                'name' => '合约交易专家展示条件',
                'input_type' => 'keyValuePair',
                'config_select_data' => json_encode([
                    ['label' => 'currency_count', 'value' => 1],
                    ['label' => 'order_count', 'value' => 1],
                    ['label' => 'copy_account_assets', 'value' => 100]
                ]),
                'sort' => 5,
                'remark' => 'currency_count:币对数量，order_count:订单数量，copy_account_assets:账户资产（USDT）',
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            // 6现货交易专家展示条件
            [
                'group_id' => $groupId,
                'key' => 'spot_expert_display_conditions',
                'value' => null,
                'name' => '现货交易专家展示条件',
                'input_type' => 'keyValuePair',
                'config_select_data' => json_encode([
                    ['label' => 'currency_count', 'value' => 1],
                    ['label' => 'order_count', 'value' => 1],
                    ['label' => 'spot_account_assets', 'value' => 100]
                ]),
                'sort' => 6,
                'remark' => 'currency_count:币对数量，order_count:订单数量，spot_account_assets:账户资产（USDT）',
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            // 7合约跟单最小资金
            [
                'group_id' => $groupId,
                'key' => 'contract_min_follow_amount',
                'value' => '100',
                'name' => '合约跟单最小资金',
                'input_type' => 'input',
                'config_select_data' => null,
                'sort' => 7,
                'remark' => '合约最小跟单金额（USDT）',
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            // 8现货跟单最小资金
            [
                'group_id' => $groupId,
                'key' => 'spot_min_follow_amount',
                'value' => '100',
                'name' => '现货跟单最小资金',
                'input_type' => 'input',
                'config_select_data' => null,
                'sort' => 8,
                'remark' => '现货最小跟单金额（USDT）',
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        Db::table('system_setting_config')->insert($configs);
    }
}
```

系统设置项获取方式：

```php
$setting = \Plugin\West\SysSettings\Helper\Helper::getSysSettingByTypeCode('key');
// 获取配置项的值
$value = $setting->value;
// 获取配置选项参数（input_type 为 keyValuePair 类型时）
$configSelectData = $setting->config_select_data;
```

## 5. 枚举类设计

### 5.1 交易专家类型枚举

交易专家类型定义：

- CONTRACT = 1：合约交易专家
- SPOT = 2：现货交易专家

使用多语言文件存储显示名称：`copy.expert_type.contract`、`copy.expert_type.spot`

### 5.2 推荐参数类型枚举

推荐参数类型：

- FIXED_AMOUNT = 1：固定额度
- RATE = 2：跟单倍率

### 5.3 跟单状态枚举

跟单状态定义：

- FOLLOWING = 1：跟单中
- PAUSED = 2：暂停

### 5.4 跟单模式枚举

跟单模式：

- SMART_RATE = 1：智能比例跟单
- MULTI_EXPLORE = 2：多元探索跟单

### 5.5 订单状态枚举

订单状态：

- OPEN = 1：未平仓
- CLOSED = 2：已平仓
- CANCELLED = 3：已取消

### 5.6 保证金模式枚举

保证金模式：

- FOLLOW_EXPERT = 1：跟随专家
- CROSS = 2：全仓模式
- ISOLATED = 3：逐仓模式

### 5.7 杠杆模式枚举

杠杆模式：

- FOLLOW_EXPERT = 1：跟随专家
- CUSTOM = 2：自定义杠杆

## 6. API 接口设计（文档）

> 业务逻辑中的统一异常处理方式：
>
> ```php
> use App\Exception\BusinessException;
> use App\Http\Common\ResultCode;
>
> // 状态码：ResultCode::FORBIDDEN 根据业务逻辑选择合适的异常状态码
> throw new BusinessException(ResultCode::FORBIDDEN, 'xxx');
> ```

### 6.1 交易专家端接口

#### 6.1.1 专家申请接口

**POST** `/api/copy/expert/apply`

请求参数：

```json
{
  "type": 1, // 1-合约交易专家, 2-现货交易专家
  "display_name": "展示名称", // 展示名称，可选，更新到 cpx_user 表中
  "introduction": "专业合约交易，稳健收益",
  "transfer_from_account": "1", // 划转资金来源账户，枚举：\App\Model\Enums\User\AccountType::class
  "transfer_amount": "100.********" // type=1时必填，合约专家需要划转资金
}
```

响应示例（需要审核）：

```json
{
  "code": 200,
  "message": "申请提交成功，等待审核",
  "data": {
    "id": 1,
    "user_id": 1,
    "introduction": "专业合约交易，稳健收益",
    "transfer_from_account": 1,
    "transfer_amount": "100.********",
    "status": 1,
    "review_remark": null,
    "reviewed_at": null,
    "reviewed_by": null,
    "is_active": 1,
    "show_total_assets": 0,
    "position_protection": 1,
    "min_follow_amount": null,
    "recommend_params": null,
    "profit_sharing_rate": "0.0000",
    "level_id": 1,
    "exclusive_mode": 0,
    "exclusive_profit_rate": null,
    "created_at": "2025-01-01 00:00:00",
    "updated_at": "2025-01-01 00:00:00",
    "user": {
      "id": 1,
      "account": "**********",
      "username": "test",
      "display_name": "test",
      "email": "<EMAIL>",
      "phone_country_code": "+86",
      "phone": "**********",
      "avatar": "https://test.com/avatar.png",
      "agent_id": 1,
      "language": "zh-CN",
      "created_at": "2025-01-01 00:00:00",
      "updated_at": "2025-01-01 00:00:00"
    }
  }
}
```

响应示例（不需要审核）：

```json
{
  "code": 200,
  "message": "申请成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "introduction": "专业合约交易，稳健收益",
    "status": 1,
    "review_remark": null,
    "reviewed_at": null,
    "reviewed_by": null,
    "is_active": 1,
    "show_total_assets": 0,
    "position_protection": 1,
    "min_follow_amount": null,
    "recommend_params": null,
    "profit_sharing_rate": "0.0000",
    "level_id": 1,
    "exclusive_mode": 0,
    "exclusive_profit_rate": null,
    "created_at": "2025-01-01 00:00:00",
    "updated_at": "2025-01-01 00:00:00",
    "user": {
      "id": 1,
      "account": "**********",
      "username": "test",
      "display_name": "test",
      "email": "<EMAIL>",
      "phone_country_code": "+86",
      "phone": "**********",
      "avatar": "https://test.com/avatar.png",
      "agent_id": 1,
      "language": "zh-CN",
      "created_at": "2025-01-01 00:00:00",
      "updated_at": "2025-01-01 00:00:00"
    }
  }
}
```

接口逻辑：

- 合约交易专家申请

  - 检查用户状态是否为正常状态，如果不是则返回异常
  - 检查用户是否完成 kyc 认证，如果没有则返回异常
  - 检查用户是否已经是合约交易专家，如果是则返回异常
  - 检查用户申请状态，如果是待审核则返回异常
  - 验证划转资金金额是否大于等于系统设置中“合约专家最小划转资金”
  - 资金划转（TODO: 待补充统一资金划转方法），检查划转是否成功
  - 带单币种默认系统中的所有币种
  - 最小跟单金额从系统设置中获取
  - 根据等级条件匹配专家等级
  - 创建合约交易专家表记录（判断系统设置中“合约专家申请是否需要审核”，不需要审核时 status 直接设置为审核通过）
  - 更新用户表中 display_name 字段
  - 返回创建的专家数据

- 现货交易专家申请
  - 检查用户状态是否为正常状态，如果不是则返回异常
  - 检查用户是否完成 kyc 认证，如果没有则返回异常
  - 检查用户是否已经是现货交易专家，如果是则返回异常
  - 检查用户申请状态，如果是待审核则返回异常
  - 带单币种默认系统中的所有币种
  - 最小跟单金额从系统设置中获取
  - 根据等级条件匹配专家等级
  - 创建现货交易专家表记录（判断系统设置中“现货专家申请是否需要审核”，不需要审核时 status 直接设置为审核通过）
  - 更新用户表中 display_name 字段
  - 返回创建的专家数据

#### 6.1.2 交易专家信息接口

**GET** `/api/copy/expert/info`

Query 参数：

- `type`: 1-合约交易专家, 2-现货交易专家

响应示例（合约交易专家）：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "introduction": "专业合约交易，稳健收益",
    "transfer_from_account": 1,
    "transfer_amount": "100.********",
    "status": 1,
    "review_remark": null,
    "reviewed_at": null,
    "reviewed_by": null,
    "is_active": 1,
    "show_total_assets": 0,
    "position_protection": 1,
    "min_follow_amount": null,
    "recommend_params": null,
    "profit_sharing_rate": "0.0000",
    "level_id": 1,
    "exclusive_mode": 0,
    "exclusive_profit_rate": null,
    "created_at": "2025-01-01 00:00:00",
    "updated_at": "2025-01-01 00:00:00",
    "user": {
      "id": 1,
      "account": "**********",
      "username": "test",
      "display_name": "test",
      "email": "<EMAIL>",
      "phone_country_code": "+86",
      "phone": "**********",
      "avatar": "https://test.com/avatar.png",
      "agent_id": 1,
      "language": "zh-CN",
      "created_at": "2025-01-01 00:00:00",
      "updated_at": "2025-01-01 00:00:00"
    },
    "level": {
      "id": 1,
      "type": 1,
      "level": 1,
      "name": "初级专家",
      "icon": "https://test.com/icon.png",
      "max_follow_count": 100,
      "max_profit_rate": 10
    },
    "statistics": {
      // 当前合约跟单账户资金
      // 当前跟单人数
      // 当前带单正在进行中的订单数
    }
  }
}
```

响应示例（现货交易专家）：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "introduction": "专业合约交易，稳健收益",
    "status": 1,
    "review_remark": null,
    "reviewed_at": null,
    "reviewed_by": null,
    "is_active": 1,
    "show_total_assets": 0,
    "position_protection": 1,
    "min_follow_amount": null,
    "recommend_params": null,
    "profit_sharing_rate": "0.0000",
    "level_id": 1,
    "exclusive_mode": 0,
    "exclusive_profit_rate": null,
    "created_at": "2025-01-01 00:00:00",
    "updated_at": "2025-01-01 00:00:00",
    "user": {
      "id": 1,
      "account": "**********",
      "username": "test",
      "display_name": "test",
      "email": "<EMAIL>",
      "phone_country_code": "+86",
      "phone": "**********",
      "avatar": "https://test.com/avatar.png",
      "agent_id": 1,
      "language": "zh-CN",
      "created_at": "2025-01-01 00:00:00",
      "updated_at": "2025-01-01 00:00:00"
    },
    "level": {
      "id": 1,
      "type": 2,
      "level": 1,
      "name": "初级专家",
      "icon": "https://test.com/icon.png",
      "max_follow_count": 100,
      "max_profit_rate": 10
    },
    "statistics": {
      // 当前现货账户资金
      // 当前跟单人数
      // 当前带单正在进行中的订单数
    }
  }
}
```

**接口逻辑**：

- 根据 type 参数查询专家信息及相关关联数据
- 查询用户信息、等级信息
- 统计当前跟单账户资金、跟单人数、进行中订单数、粉丝数量
- 返回完整的专家信息

#### 6.1.3 合约-带单数据统计接口

**GET** `/api/copy/expert/contract/statistics`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable (0-全部, 7-7 天, 30-30 天, 90-90 天, 180-180 天)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "profit_rate": "15.50", // 收益率 %
    "total_profit": "1500.********", // 总收益
    "max_drawdown": "500.********", // 最大回撤
    "total_follower_count": 150, // 累计跟单人数
    "follower_profit": "12000.********", // 跟单者收益
    "trade_frequency": 25, // 交易频率
    "win_rate": "75.50", // 胜率 %
    "profit_order_count": 45, // 盈利笔数
    "loss_order_count": 15 // 亏损笔数
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 根据时间过滤条件查询专家的历史带单订单数据
- 计算收益率：(总收益 / 初始资金) \* 100%
- 计算最大回撤：统计期间内资金的最大回撤金额
- 统计累计跟单人数：历史所有跟单过该专家的用户数量
- 计算跟单者收益：所有跟单者的总收益金额
- 计算交易频率：交易总笔数 / 专家入驻天数
- 计算胜率：盈利笔数 / 总交易笔数 \* 100%
- 统计盈利笔数和亏损笔数
- 返回统计数据

#### 6.1.4 合约-综合数据接口

**GET** `/api/copy/expert/contract/overview`

**中间件**: TokenMiddleware

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "current_follower_count": 85, // 当前跟单人数
    "max_follower_count": 100, // 交易专家等级对应最大跟单人数
    "aum": "50000.********", // 资产管理规模（AUM）
    "total_assets": "2500.********", // 总资产（合约跟单账户资金）
    "last_trade_time": "2025-01-01 12:30:00", // 最近交易时间
    "profit_sharing_rate": "10.00", // 分润比例 %
    "fan_count": 200 // 粉丝数量
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 查询当前跟单人数：统计正在跟单该专家的用户数量
- 获取最大跟单人数：从专家等级配置中获取
- 计算资产管理规模（AUM）：所有跟单者的跟单账户资金总和
- 获取总资产：专家合约跟单账户当前资金
- 查询最近交易时间：最新一笔带单订单的创建时间
- 获取分润比例：专家当前设置的分润比例
- 统计粉丝数量：关注该专家的用户总数
- 返回综合数据

#### 6.1.5 现货-带单数据统计接口

**GET** `/api/copy/expert/spot/statistics`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable (0-全部, 7-7 天, 30-30 天, 90-90 天, 180-180 天)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "profit_rate": "12.30", // 收益率 %
    "total_profit": "800.********", // 总收益
    "total_follower_count": 120, // 累计跟单人数
    "follower_profit": "8000.********", // 跟单者收益
    "win_rate": "68.50", // 胜率 %
    "profit_order_count": 35, // 盈利笔数
    "loss_order_count": 16 // 亏损笔数
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 根据时间过滤条件查询专家的历史现货带单订单数据
- 计算收益率：(总收益 / 初始资金) \* 100%
- 计算总收益：所有已平仓订单的盈亏总和
- 统计累计跟单人数：历史所有跟单过该专家的用户数量
- 计算跟单者收益：所有跟单者的总收益金额
- 计算胜率：盈利笔数 / 总交易笔数 \* 100%
- 统计盈利笔数和亏损笔数
- 返回统计数据

#### 6.1.6 现货-综合数据接口

**GET** `/api/copy/expert/spot/overview`

**中间件**: TokenMiddleware

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "current_follower_count": 65, // 当前跟单人数
    "max_follower_count": 100, // 交易专家等级对应最大跟单人数
    "aum": "30000.********", // 资产管理规模（AUM）
    "total_assets": "1800.********", // 总资产（现货账户）
    "last_trade_time": "2025-01-01 14:20:00", // 最近交易时间
    "profit_sharing_rate": "8.00", // 分润比例 %
    "trade_frequency": 18, // 交易频率
    "fan_count": 180 // 粉丝数量
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 查询当前跟单人数：统计正在跟单该专家的用户数量
- 获取最大跟单人数：从专家等级配置中获取
- 计算资产管理规模（AUM）：所有跟单者的现货账户资金总和
- 获取总资产：专家现货账户当前资金（折合 USDT）
- 查询最近交易时间：最新一笔现货带单订单的创建时间
- 获取分润比例：专家当前设置的分润比例
- 计算交易频率：交易总笔数 / 专家入驻天数
- 统计粉丝数量：关注该专家的用户总数
- 返回综合数据

#### 6.1.7 合约-带单订单数据接口

**GET** `/api/copy/expert/contract/orders`

**中间件**: TokenMiddleware

**Query 参数**：

- `type`: required|string|in:current_detail,current_summary,history (当前带单-明细/当前带单-汇总/历史带单)
- `currency_id`: integer|nullable (币种 ID，为空表示全部币种)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例（当前带单-明细）：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "currency_symbol": "BTCUSDT", // 币种
        "margin_mode": "isolated", // 保证金模式
        "direction": "long", // 方向
        "leverage": 10, // 杠杆
        "open_time": "2025-01-01 10:00:00", // 开仓时间
        "avg_price": "45000.********", // 持仓均价
        "current_price": "46000.********", // 当前价
        "quantity": "0.10000000", // 数量
        "entry_price": "44800.********", // 订单入场价
        "margin": "450.********", // 保证金
        "unrealized_pnl": "120.********", // 未实现盈亏
        "order_id": "ORD123456789" // 订单编号
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 5,
      "total_pages": 1
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 根据 type 参数确定查询类型：
  - current_detail：查询当前持仓中的带单订单明细
  - current_summary：查询当前持仓中的带单订单汇总
  - history：查询历史已平仓的带单订单
- 根据 currency_id 过滤币种（为空则查询所有币种）
- 查询专家的合约订单数据，包含订单基本信息、价格信息、盈亏信息
- 计算未实现盈亏（仅当前持仓订单）
- 获取当前市场价格用于计算浮动盈亏
- 分页返回订单数据

#### 6.1.8 合约-撤销止盈止损接口

**DELETE** `/api/copy/expert/contract/position/{id}/stop-orders`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (仓位 ID)

**Body 参数**：

```json
{
  "type": 1 // 1-止盈, 2-止损, 3-止盈止损
}
```

**验证规则**:

- type: required|integer|in:1,2,3

响应示例：

```json
{
  "code": 200,
  "message": "撤销成功",
  "data": null
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 验证仓位 ID 是否存在且属于当前专家
- 根据 type 参数确定撤销类型：
  - 1：仅撤销止盈订单
  - 2：仅撤销止损订单
  - 3：同时撤销止盈止损订单
- 调用合约交易系统 API 撤销对应的止盈止损订单
- 记录操作日志
- 返回撤销结果

#### 6.1.9 现货-带单订单数据接口

**GET** `/api/copy/expert/spot/orders`

**中间件**: TokenMiddleware

**Query 参数**：

- `type`: required|string|in:current_detail,current_summary,history (当前带单-明细/当前带单-汇总/历史带单)
- `currency_id`: integer|nullable (币种 ID，为空表示全部币种)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例（当前带单-明细）：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "currency_pair": "BTCUSDT", // 币对
        "profit": "150.********", // 收益
        "buy_price": "44000.********", // 买入价
        "buy_time": "2025-01-01 10:00:00", // 买入时间
        "quantity": "0.10000000", // 数量
        "take_profit_price": "48000.********", // 止盈价
        "stop_loss_price": "42000.********" // 止损价
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 8,
      "total_pages": 1
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 根据 type 参数确定查询类型：
  - current_detail：查询当前持仓中的现货带单订单明细
  - current_summary：查询当前持仓中的现货带单订单汇总
  - history：查询历史已完成的现货带单订单
- 根据 currency_id 过滤币种（为空则查询所有币种）
- 查询专家的现货订单数据，包含订单基本信息、价格信息、盈亏信息
- 计算未实现盈亏（仅当前持仓订单）
- 获取当前市场价格用于计算浮动盈亏
- 分页返回现货订单数据

#### 6.1.10 现货-撤销止盈止损接口

**DELETE** `/api/copy/expert/spot/order/{id}/stop-orders`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (订单 ID)

**Body 参数**：

```json
{
  "type": 1 // 1-止盈, 2-止损, 3-止盈止损
}
```

**验证规则**:

- type: required|integer|in:1,2,3

响应示例：

```json
{
  "code": 200,
  "message": "撤销成功",
  "data": null
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 验证订单 ID 是否存在且属于当前专家
- 根据 type 参数确定撤销类型：
  - 1：仅撤销止盈订单
  - 2：仅撤销止损订单
  - 3：同时撤销止盈止损订单
- 调用现货交易系统 API 撤销对应的止盈止损订单
- 记录操作日志
- 返回撤销结果

#### 6.1.11 合约-分润数据统计接口

**GET** `/api/copy/expert/contract/profit-sharing/statistics`

**中间件**: TokenMiddleware

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_profit_sharing": "2500.********", // 累计已分润
    "pending_profit_sharing": "150.********", // 预计待分润
    "yesterday_profit_sharing": "85.********", // 昨日分润
    "current_profit_sharing_rate": "10.00" // 当前分润比例 %
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 查询专家的分润统计数据：
  - 计算累计已分润金额：所有已结算的分润总和
  - 计算预计待分润金额：当前未结算的预计分润
  - 计算昨日分润金额：昨天的分润收入
  - 获取当前分润比例设置
- 统计分润来源分析：
  - 按跟单者统计分润贡献
  - 按时间段统计分润趋势
  - 按币种统计分润分布
- 计算分润相关指标：
  - 平均日分润金额
  - 分润增长趋势
  - 分润稳定性指标
- 返回完整的分润统计数据

#### 6.1.12 合约-历史分润记录接口

**GET** `/api/copy/expert/contract/profit-sharing/history`

**中间件**: TokenMiddleware

**Query 参数**：

- `type`: required|string|in:by_time,by_follower (按时间/按跟随者)
- `days`: integer|nullable (天数过滤，仅 type=by_follower 时有效)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例（按时间）：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "date": "2025-01-01", // 时间
        "profit_sharing_amount": "125.********" // 分润数量
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 30,
      "total_pages": 2
    }
  }
}
```

响应示例（按跟随者）：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "follower_user": {
          "id": 100,
          "username": "follower001",
          "display_name": "跟单用户001",
          "avatar": "https://test.com/avatar.png"
        },
        "profit_sharing_amount": "85.********" // 分润数量
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 50,
      "total_pages": 3
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 根据 type 参数确定查询类型：
  - by_time：按时间维度查询分润记录
  - by_follower：按跟随者维度查询分润记录
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - by_follower 类型支持 days 过滤
- 查询分润历史记录：
  - by_time：按日期聚合分润数据，显示每日分润金额
  - by_follower：按跟随者聚合分润数据，显示每个跟随者的分润贡献
- 计算分润统计：
  - 分润金额、分润比例、分润来源
  - 跟随者信息和贡献度
- 分页返回分润历史记录

#### 6.1.13 现货-分润数据统计接口

**GET** `/api/copy/expert/spot/profit-sharing/statistics`

**中间件**: TokenMiddleware

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_profit_sharing": "1800.********", // 累计已分润
    "pending_profit_sharing": "95.********", // 预计待分润
    "yesterday_profit_sharing": "65.********", // 昨日分润
    "current_profit_sharing_rate": "8.00" // 当前分润比例 %
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 查询现货专家的分润统计数据：
  - 计算累计已分润金额：所有已结算的现货分润总和
  - 计算预计待分润金额：当前未结算的预计现货分润
  - 计算昨日分润金额：昨天的现货分润收入
  - 获取当前分润比例设置
- 统计现货分润来源分析：
  - 按跟单者统计现货分润贡献
  - 按时间段统计现货分润趋势
  - 按币种统计现货分润分布
- 计算现货分润相关指标：
  - 平均日分润金额
  - 分润增长趋势
  - 分润稳定性指标
- 返回完整的现货分润统计数据

#### 6.1.14 现货-历史分润记录接口

**GET** `/api/copy/expert/spot/profit-sharing/history`

**中间件**: TokenMiddleware

**Query 参数**：

- `type`: required|string|in:by_time,by_follower (按时间/按跟随者)
- `days`: integer|nullable (天数过滤，仅 type=by_follower 时有效)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例（按时间）：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "date": "2025-01-01", // 时间
        "profit_sharing_amount": "95.********" // 分润数量
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 25,
      "total_pages": 2
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 根据 type 参数确定查询类型：
  - by_time：按时间维度查询现货分润记录
  - by_follower：按跟随者维度查询现货分润记录
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - by_follower 类型支持 days 过滤
- 查询现货分润历史记录：
  - by_time：按日期聚合现货分润数据，显示每日分润金额
  - by_follower：按跟随者聚合现货分润数据，显示每个跟随者的分润贡献
- 计算现货分润统计：
  - 分润金额、分润比例、分润来源
  - 跟随者信息和贡献度
- 分页返回现货分润历史记录

#### 6.1.15 合约-我的跟随者接口

**GET** `/api/copy/expert/contract/followers`

**中间件**: TokenMiddleware

**Query 参数**：

- `asset_filter`: string|nullable|in:all,zero,low (资产过滤：all-所有跟单者，zero-资产为 0，low-资产低于 50USDT)
- `mode_filter`: string|nullable|in:all,smart,multi (跟单模式：all-全部，smart-智能比例，multi-多元探索)
- `exclusive_filter`: string|nullable|in:all,exclusive,normal (尊享模式：all-全部，exclusive-尊享模式，normal-普通模式)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "follower_user": {
          "id": 100,
          "username": "follower001",
          "display_name": "跟单用户001",
          "avatar": "https://test.com/avatar.png"
        },
        "copy_account_balance": "2500.********", // 合约跟单账户资金
        "total_copy_count": 25, // 累计跟单笔数
        "copy_profit": "350.********", // 跟单收益
        "copy_net_profit": "315.********", // 跟单净利润
        "profit_sharing_rate": "10.00", // 分润比例 %
        "mode": 1, // 跟单模式：1-智能比例，2-多元探索
        "is_exclusive": 0 // 是否尊享模式：1-是，0-否
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 85,
      "total_pages": 5
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析筛选条件：
  - asset_filter：按资产金额筛选跟随者
  - mode_filter：按跟单模式筛选（智能比例/多元探索）
  - exclusive_filter：按尊享模式筛选
- 查询专家的跟随者列表：
  - 查询正在跟单该专家的用户
  - 关联查询跟随者的用户信息
  - 获取跟随者的跟单配置信息
- 计算跟随者统计数据：
  - 合约跟单账户资金余额
  - 累计跟单笔数统计
  - 跟单收益和净利润计算
  - 分润比例（普通模式或尊享模式）
- 应用筛选条件：
  - 按资产金额过滤（全部/资产为 0/资产低于 50USDT）
  - 按跟单模式过滤
  - 按尊享模式状态过滤
- 分页返回跟随者列表数据

#### 6.1.16 合约-移除跟随者接口

**DELETE** `/api/copy/expert/contract/followers`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "ids": [100, 101, 102] // 跟随者用户ID数组
}
```

**验证规则**:

- ids: required|array|min:1
- ids.\*: required|integer|exists:cpx_user,id

响应示例：

```json
{
  "code": 200,
  "message": "移除成功",
  "data": {
    "removed_count": 3 // 成功移除的跟随者数量
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 验证参数有效性：
  - 检查用户 ID 数组是否为空
  - 验证每个用户 ID 是否存在
  - 确认这些用户是否为当前专家的跟随者
- 执行移除操作：
  - 停用跟随者的跟单配置
  - 平仓跟随者当前的跟单持仓
  - 清理跟单关系记录
  - 发送通知给被移除的跟随者
- 记录操作日志：
  - 记录移除操作的时间和原因
  - 记录被移除跟随者的信息
- 统计移除结果：
  - 计算成功移除的跟随者数量
  - 处理移除失败的情况
- 返回移除操作结果

#### 6.1.17 现货-我的跟随者接口

**GET** `/api/copy/expert/spot/followers`

**中间件**: TokenMiddleware

**Query 参数**：

- `asset_filter`: string|nullable|in:all,zero,low (资产过滤：all-所有跟单者，zero-资产为 0，low-资产低于 100USDT)
- `exclusive_filter`: string|nullable|in:all,exclusive,normal (尊享模式：all-全部，exclusive-尊享模式，normal-普通模式)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "follower_user": {
          "id": 100,
          "username": "follower001",
          "display_name": "跟单用户001",
          "avatar": "https://test.com/avatar.png"
        },
        "spot_account_balance": "1800.********", // 现货账户资金
        "total_copy_count": 18, // 累计跟单笔数
        "copy_profit": "220.********", // 跟单收益
        "copy_net_profit": "202.********", // 跟单净利润
        "profit_sharing_rate": "8.00", // 分润比例 %
        "is_exclusive": 0 // 是否尊享模式：1-是，0-否
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 65,
      "total_pages": 4
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 解析筛选条件：
  - asset_filter：按资产金额筛选跟随者
  - exclusive_filter：按尊享模式筛选
- 查询专家的现货跟随者列表：
  - 查询正在跟单该现货专家的用户
  - 关联查询跟随者的用户信息
  - 获取跟随者的现货跟单配置信息
- 计算跟随者统计数据：
  - 现货账户资金余额
  - 累计现货跟单笔数统计
  - 现货跟单收益和净利润计算
  - 分润比例（普通模式或尊享模式）
- 应用筛选条件：
  - 按资产金额过滤（全部/资产为 0/资产低于 100USDT）
  - 按尊享模式状态过滤
- 分页返回现货跟随者列表数据

#### 6.1.18 现货-移除跟随者接口

**DELETE** `/api/copy/expert/spot/followers`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "ids": [100, 101, 102] // 跟随者用户ID数组
}
```

**验证规则**:

- ids: required|array|min:1
- ids.\*: required|integer|exists:cpx_user,id

响应示例：

```json
{
  "code": 200,
  "message": "移除成功",
  "data": {
    "removed_count": 3 // 成功移除的跟随者数量
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 验证参数有效性：
  - 检查用户 ID 数组是否为空
  - 验证每个用户 ID 是否存在
  - 确认这些用户是否为当前现货专家的跟随者
- 执行移除操作：
  - 停用跟随者的现货跟单配置
  - 取消跟随者当前的现货跟单订单
  - 清理现货跟单关系记录
  - 发送通知给被移除的跟随者
- 记录操作日志：
  - 记录移除操作的时间和原因
  - 记录被移除跟随者的信息
- 统计移除结果：
  - 计算成功移除的跟随者数量
  - 处理移除失败的情况
- 返回移除操作结果

#### 6.1.19 合约-交易专家列表展示状态获取接口

**GET** `/api/copy/expert/contract/display-status`

**中间件**: TokenMiddleware

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "display_status": true, // 展示状态：true-可展示，false-不可展示
    "rules": {
      "is_active": {
        "status": true, // 是否满足
        "description": "开启合约带单",
        "current_value": "是"
      },
      "currency_count": {
        "status": true,
        "description": "至少设置1个带单币对",
        "current_value": "已设置",
        "required_value": 1,
        "actual_value": 3
      },
      "order_count": {
        "status": false,
        "description": "至少存在1笔已平仓带单订单",
        "current_value": "否",
        "required_value": 1,
        "actual_value": 0
      },
      "copy_account_assets": {
        "status": true,
        "description": "合约带单账户资金 >= 100 USDT",
        "current_value": "2500.********",
        "required_value": "100.********"
      }
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 检查专家列表展示状态的各项条件：
  - is_active：检查是否开启合约带单
  - currency_count：检查是否至少设置 1 个带单币对
  - order_count：检查是否至少存在 1 笔已平仓带单订单
  - copy_account_assets：检查合约带单账户资金是否 >= 100 USDT
- 计算每个条件的状态：
  - status：true 表示满足条件，false 表示不满足
  - description：条件描述
  - current_value：当前实际值
  - required_value：要求的最小值
  - actual_value：实际数值（如适用）
- 综合判断展示状态：
  - 所有条件都满足时，display_status 为 true
  - 任一条件不满足时，display_status 为 false
- 返回详细的状态检查结果

#### 6.1.20 合约-个人设置接口

**PUT** `/api/copy/expert/contract/settings`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "is_active": 1, // 是否开启合约带单：1-是，0-否
  "show_total_assets": 1, // 是否公共展示总资产：1-是，0-否
  "show_expert_rating": 1, // 是否展示专家评分及排名：1-是，0-否
  "introduction": "专业合约交易，稳健收益", // 带单介绍
  "position_protection": 1, // 未结仓位保护：1-开启，0-关闭
  "min_follow_amount": "100.********", // 最小跟单金额
  "recommend_params": {
    "fixed_amount": {
      "amount": "100.00",
      "stop_loss_rate": "10.00",
      "take_profit_rate": "20.00",
      "max_copy_amount": "1000.00"
    },
    "multiplier": {
      "multiplier": "0.1",
      "stop_loss_rate": "10.00",
      "take_profit_rate": "20.00",
      "max_copy_amount": "1000.00"
    }
  },
  "currency_ids": [1, 2, 3], // 币种ID数组
  "profit_sharing_rate": "10.00" // 分润比例 %
}
```

**验证规则**:

- is_active: required|integer|in:0,1
- show_total_assets: required|integer|in:0,1
- show_expert_rating: required|integer|in:0,1
- introduction: required|string|max:500
- position_protection: required|integer|in:0,1
- min_follow_amount: required|numeric|min:0
- recommend_params: nullable|array
- currency_ids: required|array|min:1
- currency_ids.\*: required|integer|exists:currency,id
- profit_sharing_rate: required|numeric|min:0|max:100

响应示例：

```json
{
  "code": 200,
  "message": "设置成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "is_active": 1,
    "show_total_assets": 1,
    "show_expert_rating": 1,
    "introduction": "专业合约交易，稳健收益",
    "position_protection": 1,
    "min_follow_amount": "100.********",
    "recommend_params": {
      "fixed_amount": {
        "amount": "100.00",
        "stop_loss_rate": "10.00",
        "take_profit_rate": "20.00",
        "max_copy_amount": "1000.00"
      },
      "multiplier": {
        "multiplier": "0.1",
        "stop_loss_rate": "10.00",
        "take_profit_rate": "20.00",
        "max_copy_amount": "1000.00"
      }
    },
    "currency_ids": [1, 2, 3],
    "profit_sharing_rate": "10.00",
    "updated_at": "2025-01-01 15:30:00"
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 验证参数有效性：
  - 检查分润比例是否在允许范围内
  - 验证最小跟单金额是否符合系统要求
  - 检查币种 ID 是否有效且支持合约交易
  - 验证推荐参数的合理性
- 检查业务规则：
  - 验证专家等级是否支持设置的最大分润比例
  - 检查是否至少选择一个带单币种
  - 验证推荐参数中的止损止盈比例合理性
- 更新专家设置：
  - 更新基本设置（开启状态、展示设置等）
  - 更新带单介绍和风控设置
  - 更新推荐参数和币种配置
  - 更新分润比例
- 记录设置变更日志
- 返回更新后的设置信息

#### 6.1.21 合约-问题反馈/身份撤销接口

**POST** `/api/copy/expert/contract/feedback`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "feedback_type": 1, // 1-问题反馈, 2-身份撤销
  "problem_type": "技术问题", // 问题类型
  "content": "遇到了技术问题，需要帮助", // 问题描述
  "refund_account_type": 1 // 身份撤销时必填，资金退回账户类型：1-现货账户，2-合约账户
}
```

**验证规则**:

- feedback_type: required|integer|in:1,2
- problem_type: required|string|max:50
- content: required|string|max:1000
- refund_account_type: required_if:feedback_type,2|integer|in:1,2

响应示例：

```json
{
  "code": 200,
  "message": "提交成功",
  "data": {
    "id": 1,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "expert_user_id": 1,
    "type": 1,
    "feedback_type": 1,
    "problem_type": "技术问题",
    "content": "遇到了技术问题，需要帮助",
    "refund_account_type": null,
    "refund_amount": null,
    "created_at": "2025-01-01 16:00:00"
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 根据 feedback_type 确定处理类型：
  - 问题反馈（type=1）：记录用户反馈的问题
  - 身份撤销（type=2）：处理专家身份撤销申请
- 验证参数完整性：
  - 检查问题类型和内容的有效性
  - 身份撤销时验证退款账户类型
- 处理身份撤销逻辑：
  - 计算需要退还的资金金额
  - 验证退款账户的有效性
  - 停用专家身份和相关功能
- 创建反馈记录：
  - 记录反馈类型、问题描述、处理状态
  - 关联专家信息和用户信息
- 发送通知给管理员处理
- 返回反馈提交结果

#### 6.1.22 现货-交易专家列表展示状态获取接口

**GET** `/api/copy/expert/spot/display-status`

**中间件**: TokenMiddleware

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "display_status": true, // 展示状态：true-可展示，false-不可展示
    "rules": {
      "is_active": {
        "status": true, // 是否满足
        "description": "开启现货带单",
        "current_value": "是"
      },
      "currency_count": {
        "status": true,
        "description": "至少设置1个带单币对",
        "current_value": "已设置",
        "required_value": 1,
        "actual_value": 5
      },
      "order_count": {
        "status": true,
        "description": "至少存在1笔已平仓带单订单",
        "current_value": "是",
        "required_value": 1,
        "actual_value": 8
      },
      "spot_account_assets": {
        "status": true,
        "description": "现货账户资金 >= 100 USDT",
        "current_value": "1800.********",
        "required_value": "100.********"
      }
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 检查现货专家列表展示状态的各项条件：
  - is_active：检查是否开启现货带单
  - currency_count：检查是否至少设置 1 个带单币对
  - order_count：检查是否至少存在 1 笔已平仓带单订单
  - spot_account_assets：检查现货账户资金是否 >= 100 USDT
- 计算每个条件的状态：
  - status：true 表示满足条件，false 表示不满足
  - description：条件描述
  - current_value：当前实际值
  - required_value：要求的最小值
  - actual_value：实际数值（如适用）
- 综合判断展示状态：
  - 所有条件都满足时，display_status 为 true
  - 任一条件不满足时，display_status 为 false
- 返回详细的状态检查结果

### 6.2 用户端接口

#### 6.2.1 合约-交易专家列表接口

**GET** `/api/copy/user/contract/experts`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤：0-全部，7-7 天，30-30 天，90-90 天，180-180 天)
- `profit_min`: numeric|nullable (收益最小值，与 days 相关)
- `profit_rate_min`: numeric|nullable (收益率最小值 %，与 days 相关)
- `profit_rate_max`: numeric|nullable (收益率最大值 %，与 days 相关)
- `win_rate_min`: numeric|nullable (胜率最小值 %，与 days 相关)
- `trade_frequency_min`: integer|nullable (交易频率最小值，与 days 相关)
- `follower_profit_min`: numeric|nullable (跟单者收益最小值，与 days 相关)
- `entry_days_min`: integer|nullable (入驻天数最小值)
- `aum_min`: numeric|nullable (资产管理规模最小值)
- `max_drawdown_min`: numeric|nullable (最大回撤最小值，与 days 相关)
- `level_ids`: array|nullable (专家等级 ID 数组)
- `currency_ids`: array|nullable (跟单币种 ID 数组)
- `hide_full`: boolean|nullable (隐藏满员专家)
- `assets_public`: boolean|nullable (仅显示资产公开的专家)
- `is_followed`: boolean|nullable (仅显示已关注的专家)
- `sort_by`: string|nullable|in:profit,win_rate,profit_rate,follower_profit,max_drawdown,aum (排序字段)
- `sort_order`: string|nullable|in:asc,desc (排序方向)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "user": {
          "id": 1,
          "username": "expert001",
          "display_name": "专业交易员",
          "avatar": "https://test.com/avatar.png"
        },
        "level": {
          "id": 1,
          "level": 1,
          "name": "初级专家",
          "icon": "https://test.com/icon.png"
        },
        "current_follower_count": 85, // 当前跟单人数
        "max_follower_count": 100, // 最大跟单人数
        "statistics": {
          "profit_rate": "15.50", // 收益率 %（与days相关）
          "total_profit": "1500.********", // 总收益（与days相关）
          "win_rate": "75.50", // 胜率 %（与days相关）
          "trade_frequency": 25, // 交易频率（与days相关）
          "follower_profit": "12000.********", // 跟单者收益（与days相关）
          "max_drawdown": "500.********", // 最大回撤（与days相关）
          "aum": "50000.********" // 资产管理规模
        },
        "profit_rate_chart": [
          // 收益率统计折线图（与days相关）
          {
            "date": "2025-01-01",
            "profit_rate": "2.5"
          },
          {
            "date": "2025-01-02",
            "profit_rate": "3.2"
          }
        ],
        "is_followed": true, // 是否已关注
        "show_total_assets": 1, // 是否展示总资产
        "profit_sharing_rate": "10.00" // 分润比例 %
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 150,
      "total_pages": 8
    }
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 根据筛选条件构建查询：
  - 时间过滤：根据 days 参数过滤专家数据统计时间范围
  - 收益过滤：根据 profit_min、profit_rate_min/max 等参数过滤
  - 胜率过滤：根据 win_rate_min 参数过滤
  - 其他条件：交易频率、跟单者收益、入驻天数、AUM 等
- 查询符合条件的合约交易专家列表
- 获取专家基本信息：用户信息、等级信息
- 计算专家统计数据：收益率、总收益、胜率、跟单者收益、AUM 等
- 生成收益率统计折线图数据
- 检查当前用户是否已关注该专家
- 根据排序条件对结果进行排序
- 分页返回专家列表数据

#### 6.2.2 合约-交易专家收益率统计图接口

**GET** `/api/copy/user/contract/expert/{id}/profit-rate-chart`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "date": "2025-01-01", // 日期
        "profit_rate": "2.50" // 收益率 %
      },
      {
        "date": "2025-01-02",
        "profit_rate": "3.20"
      },
      {
        "date": "2025-01-03",
        "profit_rate": "1.80"
      }
    ]
  }
}
```

#### 6.2.3 合约-交易专家总收益统计图接口

**GET** `/api/copy/user/contract/expert/{id}/total-profit-chart`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "date": "2025-01-01", // 日期
        "total_profit": "100.********" // 总收益
      },
      {
        "date": "2025-01-02",
        "total_profit": "150.80000000"
      },
      {
        "date": "2025-01-03",
        "total_profit": "125.30000000"
      }
    ]
  }
}
```

#### 6.1.23 现货-个人设置接口

**PUT** `/api/copy/expert/spot/settings`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "is_active": 1, // 是否开启现货带单：1-是，0-否
  "show_total_assets": 1, // 是否公共展示总资产：1-是，0-否
  "show_fund_composition": 1, // 是否公开展示现货账户资金构成比例：1-是，0-否
  "introduction": "专业现货交易，稳健收益", // 带单介绍
  "new_currency_auto_copy": 1, // 新上线的交易对自动开启带单：1-是，0-否
  "position_protection": 1, // 未结仓位保护：1-开启，0-关闭
  "min_follow_amount": "100.********", // 最小跟单金额
  "recommend_params": {
    "fixed_amount": {
      "amount": "100.00",
      "stop_loss_rate": "10.00",
      "take_profit_rate": "20.00",
      "max_copy_amount": "1000.00"
    },
    "multiplier": {
      "multiplier": "0.1",
      "stop_loss_rate": "10.00",
      "take_profit_rate": "20.00",
      "max_copy_amount": "1000.00"
    }
  },
  "currency_ids": [1, 2, 3], // 币种ID数组
  "profit_sharing_rate": "8.00" // 分润比例 %
}
```

**验证规则**:

- is_active: required|integer|in:0,1
- show_total_assets: required|integer|in:0,1
- show_fund_composition: required|integer|in:0,1
- introduction: required|string|max:500
- new_currency_auto_copy: required|integer|in:0,1
- position_protection: required|integer|in:0,1
- min_follow_amount: required|numeric|min:0
- recommend_params: nullable|array
- currency_ids: required|array|min:1
- currency_ids.\*: required|integer|exists:currency,id
- profit_sharing_rate: required|numeric|min:0|max:100

响应示例：

```json
{
  "code": 200,
  "message": "设置成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "is_active": 1,
    "show_total_assets": 1,
    "show_fund_composition": 1,
    "introduction": "专业现货交易，稳健收益",
    "new_currency_auto_copy": 1,
    "position_protection": 1,
    "min_follow_amount": "100.********",
    "recommend_params": {
      "fixed_amount": {
        "amount": "100.00",
        "stop_loss_rate": "10.00",
        "take_profit_rate": "20.00",
        "max_copy_amount": "1000.00"
      },
      "multiplier": {
        "multiplier": "0.1",
        "stop_loss_rate": "10.00",
        "take_profit_rate": "20.00",
        "max_copy_amount": "1000.00"
      }
    },
    "currency_ids": [1, 2, 3],
    "profit_sharing_rate": "8.00",
    "updated_at": "2025-01-01 15:30:00"
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 验证参数有效性：
  - 检查分润比例是否在允许范围内
  - 验证最小跟单金额是否符合系统要求
  - 检查币种 ID 是否有效且支持现货交易
  - 验证推荐参数的合理性
- 检查业务规则：
  - 验证专家等级是否支持设置的最大分润比例
  - 检查是否至少选择一个带单币种
  - 验证推荐参数中的止损止盈比例合理性
  - 检查新上线币种自动开启设置的合理性
- 更新专家设置：
  - 更新基本设置（开启状态、展示设置、资金构成展示等）
  - 更新带单介绍和风控设置
  - 更新推荐参数和币种配置
  - 更新分润比例和自动开启设置
- 记录设置变更日志
- 返回更新后的设置信息

#### 6.1.24 现货-问题反馈/身份撤销接口

**POST** `/api/copy/expert/spot/feedback`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "feedback_type": 1, // 1-问题反馈, 2-身份撤销
  "problem_type": "技术问题", // 问题类型
  "content": "遇到了技术问题，需要帮助" // 问题描述
}
```

**验证规则**:

- feedback_type: required|integer|in:1,2
- problem_type: required|string|max:50
- content: required|string|max:1000

响应示例：

```json
{
  "code": 200,
  "message": "提交成功",
  "data": {
    "id": 1,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopySpotExpert",
    "expert_user_id": 1,
    "type": 2,
    "feedback_type": 1,
    "problem_type": "技术问题",
    "content": "遇到了技术问题，需要帮助",
    "refund_account_type": null,
    "refund_amount": null,
    "created_at": "2025-01-01 16:00:00"
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 根据 feedback_type 确定处理类型：
  - 问题反馈（type=1）：记录用户反馈的问题
  - 身份撤销（type=2）：处理专家身份撤销申请
- 验证参数完整性：
  - 检查问题类型和内容的有效性
  - 现货专家身份撤销无需资金退还
- 处理身份撤销逻辑：
  - 停用现货专家身份和相关功能
  - 清理专家相关配置和关联关系
  - 通知所有跟单者停止跟单
- 创建反馈记录：
  - 记录反馈类型、问题描述、处理状态
  - 关联现货专家信息和用户信息
- 发送通知给管理员处理
- 返回反馈提交结果

#### 6.1.25 合约-合约跟单账号余额历史接口

**GET** `/api/copy/expert/contract/balance-history`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "type": "transfer_in", // 类型：transfer_in-转入，transfer_out-转出，profit-收益，loss-亏损，fee-手续费
        "amount": "500.********", // 金额
        "direction": "in", // 流向：in-流入，out-流出
        "balance_after": "2500.********", // 操作后余额
        "description": "从现货账户转入", // 描述
        "created_at": "2025-01-01 10:00:00" // 时间
      },
      {
        "id": 2,
        "type": "profit",
        "amount": "150.********",
        "direction": "in",
        "balance_after": "2650.********",
        "description": "交易盈利",
        "created_at": "2025-01-01 14:30:00"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 45,
      "total_pages": 3
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析时间过滤条件：
  - 根据 days 参数确定查询时间范围
  - 支持自定义开始和结束日期
- 查询合约跟单账户余额变动记录：
  - 按时间倒序排列
  - 包含转入、转出、盈利、亏损、手续费等类型
- 计算余额变动详情：
  - 记录每次变动的金额和方向
  - 计算变动后的账户余额
  - 生成变动描述信息
- 分类统计余额变动：
  - 按变动类型进行分类统计
  - 计算各类型的总金额
- 分页返回余额历史记录
- 提供余额变动趋势分析数据

#### 6.1.26 合约-带单币对接口

**GET** `/api/copy/expert/contract/currencies`

**中间件**: TokenMiddleware

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "symbol": "BTCUSDT", // 交易对symbol
        "base_currency": "BTC",
        "quote_currency": "USDT",
        "is_enabled": true // 是否启用带单
      },
      {
        "id": 2,
        "symbol": "ETHUSDT",
        "base_currency": "ETH",
        "quote_currency": "USDT",
        "is_enabled": true
      },
      {
        "id": 3,
        "symbol": "ADAUSDT",
        "base_currency": "ADA",
        "quote_currency": "USDT",
        "is_enabled": false
      }
    ]
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 查询系统支持的合约交易币种列表
- 获取专家当前的带单币种配置：
  - 查询专家已启用的带单币种
  - 获取币种的启用/禁用状态
- 组装币种信息：
  - 包含币种基本信息（symbol、base_currency、quote_currency）
  - 标记每个币种的启用状态
  - 按币种名称排序
- 返回完整的币种列表和状态信息
- 支持专家查看和管理带单币种配置

#### 6.1.27 现货-带单币对接口

**GET** `/api/copy/expert/spot/currencies`

**中间件**: TokenMiddleware

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "symbol": "BTCUSDT", // 交易对symbol
        "base_currency": "BTC",
        "quote_currency": "USDT",
        "is_enabled": true // 是否启用带单
      },
      {
        "id": 2,
        "symbol": "ETHUSDT",
        "base_currency": "ETH",
        "quote_currency": "USDT",
        "is_enabled": true
      },
      {
        "id": 3,
        "symbol": "ADAUSDT",
        "base_currency": "ADA",
        "quote_currency": "USDT",
        "is_enabled": false
      }
    ]
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 查询系统支持的现货交易币种列表
- 获取专家当前的带单币种配置：
  - 查询专家已启用的现货带单币种
  - 获取币种的启用/禁用状态
- 组装币种信息：
  - 包含币种基本信息（symbol、base_currency、quote_currency）
  - 标记每个币种的启用状态
  - 按币种名称排序
- 返回完整的币种列表和状态信息
- 支持专家查看和管理现货带单币种配置

#### 6.1.28 合约-收益率统计图接口

**GET** `/api/copy/expert/contract/profit-rate-chart`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "date": "2025-01-01", // 日期
        "profit_rate": "2.50" // 收益率 %
      },
      {
        "date": "2025-01-02",
        "profit_rate": "3.20"
      },
      {
        "date": "2025-01-03",
        "profit_rate": "1.80"
      }
    ]
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - 默认查询最近 30 天的数据
- 查询专家的历史交易数据：
  - 按日期聚合已平仓订单的盈亏数据
  - 计算每日的累计收益率
- 计算收益率统计：
  - 以专家初始资金为基准计算收益率
  - 计算累计收益率和日收益率
  - 处理数据缺失的日期（补零或插值）
- 生成图表数据：
  - 按日期排序生成时间序列数据
  - 格式化收益率为百分比形式
  - 提供平滑的收益率曲线数据
- 返回图表所需的数据格式

#### 6.1.29 合约-总收益统计图接口

**GET** `/api/copy/expert/contract/total-profit-chart`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "date": "2025-01-01", // 日期
        "total_profit": "100.********" // 总收益
      },
      {
        "date": "2025-01-02",
        "total_profit": "150.80000000"
      },
      {
        "date": "2025-01-03",
        "total_profit": "125.30000000"
      }
    ]
  }
}
```

#### 6.1.30 合约-币种偏好统计接口（饼图）

**GET** `/api/copy/expert/contract/currency-preference`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "currency_symbol": "BTCUSDT",
        "percentage": "45.5", // 占比 %
        "trade_count": 25, // 交易次数
        "total_volume": "50000.********" // 总交易量
      },
      {
        "currency_symbol": "ETHUSDT",
        "percentage": "35.2",
        "trade_count": 18,
        "total_volume": "38000.********"
      },
      {
        "currency_symbol": "ADAUSDT",
        "percentage": "19.3",
        "trade_count": 12,
        "total_volume": "20000.********"
      }
    ]
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - 默认查询最近 30 天的数据
- 查询专家的历史合约交易数据：
  - 统计各币种的交易次数和交易量
  - 计算各币种的交易占比
- 计算币种偏好统计：
  - 按交易量计算各币种的百分比占比
  - 统计各币种的交易笔数
  - 计算各币种的总交易量
- 生成饼图数据：
  - 按占比从高到低排序
  - 格式化为饼图所需的数据格式
  - 包含币种名称、占比、交易次数、交易量
- 返回币种偏好饼图数据

#### 6.1.31 合约-持仓时间统计接口

**GET** `/api/copy/expert/contract/position-time-stats`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "average_hold_time": "4.5", // 平均持仓时间（小时）
    "max_hold_time": "24.8", // 最长持仓时间（小时）
    "chart_data": [
      {
        "time_range": "0-5小时", // 持仓时段
        "profit_count": 15, // 盈利笔数
        "loss_count": 3, // 亏损笔数
        "avg_profit": "125.********", // 平均盈利
        "avg_loss": "-45.20000000" // 平均亏损
      },
      {
        "time_range": "5-10小时",
        "profit_count": 12,
        "loss_count": 5,
        "avg_profit": "180.30000000",
        "avg_loss": "-65.80000000"
      },
      {
        "time_range": "10-15小时",
        "profit_count": 8,
        "loss_count": 2,
        "avg_profit": "220.10000000",
        "avg_loss": "-85.********"
      },
      {
        "time_range": "15-20小时",
        "profit_count": 5,
        "loss_count": 1,
        "avg_profit": "350.********",
        "avg_loss": "-120.********"
      },
      {
        "time_range": "20小时以上",
        "profit_count": 3,
        "loss_count": 0,
        "avg_profit": "500.********",
        "avg_loss": "0.********"
      }
    ]
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - 默认查询最近 30 天的数据
- 查询专家的历史合约持仓数据：
  - 统计各持仓时间段的交易记录
  - 计算持仓时长分布
- 按持仓时间分组统计：
  - 0-1 小时、1-5 小时、5-10 小时、10-15 小时、15-20 小时、20 小时以上
  - 统计各时间段的盈利笔数和亏损笔数
  - 计算各时间段的平均盈利和平均亏损
- 生成持仓时间分析数据：
  - 按时间段排序
  - 包含时间范围、盈亏统计、平均盈亏金额
- 返回持仓时间统计柱状图数据

#### 6.1.32 合约-交易专家交易分析精灵统计接口（雷达图）

**GET** `/api/copy/expert/contract/expert-analysis-radar`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "expert_score": "85.5", // 专家评分
    "expert_ranking": 15, // 专家排名
    "total_experts": 500, // 总专家数
    "radar_data": {
      "indicators": [
        {
          "name": "盈利因子",
          "max": 5,
          "description": "总盈利与总亏损比例"
        },
        {
          "name": "胜率",
          "max": 100,
          "description": "胜率百分比"
        },
        {
          "name": "平均盈亏比",
          "max": 5,
          "description": "平均盈利与平均亏损比例"
        }
      ],
      "series": [
        {
          "name": "专家平均能力参考值",
          "data": [2.5, 65.0, 1.8] // 对应indicators的值
        },
        {
          "name": "当前专家参数",
          "data": [3.2, 75.5, 2.3]
        }
      ]
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - 默认查询最近 30 天的数据
- 查询专家的交易数据并计算各项指标：
  - 专家评分：综合收益率、胜率、回撤等指标计算
  - 专家排名：在同等级专家中的排名
- 生成雷达图数据：
  - 定义雷达图指标（收益率、胜率、风控能力等）
  - 计算各指标的数值和满分值
  - 生成当前专家的雷达图数据
- 提供对比数据：
  - 同等级专家平均水平
  - 全平台专家平均水平
- 返回雷达图所需的完整数据格式

#### 6.1.33 合约-跟单者交易分析精灵统计接口（雷达图）

**GET** `/api/copy/expert/contract/follower-analysis-radar`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "radar_data": {
      "indicators": [
        {
          "name": "盈利因子",
          "max": 5,
          "description": "总盈利与总亏损比例"
        },
        {
          "name": "胜率",
          "max": 100,
          "description": "胜率百分比"
        },
        {
          "name": "平均盈亏比",
          "max": 5,
          "description": "平均盈利与平均亏损比例"
        }
      ],
      "series": [
        {
          "name": "跟单专家参数",
          "data": [3.2, 75.5, 2.3] // 专家的数据
        },
        {
          "name": "跟单者参数",
          "data": [2.8, 68.2, 2.0] // 跟单者的数据
        }
      ]
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - 默认查询最近 30 天的数据
- 查询跟单者的交易数据并计算各项指标：
  - 跟单者评分：基于跟单收益、风控能力等计算
  - 跟单者排名：在该专家跟单者中的排名
- 生成跟单者雷达图数据：
  - 定义雷达图指标（跟单收益率、风控能力、跟单频率等）
  - 计算各指标的数值和满分值
  - 生成跟单者的雷达图数据
- 提供对比数据：
  - 专家自身的交易数据作为对比基准
  - 其他跟单者的平均水平
- 返回跟单者雷达图所需的完整数据格式

#### 6.1.34 合约-交易指标接口

**GET** `/api/copy/expert/contract/trading-indicators`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "last_trade_time": "2025-01-01 14:30:00", // 最近交易时间
    "trade_count": 45, // 交易次数
    "daily_avg_trade_count": "3.2", // 日均交易次数
    "total_profit": "2500.********", // 总盈利
    "max_profit": "500.********", // 最大盈利
    "max_loss": "-200.********", // 最大亏损
    "max_drawdown": "350.********", // 最大回撤
    "long_short_ratio": "1.8", // 多空比（多头交易数/空头交易数）
    "long_trade_count": 28, // 多头交易次数
    "short_trade_count": 17, // 空头交易次数
    "avg_profit_per_trade": "55.56000000", // 平均每笔盈利
    "profit_factor": "3.2" // 盈利因子
  }
}
```

#### 6.1.35 合约-币对交易表现接口

**GET** `/api/copy/expert/contract/currency-performance`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "currency_symbol": "BTCUSDT", // 币对
        "trade_count": 25, // 交易次数
        "profit_count": 18, // 盈利次数
        "loss_count": 7, // 亏损次数
        "win_rate": "72.00", // 胜率 %
        "total_profit": "1500.********", // 总盈利
        "total_loss": "-300.********", // 总亏损
        "net_profit": "1200.********", // 净盈利
        "profit_rate": "15.50", // 收益率 %
        "avg_profit": "83.33000000", // 平均盈利
        "avg_loss": "-42.86000000", // 平均亏损
        "max_profit": "350.********", // 最大盈利
        "max_loss": "-120.********", // 最大亏损
        "profit_factor": "5.00" // 盈利因子
      },
      {
        "currency_symbol": "ETHUSDT",
        "trade_count": 18,
        "profit_count": 12,
        "loss_count": 6,
        "win_rate": "66.67",
        "total_profit": "800.********",
        "total_loss": "-200.********",
        "net_profit": "600.********",
        "profit_rate": "12.30",
        "avg_profit": "66.67000000",
        "avg_loss": "-33.33000000",
        "max_profit": "200.********",
        "max_loss": "-80.********",
        "profit_factor": "4.00"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 8,
      "total_pages": 1
    }
  }
}
```

#### 6.1.36 合约-交易日历接口

**GET** `/api/copy/expert/contract/trading-calendar`

**中间件**: TokenMiddleware

**Query 参数**：

- `year`: integer|required (年份)
- `month`: integer|required|min:1|max:12 (月份)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "calendar_data": [
      {
        "date": "2025-01-01", // 日期
        "trade_count": 3, // 交易次数
        "profit_count": 2, // 盈利次数
        "loss_count": 1, // 亏损次数
        "net_profit": "150.********", // 净盈利
        "profit_rate": "2.50" // 收益率 %
      },
      {
        "date": "2025-01-02",
        "trade_count": 2,
        "profit_count": 1,
        "loss_count": 1,
        "net_profit": "50.********",
        "profit_rate": "0.80"
      },
      {
        "date": "2025-01-03",
        "trade_count": 0,
        "profit_count": 0,
        "loss_count": 0,
        "net_profit": "0.********",
        "profit_rate": "0.00"
      }
    ]
  }
}
```

#### 6.1.37 现货-收益率统计图接口

**GET** `/api/copy/expert/spot/profit-rate-chart`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "date": "2025-01-01", // 日期
        "profit_rate": "1.80" // 收益率 %
      },
      {
        "date": "2025-01-02",
        "profit_rate": "2.50"
      },
      {
        "date": "2025-01-03",
        "profit_rate": "1.20"
      }
    ]
  }
}
```

#### 6.1.38 现货-总收益统计图接口

**GET** `/api/copy/expert/spot/total-profit-chart`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "date": "2025-01-01", // 日期
        "total_profit": "80.********" // 总收益
      },
      {
        "date": "2025-01-02",
        "total_profit": "120.80000000"
      },
      {
        "date": "2025-01-03",
        "total_profit": "95.30000000"
      }
    ]
  }
}
```

#### 6.1.39 现货-币种偏好统计接口（饼图）

**GET** `/api/copy/expert/spot/currency-preference`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "currency_symbol": "BTCUSDT",
        "percentage": "50.0", // 占比 %
        "trade_count": 20, // 交易次数
        "total_volume": "40000.********" // 总交易量
      },
      {
        "currency_symbol": "ETHUSDT",
        "percentage": "50.0",
        "trade_count": 15,
        "total_volume": "30000.********"
      }
    ]
  }
}
```

#### 6.1.40 现货-持仓时间统计接口（散点图）

**GET** `/api/copy/expert/spot/position-time-stats`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "hold_time": "2.5", // 持仓时间（小时）
        "profit": "150.********" // 盈亏
      },
      {
        "hold_time": "4.8",
        "profit": "-80.********"
      },
      {
        "hold_time": "1.2",
        "profit": "220.********"
      }
    ]
  }
}
```

#### 6.1.41 现货-交易量统计接口

**GET** `/api/copy/expert/spot/trading-volume-stats`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "date": "2025-01-01", // 日期时间
        "trading_volume": "25000.********" // 交易量（折合USDT）
      },
      {
        "date": "2025-01-02",
        "trading_volume": "18000.********"
      },
      {
        "date": "2025-01-03",
        "trading_volume": "32000.********"
      }
    ]
  }
}
```

#### 6.1.42 现货-资产构成统计接口

**GET** `/api/copy/expert/spot/asset-composition`

**中间件**: TokenMiddleware

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "currency": "USDT",
        "percentage": "50.0", // 占比 %
        "amount": "900.********" // 数量
      },
      {
        "currency": "BTC",
        "percentage": "30.0",
        "amount": "0.02000000"
      },
      {
        "currency": "ETH",
        "percentage": "20.0",
        "amount": "0.15000000"
      }
    ]
  }
}
```

#### 6.1.43 现货-币对交易表现接口

**GET** `/api/copy/expert/spot/currency-performance`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "currency_symbol": "BTCUSDT", // 币对
        "profit_order_count": 15, // 盈利订单数
        "loss_order_count": 5, // 亏损订单数
        "win_rate": "75.00", // 胜率 %
        "total_profit": "800.********", // 总盈利
        "total_loss": "-200.********", // 总亏损
        "net_profit": "600.********" // 净盈利
      },
      {
        "currency_symbol": "ETHUSDT",
        "profit_order_count": 12,
        "loss_order_count": 3,
        "win_rate": "80.00",
        "total_profit": "600.********",
        "total_loss": "-100.********",
        "net_profit": "500.********"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 6,
      "total_pages": 1
    }
  }
}
```

#### 6.1.44 现货-尊享模式开启/关闭接口

**PUT** `/api/copy/expert/spot/exclusive-mode`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "exclusive_mode": 1 // 尊享模式：1-开启，0-关闭
}
```

**验证规则**:

- exclusive_mode: required|integer|in:0,1

响应示例：

```json
{
  "code": 200,
  "message": "设置成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "exclusive_mode": 1,
    "updated_at": "2025-01-01 16:00:00"
  }
}
```

#### 6.1.45 交易专家等级接口

**GET** `/api/copy/expert/levels`

**中间件**: TokenMiddleware

**Query 参数**：

- `type`: integer|nullable|in:1,2 (专家类型：1-合约专家，2-现货专家)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "current_level": {
      "id": 2,
      "type": 1,
      "level": 2,
      "name": "中级专家",
      "icon": "https://test.com/icon2.png"
    },
    "all_levels": [
      {
        "id": 1,
        "type": 1,
        "level": 1,
        "name": "初级专家",
        "icon": "https://test.com/icon1.png",
        "condition_amount": "1000.********",
        "condition_follow_amount": "5000.********",
        "condition_follow_count": 10,
        "max_follow_count": 100,
        "max_profit_rate": "10.00"
      },
      {
        "id": 2,
        "type": 1,
        "level": 2,
        "name": "中级专家",
        "icon": "https://test.com/icon2.png",
        "condition_amount": "5000.********",
        "condition_follow_amount": "20000.********",
        "condition_follow_count": 50,
        "max_follow_count": 200,
        "max_profit_rate": "15.00"
      }
    ]
  }
}
```

**接口逻辑**：

- 验证当前用户是否为交易专家（合约或现货）
- 根据 type 参数过滤专家类型：
  - 如果指定 type，则只返回对应类型的等级
  - 如果不指定 type，则返回所有类型的等级
- 查询当前专家的等级信息：
  - 获取专家当前等级 ID
  - 查询当前等级的详细信息
- 查询所有可用等级列表：
  - 按等级从低到高排序
  - 包含等级条件、权益、限制等信息
- 计算专家升级条件：
  - 检查当前专家是否满足更高等级的条件
  - 计算距离下一等级的差距
- 返回等级信息：
  - 当前等级详情
  - 所有等级列表
  - 升级条件和进度

#### 6.1.38 合约-尊享模式开启/关闭接口

**PUT** `/api/copy/expert/contract/exclusive-mode`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "exclusive_mode": 1 // 尊享模式：1-开启，0-关闭
}
```

**验证规则**:

- exclusive_mode: required|integer|in:0,1

响应示例：

```json
{
  "code": 200,
  "message": "设置成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "exclusive_mode": 1,
    "updated_at": "2025-01-01 16:00:00"
  }
}
```

#### 6.1.39 合约-尊享模式邀请链接创建接口

**POST** `/api/copy/expert/contract/exclusive-invites`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "name": "VIP客户邀请", // 邀请链接名称
  "profit_sharing_rate": "15.00", // 分润比例 %
  "max_use_count": 10, // 最大使用次数，null表示无限制
  "expire_at": "2025-12-31 23:59:59" // 过期时间，null表示永不过期
}
```

**验证规则**:

- name: required|string|max:100
- profit_sharing_rate: required|numeric|min:0|max:100
- max_use_count: nullable|integer|min:1
- expire_at: nullable|date_format:Y-m-d H:i:s

响应示例：

```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "name": "VIP客户邀请",
    "invite_code": "INV123456789", // 邀请码
    "invite_url": "https://example.com/copy/invite/INV123456789", // 邀请链接
    "profit_sharing_rate": "15.00",
    "max_use_count": 10,
    "used_count": 0,
    "expire_at": "2025-12-31 23:59:59",
    "is_active": 1,
    "created_at": "2025-01-01 16:00:00"
  }
}
```

#### 6.1.40 合约-尊享模式邀请链接列表接口

**GET** `/api/copy/expert/contract/exclusive-invites`

**中间件**: TokenMiddleware

**Query 参数**：

- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "expert_id": 1,
        "expert_type": "App\\Model\\Copy\\CopyContractExpert",
        "name": "VIP客户邀请",
        "invite_code": "INV123456789",
        "invite_url": "https://example.com/copy/invite/INV123456789",
        "profit_sharing_rate": "15.00",
        "max_use_count": 10,
        "used_count": 3,
        "expire_at": "2025-12-31 23:59:59",
        "is_active": 1,
        "created_at": "2025-01-01 16:00:00"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 5,
      "total_pages": 1
    }
  }
}
```

#### 6.1.41 合约-尊享模式邀请链接编辑接口

**PUT** `/api/copy/expert/contract/exclusive-invites/{id}`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (邀请链接 ID)

**Body 参数**：

```json
{
  "name": "VIP客户邀请（更新）", // 邀请链接名称
  "profit_sharing_rate": "18.00", // 分润比例 %
  "max_use_count": 15, // 最大使用次数，null表示无限制
  "expire_at": "2025-12-31 23:59:59", // 过期时间，null表示永不过期
  "is_active": 1 // 是否启用：1-启用，0-禁用
}
```

**验证规则**:

- name: required|string|max:100
- profit_sharing_rate: required|numeric|min:0|max:100
- max_use_count: nullable|integer|min:1
- expire_at: nullable|date_format:Y-m-d H:i:s
- is_active: required|integer|in:0,1

响应示例：

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "name": "VIP客户邀请（更新）",
    "invite_code": "INV123456789",
    "invite_url": "https://example.com/copy/invite/INV123456789",
    "profit_sharing_rate": "18.00",
    "max_use_count": 15,
    "used_count": 3,
    "expire_at": "2025-12-31 23:59:59",
    "is_active": 1,
    "updated_at": "2025-01-01 17:00:00"
  }
}
```

#### 6.1.42 合约-尊享模式邀请链接删除接口

**DELETE** `/api/copy/expert/contract/exclusive-invites/{id}`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (邀请链接 ID)

响应示例：

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

#### 6.1.43 合约-尊享模式成员列表接口

**GET** `/api/copy/expert/contract/exclusive-members`

**中间件**: TokenMiddleware

**Query 参数**：

- `invite_id`: integer|nullable (邀请链接 ID，为空表示查看所有尊享成员)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "follower_user": {
          "id": 100,
          "username": "vip_user001",
          "display_name": "VIP用户001",
          "avatar": "https://test.com/avatar.png"
        },
        "invite": {
          "id": 1,
          "name": "VIP客户邀请",
          "invite_code": "INV123456789"
        },
        "profit_sharing_rate": "15.00", // 分润比例 %
        "copy_account_balance": "5000.********", // 合约跟单账户资金
        "total_copy_count": 35, // 累计跟单笔数
        "copy_profit": "850.********", // 跟单收益
        "copy_net_profit": "722.********", // 跟单净利润
        "joined_at": "2025-01-01 10:00:00" // 加入时间
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 15,
      "total_pages": 1
    }
  }
}
```

#### 6.1.44 合约-尊享模式成员分润比例调整接口

**PUT** `/api/copy/expert/contract/exclusive-members/{id}/profit-rate`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (尊享成员 ID)

**Body 参数**：

```json
{
  "profit_sharing_rate": "20.00" // 新的分润比例 %
}
```

**验证规则**:

- profit_sharing_rate: required|numeric|min:0|max:100

响应示例：

```json
{
  "code": 200,
  "message": "调整成功",
  "data": {
    "id": 1,
    "follower_user_id": 100,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "invite_id": 1,
    "profit_sharing_rate": "20.00",
    "adjustment_count": 2, // 调整次数
    "updated_at": "2025-01-01 18:00:00"
  }
}
```

#### 6.1.45 合约-尊享模式成员移除接口

**DELETE** `/api/copy/expert/contract/exclusive-members/{id}`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (尊享成员 ID)

响应示例：

```json
{
  "code": 200,
  "message": "移除成功",
  "data": null
}
```

#### 6.1.46 合约-粉丝列表接口

**GET** `/api/copy/expert/contract/fans`

**中间件**: TokenMiddleware

**Query 参数**：

- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "fan_user": {
          "id": 200,
          "username": "fan_user001",
          "display_name": "粉丝用户001",
          "avatar": "https://test.com/avatar.png"
        },
        "followed_at": "2025-01-01 12:00:00", // 关注时间
        "is_copying": true // 是否正在跟单
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 200,
      "total_pages": 10
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 查询专家的粉丝列表：
  - 查询关注该专家的所有用户
  - 关联查询粉丝的用户基本信息
  - 获取粉丝的关注时间
- 检查粉丝的跟单状态：
  - 判断粉丝是否正在跟单该专家
  - 获取跟单配置的启用状态
- 按关注时间倒序排列粉丝列表
- 分页返回粉丝数据：
  - 包含粉丝用户信息
  - 关注时间
  - 是否正在跟单状态
- 统计粉丝总数和分页信息

#### 6.2.4 现货-交易专家列表接口

**GET** `/api/copy/user/spot/experts`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤：0-全部，7-7 天，30-30 天，90-90 天，180-180 天)
- `profit_min`: numeric|nullable (收益最小值，与 days 相关)
- `profit_rate_min`: numeric|nullable (收益率最小值 %，与 days 相关)
- `profit_rate_max`: numeric|nullable (收益率最大值 %，与 days 相关)
- `win_rate_min`: numeric|nullable (胜率最小值 %，与 days 相关)
- `follower_profit_min`: numeric|nullable (跟单者收益最小值，与 days 相关)
- `entry_days_min`: integer|nullable (入驻天数最小值)
- `aum_min`: numeric|nullable (资产管理规模最小值)
- `level_ids`: array|nullable (专家等级 ID 数组)
- `currency_ids`: array|nullable (跟单币种 ID 数组)
- `hide_full`: boolean|nullable (隐藏满员专家)
- `assets_public`: boolean|nullable (仅显示资产公开的专家)
- `is_followed`: boolean|nullable (仅显示已关注的专家)
- `sort_by`: string|nullable|in:profit,win_rate,profit_rate,follower_profit,aum (排序字段)
- `sort_order`: string|nullable|in:asc,desc (排序方向)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "user": {
          "id": 1,
          "username": "spot_expert001",
          "display_name": "现货专家",
          "avatar": "https://test.com/avatar.png"
        },
        "level": {
          "id": 1,
          "level": 1,
          "name": "初级专家",
          "icon": "https://test.com/icon.png"
        },
        "current_follower_count": 65, // 当前跟单人数
        "max_follower_count": 100, // 最大跟单人数
        "statistics": {
          "profit_rate": "12.30", // 收益率 %（与days相关）
          "total_profit": "800.********", // 总收益（与days相关）
          "win_rate": "68.50", // 胜率 %（与days相关）
          "follower_profit": "8000.********", // 跟单者收益（与days相关）
          "aum": "30000.********" // 资产管理规模
        },
        "profit_rate_chart": [
          // 收益率统计折线图（与days相关）
          {
            "date": "2025-01-01",
            "profit_rate": "1.8"
          },
          {
            "date": "2025-01-02",
            "profit_rate": "2.5"
          }
        ],
        "is_followed": false, // 是否已关注
        "show_total_assets": 1, // 是否展示总资产
        "profit_sharing_rate": "8.00" // 分润比例 %
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 120,
      "total_pages": 6
    }
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 根据筛选条件构建查询：
  - 时间过滤：根据 days 参数过滤专家数据统计时间范围
  - 收益过滤：根据 profit_min、profit_rate_min/max 等参数过滤
  - 胜率过滤：根据 win_rate_min 参数过滤
  - 其他条件：跟单者收益、入驻天数、AUM、等级、币种等
- 查询符合条件的现货交易专家列表
- 获取专家基本信息：用户信息、等级信息
- 计算专家统计数据：收益率、总收益、胜率、跟单者收益、AUM 等
- 生成收益率统计折线图数据
- 检查当前用户是否已关注该专家
- 根据排序条件对结果进行排序
- 分页返回现货专家列表数据

#### 6.2.5 现货-交易专家收益率统计图接口

**GET** `/api/copy/user/spot/expert/{id}/profit-rate-chart`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "date": "2025-01-01", // 日期
        "profit_rate": "1.80" // 收益率 %
      },
      {
        "date": "2025-01-02",
        "profit_rate": "2.50"
      },
      {
        "date": "2025-01-03",
        "profit_rate": "1.20"
      }
    ]
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 验证专家 ID 的有效性：
  - 检查专家是否存在
  - 验证专家是否为现货交易专家
  - 检查专家是否处于可查看状态
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - 默认查询最近 30 天的数据
- 查询专家的历史现货交易数据：
  - 按日期聚合已平仓订单的盈亏数据
  - 计算每日的累计收益率
- 计算收益率统计：
  - 以专家初始资金为基准计算收益率
  - 计算累计收益率和日收益率
  - 处理数据缺失的日期（补零或插值）
- 生成图表数据：
  - 按日期排序生成时间序列数据
  - 格式化收益率为百分比形式
- 返回图表所需的数据格式

#### 6.2.6 现货-交易专家总收益统计图接口

**GET** `/api/copy/user/spot/expert/{id}/total-profit-chart`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "date": "2025-01-01", // 日期
        "total_profit": "80.********" // 总收益
      },
      {
        "date": "2025-01-02",
        "total_profit": "120.80000000"
      },
      {
        "date": "2025-01-03",
        "total_profit": "95.30000000"
      }
    ]
  }
}
```

#### 6.2.7 合约-交易专家详情接口

**GET** `/api/copy/user/contract/expert/{id}/detail`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "user": {
      "id": 1,
      "username": "expert001",
      "display_name": "专业交易员",
      "avatar": "https://test.com/avatar.png"
    },
    "level": {
      "id": 1,
      "level": 1,
      "name": "初级专家",
      "icon": "https://test.com/icon.png"
    },
    "introduction": "专业合约交易，稳健收益",
    "entry_days": 180, // 入驻天数
    "current_follower_count": 85, // 当前跟单人数
    "max_follower_count": 100, // 最大跟单人数
    "fan_count": 200, // 粉丝数量
    "is_followed": true, // 是否已关注
    "is_copying": false, // 是否正在跟单
    "show_total_assets": 1, // 是否展示总资产
    "show_expert_rating": 1, // 是否展示专家评分
    "total_assets": "2500.********", // 总资产（仅当show_total_assets=1时显示）
    "expert_rating": "85.50", // 专家评分（仅当show_expert_rating=1时显示）
    "expert_ranking": 15, // 专家排名（仅当show_expert_rating=1时显示）
    "profit_sharing_rate": "10.00", // 分润比例 %
    "min_follow_amount": "100.********", // 最小跟单金额
    "recommend_params": {
      "fixed_amount": {
        "amount": "100.00",
        "stop_loss_rate": "10.00",
        "take_profit_rate": "20.00",
        "max_copy_amount": "1000.00"
      },
      "multiplier": {
        "multiplier": "0.1",
        "stop_loss_rate": "10.00",
        "take_profit_rate": "20.00",
        "max_copy_amount": "1000.00"
      }
    },
    "currency_symbols": ["BTCUSDT", "ETHUSDT", "ADAUSDT"], // 带单币种
    "exclusive_mode": 0 // 是否开启尊享模式
  }
}
```

#### 6.2.8 现货-交易专家详情接口

**GET** `/api/copy/user/spot/expert/{id}/detail`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "user": {
      "id": 1,
      "username": "spot_expert001",
      "display_name": "现货专家",
      "avatar": "https://test.com/avatar.png"
    },
    "level": {
      "id": 1,
      "level": 1,
      "name": "初级专家",
      "icon": "https://test.com/icon.png"
    },
    "introduction": "专业现货交易，稳健收益",
    "entry_days": 150, // 入驻天数
    "current_follower_count": 65, // 当前跟单人数
    "max_follower_count": 100, // 最大跟单人数
    "fan_count": 180, // 粉丝数量
    "is_followed": false, // 是否已关注
    "is_copying": false, // 是否正在跟单
    "show_total_assets": 1, // 是否展示总资产
    "total_assets": "1800.********", // 总资产（仅当show_total_assets=1时显示）
    "profit_sharing_rate": "8.00", // 分润比例 %
    "min_follow_amount": "100.********", // 最小跟单金额
    "recommend_params": {
      "fixed_amount": {
        "amount": "100.00",
        "stop_loss_rate": "10.00",
        "take_profit_rate": "20.00",
        "max_copy_amount": "1000.00"
      },
      "multiplier": {
        "multiplier": "0.1",
        "stop_loss_rate": "10.00",
        "take_profit_rate": "20.00",
        "max_copy_amount": "1000.00"
      }
    },
    "currency_symbols": ["BTCUSDT", "ETHUSDT", "ADAUSDT"], // 带单币种
    "exclusive_mode": 0 // 是否开启尊享模式
  }
}
```

#### 6.2.9 关注/取消关注交易专家接口

**POST** `/api/copy/user/expert/{id}/follow`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Body 参数**：

```json
{
  "expert_type": 1, // 专家类型：1-合约专家，2-现货专家
  "action": "follow" // 操作：follow-关注，unfollow-取消关注
}
```

**验证规则**:

- expert_type: required|integer|in:1,2
- action: required|string|in:follow,unfollow

响应示例：

```json
{
  "code": 200,
  "message": "关注成功",
  "data": {
    "id": 1,
    "user_id": 100,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "followed_at": "2025-01-01 18:00:00"
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 验证专家 ID 的有效性：
  - 检查专家是否存在
  - 验证专家是否处于可关注状态
  - 检查专家类型（合约或现货）
- 根据 action 参数执行操作：
  - follow：执行关注操作
  - unfollow：执行取消关注操作
- 关注操作逻辑：
  - 检查是否已经关注该专家
  - 创建关注关系记录
  - 更新专家的粉丝数量统计
- 取消关注操作逻辑：
  - 检查是否已关注该专家
  - 删除关注关系记录
  - 更新专家的粉丝数量统计
  - 如果正在跟单，提示用户先停止跟单
- 记录操作日志
- 返回操作结果

#### 6.2.10 合约-跟单设置接口

**POST** `/api/copy/user/contract/copy-settings`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "expert_id": 1, // 交易专家ID
  "mode": 1, // 跟单模式：1-智能比例，2-多元探索
  "copy_type": "fixed_amount", // 跟单类型：fixed_amount-固定金额，multiplier-跟单倍率
  "amount": "500.********", // 固定金额（copy_type=fixed_amount时必填）
  "multiplier": "0.2", // 跟单倍率（copy_type=multiplier时必填）
  "stop_loss_rate": "10.00", // 止损比例 %
  "take_profit_rate": "20.00", // 止盈比例 %
  "max_copy_amount": "2000.********", // 最大跟单金额
  "currency_ids": [1, 2, 3], // 跟单币种ID数组
  "transfer_from_account": 1, // 划转资金来源账户
  "transfer_amount": "1000.********" // 划转金额
}
```

**验证规则**:

- expert_id: required|integer|exists:copy_contract_experts,id
- mode: required|integer|in:1,2
- copy_type: required|string|in:fixed_amount,multiplier
- amount: required_if:copy_type,fixed_amount|numeric|min:0
- multiplier: required_if:copy_type,multiplier|numeric|min:0|max:1
- stop_loss_rate: required|numeric|min:0|max:100
- take_profit_rate: required|numeric|min:0|max:100
- max_copy_amount: required|numeric|min:0
- currency_ids: required|array|min:1
- currency_ids.\*: required|integer|exists:currency,id
- transfer_from_account: required|integer
- transfer_amount: required|numeric|min:0

响应示例：

```json
{
  "code": 200,
  "message": "跟单设置成功",
  "data": {
    "id": 1,
    "user_id": 100,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "mode": 1,
    "copy_type": "fixed_amount",
    "amount": "500.********",
    "multiplier": null,
    "stop_loss_rate": "10.00",
    "take_profit_rate": "20.00",
    "max_copy_amount": "2000.********",
    "currency_ids": [1, 2, 3],
    "is_active": 1,
    "created_at": "2025-01-01 19:00:00"
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 验证专家 ID 的有效性：
  - 检查专家是否存在且为合约专家
  - 验证专家是否处于可跟单状态
  - 检查专家的跟单人数是否已满
- 验证跟单参数的有效性：
  - 检查跟单模式（智能比例/多元探索）
  - 验证跟单类型和金额/倍率设置
  - 检查止损止盈比例的合理性
  - 验证最大跟单金额限制
  - 检查币种 ID 的有效性
- 检查用户资金状况：
  - 验证合约跟单账户余额是否充足
  - 检查是否满足最小跟单金额要求
- 创建或更新跟单配置：
  - 如果已存在配置则更新，否则创建新配置
  - 设置跟单参数和风控设置
  - 激活跟单状态
- 记录跟单关系和操作日志
- 返回跟单配置结果

#### 6.2.11 现货-跟单设置接口

**POST** `/api/copy/user/spot/copy-settings`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "expert_id": 1, // 交易专家ID
  "copy_type": "fixed_amount", // 跟单类型：fixed_amount-固定金额，multiplier-跟单倍率
  "amount": "300.********", // 固定金额（copy_type=fixed_amount时必填）
  "multiplier": "0.15", // 跟单倍率（copy_type=multiplier时必填）
  "stop_loss_rate": "8.00", // 止损比例 %
  "take_profit_rate": "15.00", // 止盈比例 %
  "max_copy_amount": "1500.********", // 最大跟单金额
  "currency_ids": [1, 2, 3], // 跟单币种ID数组
  "transfer_from_account": 1, // 划转资金来源账户
  "transfer_amount": "800.********" // 划转金额
}
```

**验证规则**:

- expert_id: required|integer|exists:copy_spot_experts,id
- copy_type: required|string|in:fixed_amount,multiplier
- amount: required_if:copy_type,fixed_amount|numeric|min:0
- multiplier: required_if:copy_type,multiplier|numeric|min:0|max:1
- stop_loss_rate: required|numeric|min:0|max:100
- take_profit_rate: required|numeric|min:0|max:100
- max_copy_amount: required|numeric|min:0
- currency_ids: required|array|min:1
- currency_ids.\*: required|integer|exists:currency,id
- transfer_from_account: required|integer
- transfer_amount: required|numeric|min:0

响应示例：

```json
{
  "code": 200,
  "message": "跟单设置成功",
  "data": {
    "id": 1,
    "user_id": 100,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopySpotExpert",
    "copy_type": "fixed_amount",
    "amount": "300.********",
    "multiplier": null,
    "stop_loss_rate": "8.00",
    "take_profit_rate": "15.00",
    "max_copy_amount": "1500.********",
    "currency_ids": [1, 2, 3],
    "is_active": 1,
    "created_at": "2025-01-01 19:00:00"
  }
}
```

#### 6.2.12 合约-我的跟单配置列表接口

**GET** `/api/copy/user/contract/my-copy-settings`

**中间件**: TokenMiddleware

**Query 参数**：

- `status`: string|nullable|in:active,inactive (状态过滤：active-启用，inactive-停用)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "expert": {
          "id": 1,
          "user": {
            "id": 1,
            "username": "expert001",
            "display_name": "专业交易员",
            "avatar": "https://test.com/avatar.png"
          },
          "level": {
            "id": 1,
            "level": 1,
            "name": "初级专家",
            "icon": "https://test.com/icon.png"
          }
        },
        "mode": 1, // 跟单模式：1-智能比例，2-多元探索
        "copy_type": "fixed_amount",
        "amount": "500.********",
        "multiplier": null,
        "stop_loss_rate": "10.00",
        "take_profit_rate": "20.00",
        "max_copy_amount": "2000.********",
        "currency_symbols": ["BTCUSDT", "ETHUSDT"],
        "is_active": 1,
        "copy_account_balance": "1500.********", // 合约跟单账户余额
        "total_copy_count": 25, // 累计跟单笔数
        "copy_profit": "350.********", // 跟单收益
        "copy_net_profit": "315.********", // 跟单净利润
        "created_at": "2025-01-01 19:00:00"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 3,
      "total_pages": 1
    }
  }
}
```

#### 6.2.13 合约-跟单配置修改接口

**PUT** `/api/copy/user/contract/copy-settings/{id}`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (跟单配置 ID)

**Body 参数**：

```json
{
  "mode": 2, // 跟单模式：1-智能比例，2-多元探索
  "copy_type": "multiplier", // 跟单类型：fixed_amount-固定金额，multiplier-跟单倍率
  "amount": null, // 固定金额
  "multiplier": "0.3", // 跟单倍率
  "stop_loss_rate": "12.00", // 止损比例 %
  "take_profit_rate": "25.00", // 止盈比例 %
  "max_copy_amount": "3000.********", // 最大跟单金额
  "currency_ids": [1, 2], // 跟单币种ID数组
  "is_active": 1 // 是否启用：1-启用，0-停用
}
```

**验证规则**:

- mode: required|integer|in:1,2
- copy_type: required|string|in:fixed_amount,multiplier
- amount: required_if:copy_type,fixed_amount|numeric|min:0
- multiplier: required_if:copy_type,multiplier|numeric|min:0|max:1
- stop_loss_rate: required|numeric|min:0|max:100
- take_profit_rate: required|numeric|min:0|max:100
- max_copy_amount: required|numeric|min:0
- currency_ids: required|array|min:1
- currency_ids.\*: required|integer|exists:currency,id
- is_active: required|integer|in:0,1

响应示例：

```json
{
  "code": 200,
  "message": "修改成功",
  "data": {
    "id": 1,
    "user_id": 100,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "mode": 2,
    "copy_type": "multiplier",
    "amount": null,
    "multiplier": "0.3",
    "stop_loss_rate": "12.00",
    "take_profit_rate": "25.00",
    "max_copy_amount": "3000.********",
    "currency_ids": [1, 2],
    "is_active": 1,
    "updated_at": "2025-01-01 20:00:00"
  }
}
```

#### 6.2.14 合约-停止跟单接口

**DELETE** `/api/copy/user/contract/copy-settings/{id}`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (跟单配置 ID)

响应示例：

```json
{
  "code": 200,
  "message": "停止跟单成功",
  "data": null
}
```

#### 6.2.15 合约-我的跟单记录接口

**GET** `/api/copy/user/contract/my-copy-orders`

**中间件**: TokenMiddleware

**Query 参数**：

- `expert_id`: integer|nullable (交易专家 ID)
- `status`: string|nullable|in:open,closed (订单状态：open-持仓中，closed-已平仓)
- `currency_id`: integer|nullable (币种 ID)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "expert": {
          "id": 1,
          "user": {
            "id": 1,
            "username": "expert001",
            "display_name": "专业交易员",
            "avatar": "https://test.com/avatar.png"
          }
        },
        "currency_symbol": "BTCUSDT", // 币种
        "direction": "long", // 方向
        "leverage": 10, // 杠杆
        "open_time": "2025-01-01 10:00:00", // 开仓时间
        "close_time": "2025-01-01 14:30:00", // 平仓时间
        "entry_price": "44800.********", // 入场价
        "exit_price": "46200.********", // 出场价
        "quantity": "0.05000000", // 数量
        "margin": "225.********", // 保证金
        "realized_pnl": "70.********", // 已实现盈亏
        "profit_sharing_fee": "7.********", // 分润费用
        "net_profit": "63.********", // 净利润
        "status": "closed", // 状态：open-持仓中，closed-已平仓
        "copy_order_id": "COPY123456789" // 跟单订单编号
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 45,
      "total_pages": 3
    }
  }
}
```

#### 6.2.16 现货-我的跟单配置列表接口

**GET** `/api/copy/user/spot/my-copy-settings`

**中间件**: TokenMiddleware

**Query 参数**：

- `status`: string|nullable|in:active,inactive (状态过滤：active-启用，inactive-停用)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "expert": {
          "id": 1,
          "user": {
            "id": 1,
            "username": "spot_expert001",
            "display_name": "现货专家",
            "avatar": "https://test.com/avatar.png"
          },
          "level": {
            "id": 1,
            "level": 1,
            "name": "初级专家",
            "icon": "https://test.com/icon.png"
          }
        },
        "copy_type": "fixed_amount",
        "amount": "300.********",
        "multiplier": null,
        "stop_loss_rate": "8.00",
        "take_profit_rate": "15.00",
        "max_copy_amount": "1500.********",
        "currency_symbols": ["BTCUSDT", "ETHUSDT"],
        "is_active": 1,
        "spot_account_balance": "1200.********", // 现货账户余额
        "total_copy_count": 18, // 累计跟单笔数
        "copy_profit": "220.********", // 跟单收益
        "copy_net_profit": "202.********", // 跟单净利润
        "created_at": "2025-01-01 19:00:00"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 2,
      "total_pages": 1
    }
  }
}
```

#### 6.2.10 合约-交易专家币种偏好统计接口（饼图）

**GET** `/api/copy/user/contract/expert/{id}/currency-preference`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "currency_symbol": "BTCUSDT",
        "percentage": "45.5" // 占比 %
      },
      {
        "currency_symbol": "ETHUSDT",
        "percentage": "35.2"
      },
      {
        "currency_symbol": "ADAUSDT",
        "percentage": "19.3"
      }
    ]
  }
}
```

#### 6.2.11 合约-交易专家持仓时间统计接口

**GET** `/api/copy/user/contract/expert/{id}/position-time-stats`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "average_hold_time": "4.5", // 平均持仓时间（小时）
    "max_hold_time": "24.8", // 最长持仓时间（小时）
    "chart_data": [
      {
        "time_range": "0-5小时", // 持仓时段
        "profit_count": 15, // 盈利笔数
        "loss_count": 3 // 亏损笔数
      },
      {
        "time_range": "5-10小时",
        "profit_count": 12,
        "loss_count": 5
      }
    ]
  }
}
```

#### 6.2.12 合约-交易专家交易分析精灵统计接口（雷达图）

**GET** `/api/copy/user/contract/expert/{id}/expert-analysis-radar`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "expert_score": "85.5", // 专家评分
    "expert_ranking": 15, // 专家排名
    "radar_data": {
      "indicators": [
        {
          "name": "盈利因子",
          "max": 5
        },
        {
          "name": "胜率",
          "max": 100
        },
        {
          "name": "平均盈亏比",
          "max": 5
        }
      ],
      "series": [
        {
          "name": "专家平均能力参考值",
          "data": [2.5, 65.0, 1.8]
        },
        {
          "name": "当前专家参数",
          "data": [3.2, 75.5, 2.3]
        }
      ]
    }
  }
}
```

#### 6.2.13 合约-交易专家跟单者交易分析精灵统计接口（雷达图）

**GET** `/api/copy/user/contract/expert/{id}/follower-analysis-radar`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "radar_data": {
      "indicators": [
        {
          "name": "盈利因子",
          "max": 5
        },
        {
          "name": "胜率",
          "max": 100
        },
        {
          "name": "平均盈亏比",
          "max": 5
        }
      ],
      "series": [
        {
          "name": "跟单专家参数",
          "data": [3.2, 75.5, 2.3]
        },
        {
          "name": "跟单者参数",
          "data": [2.8, 68.2, 2.0]
        }
      ]
    }
  }
}
```

#### 6.2.14 合约-交易专家交易指标接口

**GET** `/api/copy/user/contract/expert/{id}/trading-indicators`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "last_trade_time": "2025-01-01 14:30:00", // 最近交易时间
    "trade_count": 45, // 交易次数
    "daily_avg_trade_count": "3.2", // 日均交易次数
    "total_profit": "2500.********", // 总盈利
    "max_profit": "500.********", // 最大盈利
    "max_loss": "-200.********", // 最大亏损
    "max_drawdown": "350.********", // 最大回撤
    "long_short_ratio": "1.8" // 多空比
  }
}
```

### 6.3 通用接口

#### 6.3.1 交易专家等级列表接口

**GET** `/api/copy/common/expert-levels`

**中间件**: TokenMiddleware

**Query 参数**：

- `type`: integer|nullable|in:1,2 (专家类型：1-合约专家，2-现货专家，为空表示全部)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "type": 1,
        "level": 1,
        "name": "初级专家",
        "icon": "https://test.com/icon1.png",
        "condition_amount": "1000.********", // 条件：资产金额
        "condition_follow_amount": "5000.********", // 条件：跟单金额
        "condition_follow_count": 10, // 条件：跟单人数
        "max_follow_count": 100, // 最大跟单人数
        "max_profit_rate": "10.00" // 最大分润比例 %
      },
      {
        "id": 2,
        "type": 1,
        "level": 2,
        "name": "中级专家",
        "icon": "https://test.com/icon2.png",
        "condition_amount": "5000.********",
        "condition_follow_amount": "20000.********",
        "condition_follow_count": 50,
        "max_follow_count": 200,
        "max_profit_rate": "15.00"
      },
      {
        "id": 3,
        "type": 1,
        "level": 3,
        "name": "高级专家",
        "icon": "https://test.com/icon3.png",
        "condition_amount": "20000.********",
        "condition_follow_amount": "100000.********",
        "condition_follow_count": 200,
        "max_follow_count": 500,
        "max_profit_rate": "20.00"
      }
    ]
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 根据 type 参数过滤专家等级类型：
  - 如果指定 type，则只返回对应类型的等级（1-合约，2-现货）
  - 如果不指定 type，则返回所有类型的等级
- 查询系统配置的专家等级列表：
  - 按等级从低到高排序
  - 获取等级的基本信息（名称、图标等）
  - 获取等级的升级条件（资金、跟单金额、跟单人数）
  - 获取等级的权益（最大跟单人数、最大分润比例）
- 格式化等级数据：
  - 包含等级 ID、类型、等级数值
  - 包含等级名称和图标
  - 包含升级条件和权益信息
- 返回完整的等级列表数据
- 支持前端展示等级体系和升级条件

#### 6.3.2 币种列表接口

**GET** `/api/copy/common/currencies`

**中间件**: TokenMiddleware

**Query 参数**：

- `type`: string|nullable|in:contract,spot (交易类型：contract-合约，spot-现货，为空表示全部)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "symbol": "BTCUSDT",
        "base_currency": "BTC",
        "quote_currency": "USDT",
        "type": "both", // 类型：contract-仅合约，spot-仅现货，both-合约现货都支持
        "is_active": true
      },
      {
        "id": 2,
        "symbol": "ETHUSDT",
        "base_currency": "ETH",
        "quote_currency": "USDT",
        "type": "both",
        "is_active": true
      },
      {
        "id": 3,
        "symbol": "ADAUSDT",
        "base_currency": "ADA",
        "quote_currency": "USDT",
        "type": "spot",
        "is_active": true
      }
    ]
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 根据 type 参数过滤币种类型：
  - contract：只返回支持合约交易的币种
  - spot：只返回支持现货交易的币种
  - both：返回同时支持合约和现货的币种
  - 不指定：返回所有币种
- 查询系统支持的交易币种列表：
  - 获取币种基本信息（symbol、base_currency、quote_currency）
  - 获取币种的交易类型支持情况
  - 获取币种的启用状态
- 过滤活跃币种：
  - 只返回 is_active 为 true 的币种
  - 按币种名称排序
- 格式化币种数据：
  - 包含币种 ID 和交易对符号
  - 包含基础货币和计价货币
  - 包含支持的交易类型
  - 包含启用状态
- 返回完整的币种列表数据
- 支持前端币种选择和配置

#### 6.3.3 尊享模式邀请验证接口

**GET** `/api/copy/common/exclusive-invite/{code}/verify`

**中间件**: TokenMiddleware

**Path 参数**：

- `code`: required|string (邀请码)

响应示例（有效邀请码）：

```json
{
  "code": 200,
  "message": "邀请码有效",
  "data": {
    "id": 1,
    "expert": {
      "id": 1,
      "user": {
        "id": 1,
        "username": "expert001",
        "display_name": "专业交易员",
        "avatar": "https://test.com/avatar.png"
      },
      "level": {
        "id": 1,
        "level": 1,
        "name": "初级专家",
        "icon": "https://test.com/icon.png"
      },
      "type": 1 // 专家类型：1-合约专家，2-现货专家
    },
    "name": "VIP客户邀请",
    "profit_sharing_rate": "15.00", // 分润比例 %
    "max_use_count": 10, // 最大使用次数
    "used_count": 3, // 已使用次数
    "expire_at": "2025-12-31 23:59:59", // 过期时间
    "is_valid": true // 是否有效
  }
}
```

响应示例（无效邀请码）：

```json
{
  "code": 400,
  "message": "邀请码无效或已过期",
  "data": {
    "is_valid": false
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 验证邀请码格式的有效性：
  - 检查邀请码是否为空
  - 验证邀请码格式是否正确
- 查询邀请码信息：
  - 根据邀请码查询对应的邀请记录
  - 获取邀请码的基本信息（名称、分润比例等）
  - 获取邀请码的使用限制（最大使用次数、已使用次数）
  - 获取邀请码的有效期信息
- 验证邀请码的有效性：
  - 检查邀请码是否存在
  - 验证邀请码是否在有效期内
  - 检查邀请码使用次数是否已达上限
  - 验证专家是否处于可邀请状态
- 获取专家信息：
  - 查询邀请码对应的专家信息
  - 获取专家的用户信息和等级信息
  - 获取专家类型（合约或现货）
- 返回验证结果：
  - 有效时返回邀请码详细信息
  - 无效时返回错误信息和原因

### 6.4 枚举值定义

#### 6.4.1 专家状态枚举

```php
// App\Model\Enums\Copy\ExpertStatus
class ExpertStatus
{
    const PENDING = 1;    // 待审核
    const APPROVED = 2;   // 审核通过
    const REJECTED = 3;   // 审核拒绝
}
```

#### 6.4.2 专家类型枚举

```php
// App\Model\Enums\Copy\ExpertType
class ExpertType
{
    const CONTRACT = 1;   // 合约专家
    const SPOT = 2;       // 现货专家
}
```

#### 6.4.3 跟单模式枚举

```php
// App\Model\Enums\Copy\CopyMode
class CopyMode
{
    const SMART_RATIO = 1;      // 智能比例
    const MULTI_EXPLORE = 2;    // 多元探索
}
```

#### 6.4.4 跟单类型枚举

```php
// App\Model\Enums\Copy\CopyType
class CopyType
{
    const FIXED_AMOUNT = 'fixed_amount';  // 固定金额
    const MULTIPLIER = 'multiplier';      // 跟单倍率
}
```

#### 6.4.5 订单状态枚举

```php
// App\Model\Enums\Copy\OrderStatus
class OrderStatus
{
    const OPEN = 'open';      // 持仓中
    const CLOSED = 'closed';  // 已平仓
}
```

### 6.5 错误码说明

#### 6.5.1 通用错误码

| 错误码 | 说明           |
| ------ | -------------- |
| 200    | 成功           |
| 400    | 请求参数错误   |
| 401    | 未授权         |
| 403    | 禁止访问       |
| 404    | 资源不存在     |
| 422    | 验证失败       |
| 500    | 服务器内部错误 |

#### 6.5.2 跟单模块专用错误码

| 错误码 | 说明                         |
| ------ | ---------------------------- |
| 10001  | 用户未完成 KYC 认证          |
| 10002  | 用户已经是交易专家           |
| 10003  | 专家申请正在审核中           |
| 10004  | 划转资金不足                 |
| 10005  | 专家不存在或已停用           |
| 10006  | 跟单人数已满                 |
| 10007  | 跟单金额不足最小限制         |
| 10008  | 已经在跟单该专家             |
| 10009  | 邀请码无效或已过期           |
| 10010  | 尊享模式邀请使用次数已达上限 |

### 6.6 接口总结

本 API 文档共包含以下接口：

#### 6.6.1 专家端接口（53 个）

**基础功能接口（6 个）**：

- 专家申请接口
- 交易专家信息接口
- 合约/现货个人设置接口
- 合约/现货问题反馈/身份撤销接口
- 交易专家等级接口

**数据统计接口（12 个）**：

- 合约/现货带单数据统计接口
- 合约/现货综合数据接口
- 合约/现货分润数据统计接口
- 合约/现货历史分润记录接口
- 合约跟单账号余额历史接口
- 合约/现货带单币对接口

**图表分析接口（17 个）**：

- 合约/现货收益率统计图接口
- 合约/现货总收益统计图接口
- 合约/现货币种偏好统计接口
- 合约持仓时间统计接口
- 现货持仓时间统计接口（散点图）
- 现货交易量统计接口
- 现货资产构成统计接口
- 合约交易分析精灵统计接口
- 合约跟单者交易分析精灵统计接口
- 合约交易指标接口
- 合约/现货币对交易表现接口
- 合约交易日历接口

**跟随者管理接口（8 个）**：

- 合约/现货我的跟随者接口
- 合约/现货移除跟随者接口
- 合约/现货交易专家列表展示状态获取接口
- 合约/现货粉丝列表接口

**订单管理接口（4 个）**：

- 合约/现货带单订单数据接口
- 合约/现货撤销止盈止损接口

**尊享模式接口（6 个）**：

- 合约/现货尊享模式开启/关闭接口
- 合约尊享模式邀请链接创建/编辑/删除接口
- 合约尊享模式成员列表/管理接口

#### 6.6.2 用户端接口（29 个）

**专家查看接口（21 个）**：

- 合约/现货交易专家列表接口
- 合约/现货交易专家详情接口
- 合约/现货交易专家收益率/总收益统计图接口
- 合约交易专家币种偏好统计接口
- 合约交易专家持仓时间统计接口
- 合约交易专家交易分析精灵统计接口
- 合约交易专家跟单者交易分析精灵统计接口
- 合约交易专家交易指标接口
- 合约交易专家币对交易表现接口
- 合约交易专家交易日历接口
- 合约交易专家带单订单数据接口
- 合约交易专家跟随者接口
- 合约交易专家带单数据统计接口
- 合约综合数据接口
- 合约交易专家带单币对接口
- 现货交易专家相关统计图表接口等

**跟单操作接口（8 个）**：

- 关注/取消关注交易专家接口
- 合约/现货跟单设置接口
- 合约/现货我的跟单配置列表接口
- 合约跟单配置修改接口
- 合约停止跟单接口
- 合约我的跟单记录接口

#### 6.6.3 通用接口（3 个）

- 交易专家等级列表接口
- 币种列表接口
- 尊享模式邀请验证接口

**总计：85 个 API 接口**

### 6.7 开发注意事项

1. **权限控制**：所有接口都需要 TokenMiddleware 中间件进行用户身份验证
2. **参数验证**：严格按照文档中的验证规则进行参数校验
3. **异常处理**：统一使用 BusinessException 进行业务异常处理
4. **数据库事务**：涉及多表操作的接口需要使用数据库事务
5. **资金安全**：涉及资金划转的操作需要特别注意安全性验证
6. **性能优化**：列表查询接口需要合理使用分页和索引
7. **日志记录**：重要操作需要记录操作日志
8. **缓存策略**：频繁查询的数据可以考虑使用 Redis 缓存

### 6.8 接口逻辑说明补充

**重要说明**：本文档中已为关键接口提供了详细的接口逻辑说明，包括：

#### 6.8.1 已完成详细逻辑说明的接口

**专家端基础功能接口**：

- 专家申请接口（合约/现货）- 包含完整的申请流程逻辑
- 交易专家信息接口 - 包含数据查询和统计逻辑
- 合约/现货个人设置接口 - 包含参数验证和更新逻辑
- 合约/现货问题反馈/身份撤销接口 - 包含反馈处理逻辑
- 交易专家等级接口 - 包含等级查询和匹配逻辑

**专家端数据统计接口**：

- 合约/现货带单数据统计接口 - 包含统计计算逻辑
- 合约/现货综合数据接口 - 包含综合数据聚合逻辑
- 合约分润数据统计接口 - 包含分润计算逻辑
- 合约跟单账号余额历史接口 - 包含余额变动追踪逻辑
- 合约/现货带单币对接口 - 包含币种配置管理逻辑

**专家端图表分析接口**：

- 合约收益率统计图接口 - 包含图表数据生成逻辑
- 合约带单订单数据接口 - 包含订单查询和分页逻辑
- 合约撤销止盈止损接口 - 包含订单操作逻辑

**用户端接口**：

- 合约交易专家列表接口 - 包含筛选和排序逻辑

#### 6.8.2 通用接口逻辑说明模板

为了确保所有接口都有完整的逻辑说明，以下提供各类接口的标准逻辑说明模板：

#### 6.8.1 专家申请类接口逻辑模板

**标准逻辑流程**：

1. 验证用户身份和状态（正常状态、KYC 认证）
2. 检查用户是否已经是该类型专家
3. 检查是否有待审核的申请
4. 验证申请参数（资金、介绍等）
5. 执行资金划转（合约专家需要）
6. 匹配专家等级
7. 创建专家记录
8. 更新用户信息
9. 返回申请结果

#### 6.8.2 数据统计类接口逻辑模板

**标准逻辑流程**：

1. 验证用户权限（专家身份或查看权限）
2. 解析时间过滤条件
3. 查询相关订单/交易数据
4. 计算统计指标（收益率、胜率、回撤等）
5. 聚合数据（按时间、币种等维度）
6. 格式化返回数据

#### 6.8.3 图表数据类接口逻辑模板

**标准逻辑流程**：

1. 验证用户权限
2. 解析时间范围和过滤条件
3. 查询历史数据
4. 按时间维度聚合数据
5. 计算图表所需的统计值
6. 格式化为图表数据格式
7. 返回图表数据

#### 6.8.4 列表查询类接口逻辑模板

**标准逻辑流程**：

1. 验证用户权限
2. 解析筛选条件和排序参数
3. 构建查询条件
4. 执行分页查询
5. 关联查询相关数据（用户、等级等）
6. 计算统计数据
7. 检查关注状态等用户相关信息
8. 分页返回结果

#### 6.8.5 设置修改类接口逻辑模板

**标准逻辑流程**：

1. 验证用户权限
2. 验证参数有效性
3. 检查业务规则（如等级限制、金额限制等）
4. 更新数据库记录
5. 记录操作日志
6. 返回更新结果

#### 6.8.6 跟单操作类接口逻辑模板

**标准逻辑流程**：

1. 验证用户身份和状态
2. 验证专家状态和可跟单性
3. 检查跟单限制（人数、金额等）
4. 执行资金划转
5. 创建/更新跟单配置
6. 记录跟单关系
7. 返回操作结果

#### 6.8.7 管理操作类接口逻辑模板

**标准逻辑流程**：

1. 验证管理权限
2. 验证操作对象的有效性
3. 检查操作的合法性
4. 执行管理操作（移除、调整等）
5. 更新相关状态
6. 记录操作日志
7. 返回操作结果

### 6.9 接口安全性说明

#### 6.9.1 权限验证

- **TokenMiddleware**: 所有接口都需要用户登录验证
- **专家身份验证**: 专家端接口需要验证用户的专家身份
- **数据权限**: 确保用户只能访问自己的数据
- **操作权限**: 验证用户是否有权限执行特定操作

#### 6.9.2 参数验证

- **类型验证**: 严格验证参数类型和格式
- **范围验证**: 验证数值参数的合理范围
- **业务验证**: 验证参数的业务逻辑合理性
- **SQL 注入防护**: 使用参数化查询防止 SQL 注入

#### 6.9.3 资金安全

- **双重验证**: 涉及资金操作的接口需要额外验证
- **金额限制**: 验证转账金额的合理性
- **余额检查**: 确保账户余额充足
- **操作日志**: 记录所有资金相关操作

---

**文档版本**：v1.0
**最后更新**：2025-01-17
**文档状态**：完整版（含接口逻辑说明）
