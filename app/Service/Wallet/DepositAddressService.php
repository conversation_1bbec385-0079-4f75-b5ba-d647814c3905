<?php

declare(strict_types=1);

namespace App\Service\Wallet;

use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Model\User\UserDepositAddress;
use App\Service\Wallet\WalletEncryptionService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Guzzle\ClientFactory;

class DepositAddressService
{
    #[Inject]
    private WalletEncryptionService $encryption;

    #[Inject]
    private ClientFactory $clientFactory;

    private string $walletRpcApi;

    public function __construct()
    {
        $this->walletRpcApi = env('WALLET_RPC_API', '');
        if (empty($this->walletRpcApi)) {
            throw new BusinessException(ResultCode::FAIL, 'WALLET_RPC_API配置未设置');
        }
    }

    /**
     * 获取或生成用户充值地址
     */
    public function getOrCreateDepositAddress(int $userId, string $chainId): array
    {
        // 检查是否已有地址(同链复用)
        $existingAddress = UserDepositAddress::where(UserDepositAddress::FIELD_USER_ID, $userId)
            ->where(UserDepositAddress::FIELD_CHAIN_ID, $chainId)
            ->where(UserDepositAddress::FIELD_STATUS, 1)
            ->first();

        if ($existingAddress) {
            return [
                'address' => $existingAddress->getAddress(),
                'memo' => $existingAddress->getMemo(),
                'chain_id' => $existingAddress->getChainId(),
            ];
        }

        // 生成新地址
        return $this->generateNewAddress($userId, $chainId);
    }

    /**
     * 生成新的充值地址
     */
    private function generateNewAddress(int $userId, string $chainId): array
    {
        try {
            // 调用钱包API生成地址
            $walletData = $this->callWalletGenerateApi($chainId);

            // 加密私钥
            $encryptedPrivateKey = $this->encryption->encryptPrivateKey($walletData['privateKey']);

            // 保存到数据库
            $depositAddress = new UserDepositAddress();
            $depositAddress->setUserId($userId);
            $depositAddress->setChainId($chainId);
            $depositAddress->setAddress($walletData['address']);
            $depositAddress->setAddressType(1); // 普通地址
            $depositAddress->setPublicKey(''); // API返回数据中没有公钥
            $depositAddress->setPrivateKeyEncrypted($encryptedPrivateKey);
            $depositAddress->setStatus(1);

            if (!$depositAddress->save()) {
                throw new BusinessException(ResultCode::FAIL, '地址保存失败');
            }

            return [
                'address' => $walletData['address'],
                'memo' => null,
                'chain_id' => $chainId,
            ];

        } catch (\Exception $e) {
            if ($e instanceof BusinessException) {
                throw $e;
            }
            throw new BusinessException(ResultCode::FAIL, '地址生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 调用钱包API生成地址
     */
    private function callWalletGenerateApi(string $chainId): array
    {
        try {
            $client = $this->clientFactory->create([
                'timeout' => 5,
                'verify' => false,
                'proxy' => '',
            ]);

            $response = $client->post("{$this->walletRpcApi}/api/v1/wallet/generate", [
                'json' => [
                    'chain' => $chainId
                ],
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ]
            ]);

            $statusCode = $response->getStatusCode();
            if ($statusCode !== 200) {
                throw new BusinessException(ResultCode::FAIL, "钱包API请求失败，状态码: {$statusCode}");
            }

            $body = $response->getBody()->getContents();
            $result = json_decode($body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new BusinessException(ResultCode::FAIL, '钱包API返回数据格式错误');
            }

            if (!isset($result['success']) || !$result['success']) {
                $message = $result['message'] ?? '钱包地址生成失败';
                throw new BusinessException(ResultCode::FAIL, $message);
            }

            if (!isset($result['data']['address']) || !isset($result['data']['privateKey'])) {
                throw new BusinessException(ResultCode::FAIL, '钱包API返回数据不完整');
            }

            return $result['data'];

        } catch (\Exception $e) {
            if ($e instanceof BusinessException) {
                throw $e;
            }
            throw new BusinessException(ResultCode::FAIL, '调用钱包API失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取解密后的私钥
     */
    public function getDecryptedPrivateKey(int $addressId): string
    {
        $address = UserDepositAddress::find($addressId);
        if (!$address) {
            throw new BusinessException(ResultCode::FAIL, '地址记录不存在');
        }

        return $this->encryption->decryptPrivateKey($address->getPrivateKeyEncrypted());
    }

    /**
     * 验证地址格式
     */
    public function validateAddress(string $address, string $chainId): bool
    {
        // 简单的地址格式验证，可以根据需要扩展
        if (empty($address) || empty($chainId)) {
            return false;
        }

        // 基本长度和格式检查
        switch (strtolower($chainId)) {
            case 'btc':
            case 'bitcoin':
                return preg_match('/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/', $address) === 1;
            case 'eth':
            case 'ethereum':
            case 'bsc':
            case 'polygon':
            case 'arbitrum':
            case 'optimism':
                return preg_match('/^0x[a-fA-F0-9]{40}$/', $address) === 1;
            case 'tron':
            case 'trx':
                return preg_match('/^T[A-Za-z1-9]{33}$/', $address) === 1;
            default:
                return strlen($address) > 10; // 基本长度检查
        }
    }

    /**
     * 获取用户在指定链上的地址
     */
    public function getUserAddressByChain(int $userId, string $chainId): ?UserDepositAddress
    {
        return UserDepositAddress::where(UserDepositAddress::FIELD_USER_ID, $userId)
            ->where(UserDepositAddress::FIELD_CHAIN_ID, $chainId)
            ->where(UserDepositAddress::FIELD_STATUS, 1)
            ->first();
    }

    /**
     * 获取用户所有充值地址
     */
    public function getUserAllAddresses(int $userId): array
    {
        $addresses = UserDepositAddress::where(UserDepositAddress::FIELD_USER_ID, $userId)
            ->where(UserDepositAddress::FIELD_STATUS, 1)
            ->get();

        $result = [];
        foreach ($addresses as $address) {
            $result[] = [
                'id' => $address->getId(),
                'chain_id' => $address->getChainId(),
                'address' => $address->getAddress(),
                'memo' => $address->getMemo(),
                'created_at' => $address->getCreatedAt()->format('Y-m-d H:i:s'),
            ];
        }

        return $result;
    }



    /**
     * 禁用地址
     */
    public function disableAddress(int $addressId): bool
    {
        $address = UserDepositAddress::find($addressId);
        if (!$address) {
            throw new BusinessException(ResultCode::FAIL, '地址记录不存在');
        }

        $address->setStatus(2); // 禁用状态
        return $address->save();
    }
}
