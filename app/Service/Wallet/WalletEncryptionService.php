<?php

declare(strict_types=1);

namespace App\Service\Wallet;

use App\Exception\BusinessException;
use App\Http\Common\ResultCode;

class WalletEncryptionService
{
    private string $publicKeyPath;
    private string $privateKeyPath;

    public function __construct()
    {
        $this->publicKeyPath = BASE_PATH . '/config/wallet/public_key.pem';
        $this->privateKeyPath = BASE_PATH . '/config/wallet/private_key.pem';
        
        $this->validateKeyFiles();
    }

    /**
     * 验证密钥文件是否存在
     */
    private function validateKeyFiles(): void
    {
        if (!file_exists($this->publicKeyPath)) {
            throw new BusinessException(
                ResultCode::FAIL, 
                '公钥文件不存在，请先运行命令生成密钥对: php bin/hyperf.php wallet:generate-keys'
            );
        }

        if (!file_exists($this->privateKeyPath)) {
            throw new BusinessException(
                ResultCode::FAIL, 
                '私钥文件不存在，请先运行命令生成密钥对: php bin/hyperf.php wallet:generate-keys'
            );
        }
    }

    /**
     * 加密私钥
     */
    public function encryptPrivateKey(string $privateKey): string
    {
        try {
            $publicKey = file_get_contents($this->publicKeyPath);
            if (!$publicKey) {
                throw new BusinessException(ResultCode::FAIL, '读取公钥文件失败');
            }

            $encrypted = '';
            $result = openssl_public_encrypt($privateKey, $encrypted, $publicKey);
            
            if (!$result) {
                throw new BusinessException(
                    ResultCode::FAIL, 
                    '私钥加密失败: ' . openssl_error_string()
                );
            }

            return base64_encode($encrypted);
            
        } catch (\Exception $e) {
            if ($e instanceof BusinessException) {
                throw $e;
            }
            throw new BusinessException(ResultCode::FAIL, '私钥加密过程中发生错误: ' . $e->getMessage());
        }
    }

    /**
     * 解密私钥
     */
    public function decryptPrivateKey(string $encryptedPrivateKey): string
    {
        try {
            $privateKey = file_get_contents($this->privateKeyPath);
            if (!$privateKey) {
                throw new BusinessException(ResultCode::FAIL, '读取私钥文件失败');
            }

            $encrypted = base64_decode($encryptedPrivateKey);
            if ($encrypted === false) {
                throw new BusinessException(ResultCode::FAIL, '加密私钥格式错误');
            }

            $decrypted = '';
            $result = openssl_private_decrypt($encrypted, $decrypted, $privateKey);
            
            if (!$result) {
                throw new BusinessException(
                    ResultCode::FAIL, 
                    '私钥解密失败: ' . openssl_error_string()
                );
            }

            return $decrypted;
            
        } catch (\Exception $e) {
            if ($e instanceof BusinessException) {
                throw $e;
            }
            throw new BusinessException(ResultCode::FAIL, '私钥解密过程中发生错误: ' . $e->getMessage());
        }
    }

    /**
     * 验证密钥对是否匹配
     */
    public function validateKeyPair(): bool
    {
        try {
            $testData = 'test_encryption_' . time();
            $encrypted = $this->encryptPrivateKey($testData);
            $decrypted = $this->decryptPrivateKey($encrypted);
            
            return $testData === $decrypted;
            
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取公钥内容
     */
    public function getPublicKey(): string
    {
        $publicKey = file_get_contents($this->publicKeyPath);
        if (!$publicKey) {
            throw new BusinessException(ResultCode::FAIL, '读取公钥文件失败');
        }
        return $publicKey;
    }

    /**
     * 获取私钥内容（仅用于内部验证，不对外暴露）
     */
    private function getPrivateKey(): string
    {
        $privateKey = file_get_contents($this->privateKeyPath);
        if (!$privateKey) {
            throw new BusinessException(ResultCode::FAIL, '读取私钥文件失败');
        }
        return $privateKey;
    }
}
