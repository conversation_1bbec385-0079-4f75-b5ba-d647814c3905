<?php

declare(strict_types=1);

namespace App\Service\UserAccounts;

use App\Enum\AsyncExecutorKey;
use App\Enum\UserAssets\UserAssetsFlowsCacheKey;
use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Job\User\UserAssetsChangedCacheJob;
use App\Model\User\UserAccountsAsset;
use App\Model\User\UserAccountsFlow;
use App\Service\RedisFactory\CacheRedis;
use Carbon\Carbon;
use Hyperf\Database\Exception\QueryException;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Logger\LoggerFactory;
use Psr\Log\LoggerInterface;

/**
 * 用户账户资金变动服务类
 */
class UserAccountsAssetService
{
    use UserAssetsTrait;

    protected LoggerInterface $logger;

    #[Inject]
    protected UserAssetsRedisService $redisService;

    #[Inject]
    protected CacheRedis $redis;

    public const DEFAULT_PERCISION = 8; //默认数据精度
    public const BALANCE_TOLERANCE = '0.********'; //余额对比容错值

    // 是否使用Redis进行资金操作
    protected bool $useRedis = true;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('user-accounts','account-logs');
    }



    /**
     * 检查余额是否足够（包含容错）
     */
    public function isBalanceSufficient(float $balance, float $amount): bool
    {
        // 计算差额
        $difference = bcsub((string)$balance, (string)$amount, self::DEFAULT_PERCISION);

        // 如果差额大于等于0，余额足够
        if (bccomp($difference, '0', self::DEFAULT_PERCISION) >= 0) {
            return true;
        }

        // 如果差额为负，但绝对值小于等于容错值，认为余额足够
        $absDifference = bcmul($difference, '-1', self::DEFAULT_PERCISION);
        return bccomp($absDifference, self::BALANCE_TOLERANCE, self::DEFAULT_PERCISION) <= 0;
    }

    public function triggerCacheUpdate(int $userId, int $accountType, int $currencyId): void
    {
        go(function () use ($userId, $accountType, $currencyId) {
            pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value, new UserAssetsChangedCacheJob($userId, $accountType, $currencyId), 3);
        });
    }

    /**
     * 冻结用户资金
     * 将指定字段资金转移到指定冻结字段中
     */
    public function freezeAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'available',
        string $frozenField = 'frozen'
    ): bool {
        if ($this->useRedis) {
            return $this->redisService->freezeAsset($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField, $frozenField);
        }

        return $this->freezeAssetWithMysql($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField, $frozenField);
    }



    /**
     * 使用MySQL冻结用户资金（原有逻辑）
     */
    public function freezeAssetWithMysql(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'available',
        string $frozenField = 'frozen',
        bool $createFlow = true
    ): bool {
        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField, $frozenField, $createFlow) {
            // 获取用户账户资产记录（加锁）
            $asset = UserAccountsAsset::query()
                ->where('user_id', $userId)
                ->where('account_type', $accountType)
                ->where('currency_id', $currencyId)
                ->lockForUpdate()
                ->first();

            if (!$asset) {
                throw new \RuntimeException('用户账户不存在');
            }

            // 检查指定字段余额是否足够（包含容错）
            if (!$this->isBalanceSufficient($asset->{$assetField}, $amount)) {
                throw new \RuntimeException('余额不足');
            }

            // 记录变动前的余额
            $beforeAsset = $asset->{$assetField};
            $beforeFrozen = $asset->{$frozenField};

            // 更新资产余额
            $asset->{$assetField} = (float)bcsub((string)$asset->{$assetField}, (string)$amount, self::DEFAULT_PERCISION);
            $asset->{$frozenField} = (float)bcadd((string)$asset->{$frozenField}, (string)$amount, self::DEFAULT_PERCISION);

            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录资金减少流水
            if ($createFlow) {
                $this->createFlow(
                    $userId,
                    $asset->id,
                    $currencyId,
                    $flowType,
                    $amount,
                    $beforeAsset,
                    $asset->{$assetField},
                    -1, // 减少
                    $relatedId
                );

                // 记录冻结资金增加流水
                $this->createFlow(
                    $userId,
                    $asset->id,
                    $currencyId,
                    $flowType,
                    $amount,
                    $beforeFrozen,
                    $asset->{$frozenField},
                    1, // 增加
                    $relatedId
                );
            }

            $this->triggerCacheUpdate($userId,$accountType,$currencyId);

            return true;
        });
    }

    /**
     * 解冻用户资金
     * 将指定冻结字段资金转移到指定目标字段中
     */
    public function unfreezeAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $frozenField = 'frozen',
        string $targetField = 'available'
    ): bool {
        if ($this->useRedis) {
            return $this->redisService->unfreezeAsset($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $frozenField, $targetField);
        }

        return $this->unfreezeAssetWithMysql($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $frozenField, $targetField);
    }



    /**
     * 使用MySQL解冻用户资金（原有逻辑）
     */
    public function unfreezeAssetWithMysql(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $frozenField = 'frozen',
        string $targetField = 'available',
        bool $createFlow = true
    ): bool {
        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $frozenField, $targetField, $createFlow) {
            // 获取用户账户资产记录（加锁）
            $asset = UserAccountsAsset::query()
                ->where('user_id', $userId)
                ->where('account_type', $accountType)
                ->where('currency_id', $currencyId)
                ->lockForUpdate()
                ->first();

            if (!$asset) {
                throw new \RuntimeException('用户账户不存在');
            }

            // 检查指定字段余额是否足够（包含容错）
            if (!$this->isBalanceSufficient($asset->{$frozenField}, $amount)) {
                throw new \RuntimeException('余额不足');
            }

            // 记录变动前的余额
            $beforeTarget = $asset->{$targetField};
            $beforeFrozen = $asset->{$frozenField};

            // 更新资产余额
            $asset->{$frozenField} = (float)bcsub((string)$asset->{$frozenField}, (string)$amount, self::DEFAULT_PERCISION);
            $asset->{$targetField} = (float)bcadd((string)$asset->{$targetField}, (string)$amount, self::DEFAULT_PERCISION);

            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录资金减少流水
            if ($createFlow) {
                $this->createFlow(
                    $userId,
                    $asset->id,
                    $currencyId,
                    $flowType,
                    $amount,
                    $beforeFrozen,
                    $asset->{$frozenField},
                    -1, // 减少
                    $relatedId
                );

                // 记录目标字段增加流水
                $this->createFlow(
                    $userId,
                    $asset->id,
                    $currencyId,
                    $flowType,
                    $amount,
                    $beforeTarget,
                    $asset->{$targetField},
                    1, // 增加
                    $relatedId
                );
            }

            $this->triggerCacheUpdate($userId,$accountType,$currencyId);

            return true;
        });
    }

    /**
     * 扣减冻结资金
     * 直接从冻结资金中扣除
     */
    public function deductFrozenAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'frozen'
    ): bool {
        if ($this->useRedis) {
            return $this->redisService->deductAsset($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField);
        }

        return $this->deductFrozenAssetWithMysql($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField);
    }

    /**
     * 增加用户可用资金
     */
    public function addAvailableAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'available'
    ): bool {
        if ($this->useRedis) {
            return $this->redisService->addAsset($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField);
        }

        return $this->addAvailableAssetWithMysql($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField);
    }



    /**
     * 减少借款金额
     */
    public function reduceBorrowedAmount(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0
    ): bool {
        if ($this->useRedis) {
            return $this->redisService->reduceBorrowedAmount($userId, $accountType, $currencyId, $amount, $flowType, $relatedId);
        }

        throw new \RuntimeException('MySQL减少借款金额方法待实现');
    }

    /**
     * 增加利息金额
     */
    public function addInterestAmount(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0
    ): bool {
        if ($this->useRedis) {
            return $this->redisService->addInterestAmount($userId, $accountType, $currencyId, $amount, $flowType, $relatedId);
        }

        return $this->addInterestAmountWithMysql($userId, $accountType, $currencyId, $amount, $flowType, $relatedId);
    }

    /**
     * 使用MySQL增加利息金额
     */
    public function addInterestAmountWithMysql(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        bool $createFlow = true
    ): bool {
        if (bccomp((string)$amount, '0', self::DEFAULT_PERCISION) <= 0) {
            throw new \InvalidArgumentException('利息金额必须大于0');
        }

        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $createFlow) {
            // 获取或创建用户账户资产记录（加锁）
            $asset = $this->getOrCreateAsset($userId, $accountType, $currencyId);

            // 记录变动前的余额
            $beforeAmount = $asset->interest_amount ?? 0;

            // 更新利息金额
            $asset->interest_amount = (float)bcadd((string)$beforeAmount, (string)$amount, self::DEFAULT_PERCISION);

            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录利息增加流水
            if ($createFlow) {
                $this->createFlow(
                    $userId,
                    $asset->id,
                    $currencyId,
                    $flowType,
                    $amount,
                    $beforeAmount,
                    $asset->interest_amount,
                    1, // 增加
                    $relatedId
                );
            }

            $this->triggerCacheUpdate($userId, $accountType, $currencyId);

            return true;
        });
    }

    /**
     * 使用MySQL扣减冻结资金（原有逻辑）
     */
    public function deductFrozenAssetWithMysql(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'frozen'
    ): bool {
        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField) {
            // 获取用户账户资产记录（加锁）
            $asset = UserAccountsAsset::query()
                ->where('user_id', $userId)
                ->where('account_type', $accountType)
                ->where('currency_id', $currencyId)
                ->lockForUpdate()
                ->first();

            if (!$asset) {
                throw new \RuntimeException('用户账户不存在');
            }

            // 检查指定字段余额是否足够（包含容错）
            if (!$this->isBalanceSufficient($asset->{$assetField}, $amount)) {
                throw new \RuntimeException('余额不足');
            }

            // 记录变动前的余额
            $beforeAmount = $asset->{$assetField};

            // 更新资产余额
            $asset->{$assetField} = (float)bcsub((string)$asset->{$assetField}, (string)$amount, self::DEFAULT_PERCISION);
            
            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录资金减少流水
            $this->createFlow(
                $userId,
                $asset->id,
                $currencyId,
                $flowType,
                $amount,
                $beforeAmount,
                $asset->{$assetField},
                -1, // 减少
                $relatedId
            );

            $this->triggerCacheUpdate($userId,$accountType,$currencyId);

            return true;
        });
    }




    /**
     * 使用MySQL增加用户可用资金（原有逻辑）
     */
    public function addAvailableAssetWithMysql(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'available',
        bool $createFlow = true
    ): bool {
        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField, $createFlow) {
            // 获取或创建用户账户资产记录（加锁）
            $asset = $this->getOrCreateAsset($userId, $accountType, $currencyId);

            // 记录变动前的余额
            $beforeAmount = $asset->{$assetField};

            // 更新资产余额
            $asset->{$assetField} = (float)bcadd((string)$asset->{$assetField}, (string)$amount, self::DEFAULT_PERCISION);
            
            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录资金增加流水
            if ($createFlow) {
                $this->createFlow(
                    $userId,
                    $asset->id,
                    $currencyId,
                    $flowType,
                    $amount,
                    $beforeAmount,
                    $asset->{$assetField},
                    1, // 增加
                    $relatedId
                );
            }

            $this->triggerCacheUpdate($userId,$accountType,$currencyId);

            return true;
        });
    }

    /**
     * 扣减用户可用资金
     */
    public function deductAvailableAsset(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'available'
    ): bool {
        if ($this->useRedis) {
            return $this->redisService->deductAsset($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField);
        }

        return $this->deductAvailableAssetWithMysql($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField);
    }

    /**
     * 使用MySQL扣减用户可用资金（原有逻辑）
     */
    public function deductAvailableAssetWithMysql(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'available',
        bool $createFlow = true
    ): bool {
        if (bccomp((string)$amount, '0', self::DEFAULT_PERCISION) <= 0) {
            throw new \InvalidArgumentException('扣减金额必须大于0');
        }

        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField, $createFlow) {
            // 获取用户账户资产记录（加锁）
            $asset = UserAccountsAsset::query()
                ->where('user_id', $userId)
                ->where('account_type', $accountType)
                ->where('currency_id', $currencyId)
                ->lockForUpdate()
                ->first();

            if (!$asset) {
                throw new \RuntimeException('用户账户不存在');
            }

            if((float)$asset->{$assetField} <= 0){
                return false;
            }

            // 检查指定字段余额是否足够（包含容错）
            if (!$this->isBalanceSufficient($asset->{$assetField}, $amount)) {
                //throw new \RuntimeException('余额不足');
                $amount = $asset->{$assetField};
            }

            // 记录变动前的余额
            $beforeAmount = $asset->{$assetField};

            // 更新资产余额
            $asset->{$assetField} = (float)bcsub((string)$asset->{$assetField}, (string)$amount, self::DEFAULT_PERCISION);

            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录资金减少流水
            if ($createFlow) {
                $this->createFlow(
                    $userId,
                    $asset->id,
                    $currencyId,
                    $flowType,
                    $amount,
                    $beforeAmount,
                    $asset->{$assetField},
                    -1, // 减少
                    $relatedId
                );
            }

            $this->triggerCacheUpdate($userId,$accountType,$currencyId);

            return true;
        });
    }

    /**
     * 增加用户借款金额
     */
    public function addBorrowedAmount(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'borrowed_amount',
        string $availableField = 'available'
    ): bool {
        if (bccomp((string)$amount, '0', self::DEFAULT_PERCISION) <= 0) {
            throw new \InvalidArgumentException('借款金额必须大于0');
        }

        if($this->useRedis){
            return $this->redisService->addBorrowedAmount($userId,$accountType,$currencyId,$amount,$flowType,$relatedId);
        }

        return $this->addBorrowedAmountWithMysql($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField, $availableField);
    }

    /**
     * 使用MySQL增加借款金额（原有逻辑）
     */
    public function addBorrowedAmountWithMysql(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'borrowed_amount',
        string $availableField = 'available',
        bool $createFlow = true
    ): bool {
        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField, $availableField, $createFlow) {
            // 获取或创建用户账户资产记录（加锁）
            $asset = $this->getOrCreateAsset($userId, $accountType, $currencyId);

            // 记录变动前的余额
            $beforeAmount = $asset->{$assetField} ?? 0;
            $beforeAvailable = $asset->{$availableField};

            // 更新指定字段和可用余额
            $asset->{$assetField} = (float)bcadd((string)$beforeAmount, (string)$amount, self::DEFAULT_PERCISION);
            $asset->{$availableField} = (float)bcadd((string)$asset->{$availableField}, (string)$amount, self::DEFAULT_PERCISION);

            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录指定字段增加流水
            if ($createFlow) {
                $this->createFlow(
                    $userId,
                    $asset->id,
                    $currencyId,
                    $flowType,
                    $amount,
                    $beforeAmount,
                    $asset->{$assetField},
                    1, // 增加
                    $relatedId
                );

                // 记录可用资金增加流水
                $this->createFlow(
                    $userId,
                    $asset->id,
                    $currencyId,
                    $flowType,
                    $amount,
                    $beforeAvailable,
                    $asset->{$availableField},
                    1, // 增加
                    $relatedId
                );
            }

            $this->triggerCacheUpdate($userId, $accountType, $currencyId);

            return true;
        });
    }

    /**
     * 使用MySQL减少借款金额
     */
    public function reduceBorrowedAmountWithMysql(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount,
        int $flowType,
        int $relatedId = 0,
        string $assetField = 'borrowed_amount',
        bool $createFlow = true
    ): bool {
        if (bccomp((string)$amount, '0', self::DEFAULT_PERCISION) <= 0) {
            throw new \InvalidArgumentException('还款金额必须大于0');
        }

        return Db::transaction(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $relatedId, $assetField, $createFlow) {
            // 获取用户账户资产记录（加锁）
            $asset = UserAccountsAsset::query()
                ->where('user_id', $userId)
                ->where('account_type', $accountType)
                ->where('currency_id', $currencyId)
                ->lockForUpdate()
                ->first();

            if (!$asset) {
                throw new \RuntimeException('用户账户不存在');
            }

            $beforeAmount = $asset->{$assetField} ?? 0;
            $beforeAvailable = $asset->available;

            // 检查指定字段金额是否足够（包含容错）
            if (!$this->isBalanceSufficient($beforeAmount, $amount)) {
                throw new \RuntimeException('余额不足');
            }

            // 检查可用余额是否足够（包含容错）
            if (!$this->isBalanceSufficient($asset->available, $amount)) {
                throw new \RuntimeException('可用余额不足');
            }

            // 更新指定字段和可用余额
            $asset->{$assetField} = (float)bcsub((string)$beforeAmount, (string)$amount, self::DEFAULT_PERCISION);
            $asset->available = (float)bcsub((string)$asset->available, (string)$amount, self::DEFAULT_PERCISION);
            
            if (!$asset->save()) {
                throw new \RuntimeException('更新账户资产失败');
            }

            // 记录指定字段减少流水
            if ($createFlow) {
                $this->createFlow(
                    $userId,
                    $asset->id,
                    $currencyId,
                    $flowType,
                    $amount,
                    $beforeAmount,
                    $asset->{$assetField},
                    -1, // 减少
                    $relatedId
                );

                // 记录可用资金减少流水
                $this->createFlow(
                    $userId,
                    $asset->id,
                    $currencyId,
                    $flowType,
                    $amount,
                    $beforeAvailable,
                    $asset->available,
                    -1, // 减少
                    $relatedId
                );
            }

            $this->triggerCacheUpdate($userId,$accountType,$currencyId);

            return true;
        });
    }



    /**
     * 获取用户账户资产
     */
    public function getUserAsset(int $userId, int $accountType, int $currencyId): ?UserAccountsAsset
    {
        if ($this->useRedis) {
            $redisAsset = $this->redisService->getUserAsset($userId, $accountType, $currencyId);
            // 创建一个模拟的UserAccountsAsset对象
            $asset = new UserAccountsAsset();
            if ($redisAsset) {
                $asset->user_id = $userId;
                $asset->account_type = $accountType;
                $asset->currency_id = $currencyId;
                $asset->available = $redisAsset['available'];
                $asset->frozen = $redisAsset['frozen'];
                $asset->locked = $redisAsset['locked'];
                $asset->margin_quote = $redisAsset['margin_quote'];
                $asset->borrowed_amount = $redisAsset['borrowed_amount'];
                $asset->interest_amount = $redisAsset['interest_amount'];
                $asset->status = 1;
            }else{
                $asset->user_id = $userId;
                $asset->account_type = $accountType;
                $asset->currency_id = $currencyId;
                $asset->available = 0.0;
                $asset->frozen = 0.0;
                $asset->status = 1;
            }
            return $asset;
        }

        return UserAccountsAsset::query()
            ->where('user_id', $userId)
            ->where('account_type', $accountType)
            ->where('currency_id', $currencyId)
            ->first();
    }



    /**
     * 获取用户所有账户资产
     */
    public function getUserAssets(int $userId, ?int $accountType = null): array
    {
        if ($this->useRedis) {
            return $this->redisService->getUserAssets($userId, $accountType);
        }

        $query = UserAccountsAsset::query()->where('user_id', $userId);

        if ($accountType !== null) {
            $query->where('account_type', $accountType);
            $query->where('available', '>', 0);
            $query->where('status', '=', 1);
        }

        return $query->get()->toArray();
    }



    /**
     * 检查用户资金是否足够
     */
    public function checkAvailableBalance(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount
    ): bool {
        $asset = $this->getUserAsset($userId, $accountType, $currencyId);

        if (!$asset) {
            return false;
        }

        return bccomp((string)$asset->available, (string)$amount, self::DEFAULT_PERCISION) >= 0;
    }

    /**
     * 检查用户冻结资金是否足够
     */
    public function checkFrozenBalance(
        int $userId,
        int $accountType,
        int $currencyId,
        float $amount
    ): bool {
        $asset = $this->getUserAsset($userId, $accountType, $currencyId);

        if (!$asset) {
            return false;
        }

        return bccomp((string)$asset->frozen, (string)$amount, self::DEFAULT_PERCISION) >= 0;
    }

    /**
     * 获取或创建用户账户资产记录
     */
    protected function getOrCreateAsset(int $userId, int $accountType, int $currencyId): UserAccountsAsset
    {
        $asset = UserAccountsAsset::query()
            ->where('user_id', $userId)
            ->where('account_type', $accountType)
            ->where('currency_id', $currencyId)
            ->lockForUpdate()
            ->first();

        if (!$asset) {
            $asset = new UserAccountsAsset();
            $asset->user_id = $userId;
            $asset->account_type = $accountType;
            $asset->currency_id = $currencyId;
            $asset->available = 0.000000000000000000;
            $asset->frozen = 0.000000000000000000;
            $asset->locked = 0.000000000000000000;
            $asset->borrowed_amount = 0.000000000000000000;
            $asset->interest_amount = 0.000000000000000000;
            $asset->status = 1; // 正常状态

            if (!$asset->save()) {
                throw new \RuntimeException('创建账户资产记录失败');
            }
        }

        return $asset;
    }

    /**
     * 创建资金流水记录
     * 直接写入Redis ZSET队列，使用微秒时间戳作为score
     */
    protected function createFlow(
        int $userId,
        int $accountId,
        int $currencyId,
        int $type,
        float|string $amount,
        float|string $before,
        float|string $after,
        int $direction,
        int $relatedId = 0
    ): void {
        // 构建流水数据
        $flowData = [
            'user_id' => $userId,
            'account_id' => $accountId,
            'currency_id' => $currencyId,
            'type' => $type,
            'amount' => (float)$amount,
            'before' => (float)$before,
            'after' => (float)$after,
            'direction' => $direction,
            'related_id' => $relatedId,
            'created_at' => Carbon::now()->toDateTimeString()
        ];

        // 使用微秒时间戳作为score，确保时间顺序
        $score = microtime(true) * 100000;

        // 生成唯一的member标识
        $flowId = UserAssetsFlowsCacheKey::generateFlowId();
        $member = $flowId . '|' . json_encode($flowData);

        // 添加到Redis ZSET队列
        $queueKey = UserAssetsFlowsCacheKey::getFlowsQueueKey();
        $this->redis->zAdd($queueKey, $score, $member);
    }

    /**
     * 异步队列执行保存流水
     * @param array $flowsData
     * @return bool
     */
    public static function saveAssetsFlow(array $flowsData): bool
    {
        try {
            return (new UserAccountsFlow())->fill($flowsData)->save();
        }catch (QueryException $e){
            //mysql 执行语法错误直接丢弃
            logger('flows','flowsjob/flows.log')->info("数据流水执行异常：".json_encode($flowsData));
            return true;
        } catch (\Throwable $t){
            //其他异常继续执行
            throw new BusinessException(ResultCode::FAIL,$t->getMessage());
        }
    }
}
