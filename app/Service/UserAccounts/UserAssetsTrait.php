<?php

namespace App\Service\UserAccounts;

use App\Enum\UserAssets\UserAssetsFlowsCacheKey;
use Carbon\Carbon;

trait UserAssetsTrait
{
    protected function createFlow(
        int $userId,
        int $accountType,
        int $currencyId,
        int $type,
        float|string $amount,
        float|string $before,
        float|string $after,
        int $direction,
        int $relatedId = 0
    ): void {
        // 构建流水数据
        $flowData = [
            'user_id' => $userId,
            'account_type' => $accountType,
            'currency_id' => $currencyId,
            'type' => $type,
            'amount' => (float)$amount,
            'before' => (float)$before,
            'after' => (float)$after,
            'direction' => $direction,
            'related_id' => $relatedId,
            'created_at' => Carbon::now()->toDateTimeString()
        ];

        // 使用微秒时间戳作为score，确保时间顺序
        $score = microtime(true) * 100000;

        // 生成唯一的member标识
        $flowId = UserAssetsFlowsCacheKey::generateFlowId();
        $member = $flowId . '|' . json_encode($flowData);

        // 添加到Redis ZSET队列
        $queueKey = UserAssetsFlowsCacheKey::getFlowsQueueKey();
        $this->redis->zAdd($queueKey, $score, $member);
    }

    /**
     * 同步数据库资产操作
     * 将Redis操作对应的MySQL操作加入异步队列
     *
     * @param array $data 包含callback和方法参数的数组
     */
    protected function syncDbAssets(array $data): void
    {
        // 使用微秒时间戳 * 100000 作为score
        $score = microtime(true) * 100000;

        // 将数据编码为JSON
        $member = json_encode($data);
        // 添加到资产同步队列
        $queueKey = UserAssetsFlowsCacheKey::getAssetsSyncQueue();
        $this->redis->zAdd($queueKey, $score, $member);
    }
}