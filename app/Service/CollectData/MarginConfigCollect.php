<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆配置数据收集服务
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\Service\CollectData;

use App\Enum\Config\MarginConfigKey;
use App\Model\Currency\Currency;
use App\Model\Currency\CurrencyMarginBorrow;
use App\Model\Trade\TradeMarginBracket;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Psr\Http\Message\ResponseInterface;

/**
 * 杠杆配置数据收集
 */
class MarginConfigCollect
{
    /**
     * 币安杠杆币种列表接口
     */
    private const BINANCE_MARGIN_PAIRS_URL = 'https://www.binance.com/bapi/margin/v1/friendly/isolated-margin/pair/listed';

    /**
     * 币安杠杆档位数据接口
     */
    private const BINANCE_MARGIN_LEVELS_URL = 'https://www.binance.com/bapi/margin/v1/friendly/isolated-margin/pair/vip-level';

    /**
     * 币安全仓杠杆借贷数据接口
     */
    private const BINANCE_CROSS_MARGIN_URL = 'https://www.binance.com/bapi/margin/v1/friendly/margin/vip/spec/list-all';

    /**
     * 币安全仓杠杆风险档位数据接口
     */
    private const BINANCE_MARGIN_BRACKETS_URL = 'https://www.binance.com/bapi/margin/v1/public/margin/asset-leverage-brackets';

    /**
     * 币安逐仓杠杆档位配置数据接口
     */
    private const BINANCE_ISOLATED_TIER_CONFIG_URL = 'https://www.binance.com/bapi/margin/v1/public/isolated-margin/vip-tier-config/';

    /**
     * 同步币安杠杆借贷配置数据
     *
     * @return bool 同步是否成功
     * @throws \Throwable
     */
    public function syncBinanceMarginConfig(): bool
    {
        try {
            logger('margin_sync')->info('开始同步币安杠杆借贷配置数据');

            // 步骤1：获取币安杠杆币种列表
            $pairData = $this->fetchBinanceMarginPairs();
            if (empty($pairData)) {
                logger('margin_sync')->error('获取币安杠杆币种列表失败');
                return false;
            }

            // 步骤2：获取币安杠杆档位数据
            $levelData = $this->fetchBinanceMarginLevels();
            if (empty($levelData)) {
                logger('margin_sync')->error('获取币安杠杆档位数据失败');
                return false;
            }

            // 步骤3：保存数据到数据库
            $result = $this->saveMarginConfig($pairData, $levelData);

            if ($result) {
                logger('margin_sync')->info('币安杠杆借贷配置数据同步成功');
            } else {
                logger('margin_sync')->error('币安杠杆借贷配置数据保存失败');
            }

            return $result;

        } catch (\Throwable $e) {
            logger('margin_sync')->error('同步币安杠杆借贷配置数据异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 获取币安杠杆币种列表
     *
     * @return array symbol => [is_base_borrow, is_quote_borrow] 的映射
     */
    private function fetchBinanceMarginPairs(): array
    {
        try {
            $client = client([
                'timeout' => 30,
                'headers' => [
                    'Referer' => 'https://www.binance.com/',
                    'Origin' => 'https://www.binance.com',
                ]
            ]);

            $response = $client->get(self::BINANCE_MARGIN_PAIRS_URL);

            if ($response->getStatusCode() !== 200) {
                logger('margin_sync')->error('获取币安杠杆币种列表HTTP状态码异常', [
                    'status_code' => $response->getStatusCode(),
                    'response_body' => $response->getBody()->getContents()
                ]);
                return [];
            }

            $data = json_decode($response->getBody()->getContents(), true);

            if (!$data || $data['code'] !== '000000' || !isset($data['data'])) {
                logger('margin_sync')->error('币安杠杆币种列表接口返回数据格式异常', [
                    'response_data' => $data
                ]);
                return [];
            }

            $pairMapping = [];
            foreach ($data['data'] as $pair) {
                $symbol = $pair['symbol'] ?? '';
                if (empty($symbol)) {
                    continue;
                }

                $pairMapping[$symbol] = [
                    'is_base_borrow' => ($pair['isBaseBorrowable'] ?? false) ? 1 : 0,
                    'is_quote_borrow' => ($pair['isQuoteBorrowable'] ?? false) ? 1 : 0,
                ];
            }

            logger('margin_sync')->info('成功获取币安杠杆币种列表', [
                'total_pairs' => count($pairMapping)
            ]);

            return $pairMapping;

        } catch (\Throwable $e) {
            logger('margin_sync')->error('获取币安杠杆币种列表异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return [];
        }
    }

    /**
     * 获取币安杠杆档位数据
     *
     * @return array symbol => [base_level, quote_level] 的映射
     */
    private function fetchBinanceMarginLevels(): array
    {
        try {
            $client = client([
                'timeout' => 30,
                'headers' => [
                    'Referer' => 'https://www.binance.com/',
                    'Origin' => 'https://www.binance.com',
                ]
            ]);

            $response = $client->get(self::BINANCE_MARGIN_LEVELS_URL);

            if ($response->getStatusCode() !== 200) {
                logger('margin_sync')->error('获取币安杠杆档位数据HTTP状态码异常', [
                    'status_code' => $response->getStatusCode(),
                    'response_body' => $response->getBody()->getContents()
                ]);
                return [];
            }

            $data = json_decode($response->getBody()->getContents(), true);

            if (!$data || $data['code'] !== '000000' || !isset($data['data'])) {
                logger('margin_sync')->error('币安杠杆档位数据接口返回数据格式异常', [
                    'response_data' => $data
                ]);
                return [];
            }

            $levelMapping = [];
            foreach ($data['data'] as $level) {
                $symbol = $level['symbol'] ?? '';
                if (empty($symbol)) {
                    continue;
                }

                $levelMapping[$symbol] = [
                    'base_level' => $level['base']['levelDetails'] ?? [],
                    'quote_level' => $level['quote']['levelDetails'] ?? [],
                ];
            }

            logger('margin_sync')->info('成功获取币安杠杆档位数据', [
                'total_symbols' => count($levelMapping)
            ]);

            return $levelMapping;

        } catch (\Throwable $e) {
            logger('margin_sync')->error('获取币安杠杆档位数据异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return [];
        }
    }

    /**
     * 保存杠杆配置数据到数据库
     *
     * @param array $pairData 币种借贷配置映射
     * @param array $levelData 档位数据映射
     * @return bool
     * @throws \Throwable
     */
    private function saveMarginConfig(array $pairData, array $levelData): bool
    {
        try {
            return Db::transaction(function () use ($pairData, $levelData) {
                $savedCount = 0;
                $skippedCount = 0;

                // 获取本地币种映射 symbol => currency_id
                $currencyMapping = Currency::query()
                    ->pluck('id', 'symbol')
                    ->toArray();

                logger('margin_sync')->info('开始保存杠杆配置数据', [
                    'pair_count' => count($pairData),
                    'level_count' => count($levelData),
                    'local_currency_count' => count($currencyMapping)
                ]);

                // 合并两个数据源
                $allSymbols = array_unique(array_merge(array_keys($pairData), array_keys($levelData)));

                foreach ($allSymbols as $symbol) {
                    // 检查本地是否存在该币种
                    if (!isset($currencyMapping[$symbol])) {
                        $skippedCount++;
                        logger('margin_sync')->debug('跳过不存在的币种', ['symbol' => $symbol]);
                        continue;
                    }

                    $currencyId = $currencyMapping[$symbol];
                    $pairInfo = $pairData[$symbol] ?? ['is_base_borrow' => 0, 'is_quote_borrow' => 0];
                    $levelInfo = $levelData[$symbol] ?? ['base_level' => [], 'quote_level' => []];

                                         // 准备保存的数据
                     $marginData = [
                         'currency_id' => $currencyId,
                         'is_cross' => 2, // 逐仓
                         'base_level' => $levelInfo['base_level'],
                         'quote_level' => $levelInfo['quote_level'],
                         'is_base_borrow' => $pairInfo['is_base_borrow'],
                         'is_quote_borrow' => $pairInfo['is_quote_borrow'],
                         'updated_at' => Carbon::now(),
                     ];

                    // 检查记录是否存在
                    $existingRecord = CurrencyMarginBorrow::query()
                        ->where('currency_id', $currencyId)
                        ->where('is_cross', 2)
                        ->first();

                    if ($existingRecord) {
                        // 更新现有记录
                        $existingRecord->update($marginData);
                                         } else {
                         // 创建新记录
                         $marginData['created_at'] = Carbon::now();
                         CurrencyMarginBorrow::query()->create($marginData);
                     }

                    $savedCount++;
                }

                logger('margin_sync')->info('杠杆配置数据保存完成', [
                    'saved_count' => $savedCount,
                    'skipped_count' => $skippedCount,
                    'total_processed' => count($allSymbols)
                ]);

                return $savedCount > 0;
            });

        } catch (\Throwable $e) {
            logger('margin_sync')->error('保存杠杆配置数据异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw $e;
        }
    }

    /**
     * 同步币安全仓杠杆借贷配置数据
     *
     * @return bool 同步是否成功
     * @throws \Throwable
     */
    public function syncBinanceCrossMarginConfig(): bool
    {
        try {
            logger('margin_sync')->info('开始同步币安全仓杠杆借贷配置数据');

            // 获取币安全仓杠杆数据
            $crossMarginData = $this->fetchBinanceCrossMarginData();
            if (empty($crossMarginData)) {
                logger('margin_sync')->error('获取币安全仓杠杆数据失败');
                return false;
            }

            // 保存数据到数据库
            $result = $this->saveCrossMarginConfig($crossMarginData);

            if ($result) {
                logger('margin_sync')->info('币安全仓杠杆借贷配置数据同步成功');
            } else {
                logger('margin_sync')->error('币安全仓杠杆借贷配置数据保存失败');
            }

            return $result;

        } catch (\Throwable $e) {
            logger('margin_sync')->error('同步币安全仓杠杆借贷配置数据异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 获取币安全仓杠杆数据
     *
     * @return array assetName => specs 的映射
     */
    private function fetchBinanceCrossMarginData(): array
    {
        try {
            $client = client([
                'timeout' => 30,
                'headers' => [
                    'Referer' => 'https://www.binance.com/',
                    'Origin' => 'https://www.binance.com',
                ]
            ]);

            $response = $client->get(self::BINANCE_CROSS_MARGIN_URL);

            if ($response->getStatusCode() !== 200) {
                logger('margin_sync')->error('获取币安全仓杠杆数据HTTP状态码异常', [
                    'status_code' => $response->getStatusCode(),
                    'response_body' => $response->getBody()->getContents()
                ]);
                return [];
            }

            $data = json_decode($response->getBody()->getContents(), true);

            if (!$data || $data['code'] !== '000000' || !isset($data['data'])) {
                logger('margin_sync')->error('币安全仓杠杆数据接口返回数据格式异常', [
                    'response_data' => $data
                ]);
                return [];
            }

            $crossMarginMapping = [];
            foreach ($data['data'] as $asset) {
                $assetName = $asset['assetName'] ?? '';
                if (empty($assetName)) {
                    continue;
                }

                $crossMarginMapping[$assetName] = [
                    'specs' => $asset['specs'] ?? [],
                ];
            }

            logger('margin_sync')->info('成功获取币安全仓杠杆数据', [
                'total_assets' => count($crossMarginMapping)
            ]);

            return $crossMarginMapping;

        } catch (\Throwable $e) {
            logger('margin_sync')->error('获取币安全仓杠杆数据异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return [];
        }
    }

    /**
     * 保存全仓杠杆配置数据到数据库
     *
     * @param array $crossMarginData 全仓杠杆数据映射
     * @return bool
     * @throws \Throwable
     */
    private function saveCrossMarginConfig(array $crossMarginData): bool
    {
        try {
            return Db::transaction(function () use ($crossMarginData) {
                $savedCount = 0;
                $skippedCount = 0;

                // 获取本地币种映射 base_asset => currency_id
                $currencyMapping = Currency::query()
                    ->pluck('id', 'base_asset')
                    ->toArray();

                logger('margin_sync')->info('开始保存全仓杠杆配置数据', [
                    'cross_margin_count' => count($crossMarginData),
                    'local_currency_count' => count($currencyMapping)
                ]);

                foreach ($crossMarginData as $assetName => $assetData) {
                    // 检查本地是否存在该币种（通过base_asset字段匹配）
                    if (!isset($currencyMapping[$assetName])) {
                        $skippedCount++;
                        logger('margin_sync')->debug('跳过不存在的资产', ['asset_name' => $assetName]);
                        continue;
                    }

                    $currencyId = $currencyMapping[$assetName];
                    $specs = $assetData['specs'] ?? [];

                    // 准备保存的数据
                    $marginData = [
                        'currency_id' => $currencyId,
                        'is_cross' => 1, // 全仓
                        'base_level' => $specs,
                        'quote_level' => [], // 全仓杠杆quote_level为空数组
                        'is_base_borrow' => !empty($specs) ? 1 : 0, // 有specs数据说明可借
                        'is_quote_borrow' => !empty($specs) ? 1 : 0, // 有specs数据说明可借
                        'updated_at' => Carbon::now(),
                    ];

                    // 检查记录是否存在
                    $existingRecord = CurrencyMarginBorrow::query()
                        ->where('currency_id', $currencyId)
                        ->where('is_cross', 1)
                        ->first();

                    if ($existingRecord) {
                        // 更新现有记录
                        $existingRecord->update($marginData);
                    } else {
                        // 创建新记录
                        $marginData['created_at'] = Carbon::now();
                        CurrencyMarginBorrow::query()->create($marginData);
                    }

                    $savedCount++;
                }

                logger('margin_sync')->info('全仓杠杆配置数据保存完成', [
                    'saved_count' => $savedCount,
                    'skipped_count' => $skippedCount,
                    'total_processed' => count($crossMarginData)
                ]);

                return $savedCount > 0;
            });

        } catch (\Throwable $e) {
            logger('margin_sync')->error('保存全仓杠杆配置数据异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw $e;
        }
    }

    /**
     * 将杠杆配置数据写入Redis缓存
     *
     * @return bool 写入是否成功
     * @throws \Throwable
     */
    public function cacheMarginConfigToRedis(): bool
    {
        try {
            logger('margin_sync')->info('开始将杠杆配置数据写入Redis缓存');

            $cachedCount = 0;
            $errorCount = 0;

            // 获取状态为启用的币种的杠杆配置数据
            $marginConfigs = CurrencyMarginBorrow::query()
                ->join('currency', 'currency_margin_borrow.currency_id', '=', 'currency.id')
                ->where('currency.status', 1)
                ->get(['currency_margin_borrow.currency_id', 'currency_margin_borrow.is_cross', 'currency_margin_borrow.base_level', 'currency_margin_borrow.quote_level']);

            foreach ($marginConfigs as $config) {
                try {
                    $cacheKey = MarginConfigKey::getMarginBorrowKey($config->currency_id, $config->is_cross);
                    
                    if ($config->is_cross == 1) {
                        // 全仓：使用vipLevel作为hash field
                        $this->cacheCrossMarginData($cacheKey, $config->base_level);
                    } else {
                        // 逐仓：按index合并base_level和quote_level
                        $this->cacheIsolatedMarginData($cacheKey, $config->base_level, $config->quote_level);
                    }
                    
                    $cachedCount++;

                } catch (\Throwable $e) {
                    $errorCount++;
                    logger('margin_sync')->error('缓存单个杠杆配置失败', [
                        'currency_id' => $config->currency_id,
                        'is_cross' => $config->is_cross,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            logger('margin_sync')->info('杠杆配置数据写入Redis缓存完成', [
                'total_processed' => count($marginConfigs),
                'cached_count' => $cachedCount,
                'error_count' => $errorCount
            ]);

            return $errorCount == 0;

        } catch (\Throwable $e) {
            logger('margin_sync')->error('写入杠杆配置数据到Redis缓存异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw $e;
        }
    }

    /**
     * 缓存全仓杠杆数据
     *
     * @param string $cacheKey Redis缓存Key
     * @param array $baseLevel 全仓档位数据
     * @throws \Throwable
     */
    private function cacheCrossMarginData(string $cacheKey, array $baseLevel): void
    {
        try {
            $redis = redis('default');
            
            // 删除旧缓存
            $redis->del($cacheKey);
            
            // 将specs数据以index为field写入hash
            foreach ($baseLevel as $index => $spec) {
                if (empty($spec)) {
                    continue;
                }
                
                $redis->hSet($cacheKey, (string)$index, json_encode($spec));
            }

        } catch (\Throwable $e) {
            logger('margin_sync')->error('缓存全仓杠杆数据失败', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 缓存逐仓杠杆数据
     *
     * @param string $cacheKey Redis缓存Key
     * @param array $baseLevel 基础币档位数据
     * @param array $quoteLevel 计价币档位数据
     * @throws \Throwable
     */
    private function cacheIsolatedMarginData(string $cacheKey, array $baseLevel, array $quoteLevel): void
    {
        try {
            $redis = redis('default');
            
            // 删除旧缓存
            $redis->del($cacheKey);
            
            // 按index合并base_level和quote_level
            $maxLevels = max(count($baseLevel), count($quoteLevel));
            
            for ($i = 0; $i < $maxLevels; $i++) {
                $baseData = $baseLevel[$i] ?? [];
                $quoteData = $quoteLevel[$i] ?? [];
                
                // 合并数据，将字段名加上前缀
                $mergedData = [];
                
                // 处理base数据
                foreach ($baseData as $key => $value) {
                    if ($key === 'level') continue; // 跳过level字段
                    $mergedData['base_' . $this->convertFieldName($key)] = $value;
                }
                
                // 处理quote数据
                foreach ($quoteData as $key => $value) {
                    if ($key === 'level') continue; // 跳过level字段
                    $mergedData['quote_' . $this->convertFieldName($key)] = $value;
                }
                
                if (!empty($mergedData)) {
                    $redis->hSet($cacheKey, strval($i), json_encode($mergedData));
                }
            }

        } catch (\Throwable $e) {
            logger('margin_sync')->error('缓存逐仓杠杆数据失败', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 转换字段名称
     *
     * @param string $fieldName 原字段名
     * @return string 转换后的字段名
     */
    private function convertFieldName(string $fieldName): string
    {
        // 将字段名转换为符合要求的格式
        $fieldMap = [
            'maxBorrowable' => 'maxBorrowable',
            'interestRate' => 'interestRate',
            'dailyInterestRate' => 'interestRate',
            'borrowLimit' => 'maxBorrowable',
        ];

        return $fieldMap[$fieldName] ?? $fieldName;
    }

    /**
     * 清除杠杆配置Redis缓存
     *
     * @return bool 清除是否成功
     */
    public function clearMarginConfigCache(): bool
    {
        try {
            logger('margin_sync')->info('开始清除杠杆配置Redis缓存');

            $redis = redis();
            $pattern = MarginConfigKey::MARGIN_BORROW_PREFIX . ':*';
            
            // 获取所有匹配的key
            $keys = $redis->keys($pattern);
            
            if (!empty($keys)) {
                $deletedCount = $redis->del(...$keys);
                logger('margin_sync')->info('清除杠杆配置Redis缓存完成', [
                    'deleted_count' => $deletedCount
                ]);
            } else {
                logger('margin_sync')->info('没有找到需要清除的杠杆配置缓存');
            }

            return true;

        } catch (\Throwable $e) {
            logger('margin_sync')->error('清除杠杆配置Redis缓存异常', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取本地杠杆配置统计信息
     *
     * @return array
     */
    public function getMarginConfigStats(): array
    {
        try {
            $stats = [
                'total_records' => CurrencyMarginBorrow::query()->count(),
                'cross_margin_count' => CurrencyMarginBorrow::query()->where('is_cross', 1)->count(),
                'isolated_margin_count' => CurrencyMarginBorrow::query()->where('is_cross', 2)->count(),
                'base_borrowable_count' => CurrencyMarginBorrow::query()->where('is_base_borrow', 1)->count(),
                'quote_borrowable_count' => CurrencyMarginBorrow::query()->where('is_quote_borrow', 1)->count(),
                'last_updated' => CurrencyMarginBorrow::query()->latest('updated_at')->value('updated_at'),
            ];

            return $stats;

        } catch (\Throwable $e) {
            logger('margin_sync')->error('获取杠杆配置统计信息异常', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 同步币安全仓杠杆风险档位数据
     *
     * @return bool 同步是否成功
     * @throws \Throwable
     */
    public function syncBinanceMarginBrackets(): bool
    {
        try {
            logger('margin_sync')->info('开始同步币安全仓杠杆风险档位数据');

            // 获取币安全仓杠杆风险档位数据
            $bracketsData = $this->fetchBinanceMarginBrackets();
            if (empty($bracketsData)) {
                logger('margin_sync')->error('获取币安全仓杠杆风险档位数据失败');
                return false;
            }

            // 保存数据到数据库
            $result = $this->saveMarginBrackets($bracketsData);

            if ($result) {
                logger('margin_sync')->info('币安全仓杠杆风险档位数据同步成功');
            } else {
                logger('margin_sync')->error('币安全仓杠杆风险档位数据保存失败');
            }

            return $result;

        } catch (\Throwable $e) {
            logger('margin_sync')->error('同步币安全仓杠杆风险档位数据异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 获取币安全仓杠杆风险档位数据
     *
     * @return array 档位数据数组
     */
    private function fetchBinanceMarginBrackets(): array
    {
        try {
            $client = client([
                'timeout' => 30,
                'headers' => [
                    'Referer' => 'https://www.binance.com/',
                    'Origin' => 'https://www.binance.com',
                ]
            ]);

            $response = $client->get(self::BINANCE_MARGIN_BRACKETS_URL);

            if ($response->getStatusCode() !== 200) {
                logger('margin_sync')->error('获取币安全仓杠杆风险档位数据HTTP状态码异常', [
                    'status_code' => $response->getStatusCode(),
                    'response_body' => $response->getBody()->getContents()
                ]);
                return [];
            }

            $data = json_decode($response->getBody()->getContents(), true);

            if (!$data || $data['code'] !== '000000' || !isset($data['data'])) {
                logger('margin_sync')->error('币安全仓杠杆风险档位数据接口返回数据格式异常', [
                    'response_data' => $data
                ]);
                return [];
            }

            logger('margin_sync')->info('成功获取币安全仓杠杆风险档位数据', [
                'total_groups' => count($data['data'])
            ]);

            return $data['data'];

        } catch (\Throwable $e) {
            logger('margin_sync')->error('获取币安全仓杠杆风险档位数据异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return [];
        }
    }

    /**
     * 保存全仓杠杆风险档位数据到数据库
     *
     * @param array $bracketsData 档位数据数组
     * @return bool
     * @throws \Throwable
     */
    private function saveMarginBrackets(array $bracketsData): bool
    {
        try {
            return Db::transaction(function () use ($bracketsData) {
                $savedCount = 0;
                $skippedCount = 0;
                $batchData = [];

                // 获取本地币种映射 base_asset => currency_id
                $currencyMapping = Currency::query()
                    ->pluck('id', 'base_asset')
                    ->toArray();

                logger('margin_sync')->info('开始保存全仓杠杆风险档位数据', [
                    'brackets_groups' => count($bracketsData),
                    'local_currency_count' => count($currencyMapping)
                ]);

                foreach ($bracketsData as $group) {
                    $assetNames = $group['assetNames'] ?? [];
                    $brackets = $group['brackets'] ?? [];
                    $rank = $group['rank'] ?? '';

                    if (empty($assetNames) || empty($brackets)) {
                        $skippedCount++;
                        continue;
                    }

                    // 遍历每个资产名称
                    foreach ($assetNames as $assetName) {
                        // 检查本地是否存在该币种（通过base_asset字段匹配）
                        if (!isset($currencyMapping[$assetName])) {
                            logger('margin_sync')->debug('跳过不存在的资产', [
                                'asset_name' => $assetName,
                                'rank' => $rank
                            ]);
                            continue;
                        }

                        $currencyId = $currencyMapping[$assetName];

                        // 先删除该币种的旧档位数据（全仓）
                        TradeMarginBracket::query()
                            ->where('currency_id', $currencyId)
                            ->where('is_cross', 1)
                            ->delete();

                        // 遍历档位数据
                        foreach ($brackets as $index => $bracket) {
                            $batchData[] = [
                                'currency_id' => $currencyId,
                                'level' => $index + 1, // 档位等级从1开始
                                'hold_start' => (float)($bracket['floor'] ?? 0),
                                'hold_end' => (float)($bracket['maxDebt'] ?? 0),
                                'max_lever' => (int)($bracket['leverage'] ?? 1),
                                'keep_rate' => (float)($bracket['maintenanceMarginRate'] ?? 0),
                                'init_rate' => (float)($bracket['initialMarginRate'] ?? 0),
                                'is_cross' => 1, // 全仓
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now(),
                            ];

                            // 每100条批量插入一次
                            if (count($batchData) >= 100) {
                                TradeMarginBracket::query()->insert($batchData);
                                $savedCount += count($batchData);
                                $batchData = [];
                            }
                        }
                    }
                }

                // 插入剩余数据
                if (!empty($batchData)) {
                    TradeMarginBracket::query()->insert($batchData);
                    $savedCount += count($batchData);
                }

                logger('margin_sync')->info('全仓杠杆风险档位数据保存完成', [
                    'saved_count' => $savedCount,
                    'skipped_count' => $skippedCount,
                    'total_processed' => count($bracketsData)
                ]);

                return $savedCount > 0;
            });

        } catch (\Throwable $e) {
            logger('margin_sync')->error('保存全仓杠杆风险档位数据异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw $e;
        }
    }

    /**
     * 同步币安逐仓杠杆档位配置数据
     *
     * @return bool 同步是否成功
     * @throws \Throwable
     */
    public function syncBinanceIsolatedMarginTiers(): bool
    {
        try {
            logger('margin_sync')->info('开始同步币安逐仓杠杆档位配置数据');

            // 获取市场类型为1的币种列表
            $currencies = $this->getCryptoCurrencies();
            if (empty($currencies)) {
                logger('margin_sync')->error('没有找到市场类型为1的币种');
                return false;
            }

            // 批量获取和保存逐仓档位配置数据（每10个币种保存一次）
            $result = $this->fetchAndSaveIsolatedMarginTiers($currencies);

            if ($result) {
                logger('margin_sync')->info('币安逐仓杠杆档位配置数据同步成功');
            } else {
                logger('margin_sync')->error('币安逐仓杠杆档位配置数据同步失败');
            }

            return $result;

        } catch (\Throwable $e) {
            logger('margin_sync')->error('同步币安逐仓杠杆档位配置数据异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 获取市场类型为1的币种列表
     *
     * @return array [symbol => currency_id]
     */
    private function getCryptoCurrencies(): array
    {
        try {
            $currencies = Currency::query()
                ->where('market_type', 1)
                ->pluck('id', 'symbol')
                ->toArray();

            logger('margin_sync')->info('成功获取加密货币币种列表', [
                'total_currencies' => count($currencies)
            ]);

            return $currencies;

        } catch (\Throwable $e) {
            logger('margin_sync')->error('获取加密货币币种列表异常', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 批量获取和保存逐仓杠杆档位配置数据（每10个币种保存一次）
     *
     * @param array $currencies [symbol => currency_id]
     * @return bool
     * @throws \Throwable
     */
    private function fetchAndSaveIsolatedMarginTiers(array $currencies): bool
    {
        $batchSize = 10; // 每10个币种保存一次
        $totalSuccessCount = 0;
        $totalFailCount = 0;
        $batchCount = 0;

        $client = client([
            'timeout' => 30,
            'headers' => [
                'Referer' => 'https://www.binance.com/',
                'Origin' => 'https://www.binance.com',
            ]
        ]);

        logger('margin_sync')->info('开始批量获取和保存逐仓杠杆档位配置', [
            'total_symbols' => count($currencies),
            'batch_size' => $batchSize
        ]);

        $currencyBatch = [];
        $batchTierData = [];

        foreach ($currencies as $symbol => $currencyId) {
            $currencyBatch[$symbol] = $currencyId;

            try {
                // 请求API
                $url = self::BINANCE_ISOLATED_TIER_CONFIG_URL . $symbol;
                $response = $client->get($url);
                if ($response->getStatusCode() !== 200) {
                    logger('margin_sync')->warning('获取逐仓档位配置HTTP状态码异常', [
                        'symbol' => $symbol,
                        'status_code' => $response->getStatusCode()
                    ]);
                    $totalFailCount++;
                    continue;
                }

                $data = json_decode($response->getBody()->getContents(), true);

                if (!$data || $data['code'] !== '000000' || !isset($data['data']['tierConfigs'])) {
                    logger('margin_sync')->warning('逐仓档位配置接口返回数据格式异常', [
                        'symbol' => $symbol,
                        'response_data' => $data
                    ]);
                    $totalFailCount++;
                    continue;
                }

                $tierConfigs = $data['data']['tierConfigs'] ?? [];
                if (!empty($tierConfigs)) {
                    $batchTierData[$symbol] = [
                        'currency_id' => $currencyId,
                        'tier_configs' => $tierConfigs
                    ];
                    $totalSuccessCount++;
                }

                // 控制请求频率：0.3秒间隔
                usleep(300000); // 300毫秒 = 300,000微秒

            } catch (\Throwable $e) {
                logger('margin_sync')->error('获取单个币种逐仓档位配置异常', [
                    'symbol' => $symbol,
                    'error' => $e->getMessage()
                ]);
                $totalFailCount++;
                continue;
            }

            // 每10个币种或到达最后一个币种时保存一次
            if (count($currencyBatch) >= $batchSize || $symbol === array_key_last($currencies)) {
                $batchCount++;
                
                if (!empty($batchTierData)) {
                    try {
                        $this->saveBatchIsolatedMarginTiers($batchTierData, $batchCount);
                        logger('margin_sync')->info('批次保存完成', [
                            'batch_number' => $batchCount,
                            'batch_symbols' => count($batchTierData),
                            'symbols' => array_keys($batchTierData)
                        ]);
                    } catch (\Throwable $e) {
                        logger('margin_sync')->error('批次保存失败', [
                            'batch_number' => $batchCount,
                            'error' => $e->getMessage()
                        ]);
                        // 继续处理下一批次，不中断整个流程
                    }
                }

                // 重置批次数据
                $currencyBatch = [];
                $batchTierData = [];
            }
        }

        logger('margin_sync')->info('逐仓杠杆档位配置数据获取和保存完成', [
            'total_success_count' => $totalSuccessCount,
            'total_fail_count' => $totalFailCount,
            'total_processed' => count($currencies),
            'total_batches' => $batchCount
        ]);

        return $totalSuccessCount > 0;
    }

    /**
     * 批量保存逐仓杠杆档位配置数据到数据库
     *
     * @param array $tierData [symbol => [currency_id, tier_configs]]
     * @param int $batchNumber 批次号
     * @return bool
     * @throws \Throwable
     */
    private function saveBatchIsolatedMarginTiers(array $tierData, int $batchNumber): bool
    {
        try {
            return Db::transaction(function () use ($tierData, $batchNumber) {
                $savedCount = 0;
                $batchData = [];

                logger('margin_sync')->info('开始保存逐仓杠杆档位配置数据', [
                    'batch_number' => $batchNumber,
                    'symbols_count' => count($tierData)
                ]);

                foreach ($tierData as $symbol => $symbolData) {
                    $currencyId = $symbolData['currency_id'];
                    $tierConfigs = $symbolData['tier_configs'];

                    // 先删除该币种的旧档位数据（逐仓）
                    TradeMarginBracket::query()
                        ->where('currency_id', $currencyId)
                        ->where('is_cross', 2)
                        ->delete();

                    // 遍历档位配置数据
                    foreach ($tierConfigs as $tier) {
                        $leverageStr = $tier['leverage'] ?? '1.00x';
                        $leverage = (float)str_replace('x', '', $leverageStr); // 去掉x转为浮点数
                        
                        $quoteTierLimit = (float)($tier['quoteTierLimit'] ?? 0);
                        $normalBar = (float)($tier['normalBar'] ?? 1);
                        $liquidationBar = (float)($tier['liquidationBar'] ?? 1);

                        $batchData[] = [
                            'currency_id' => $currencyId,
                            'level' => (int)($tier['tier'] ?? 1),
                            'hold_start' => $leverage > 0 ? $quoteTierLimit / $leverage : 0,
                            'hold_end' => 0, // 固定为0
                            'max_lever' => (int)$leverage,
                            'keep_rate' => $normalBar - 1,
                            'init_rate' => $liquidationBar - 1,
                            'is_cross' => 2, // 逐仓
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now(),
                        ];

                        // 每100条批量插入一次
                        if (count($batchData) >= 100) {
                            TradeMarginBracket::query()->insert($batchData);
                            $savedCount += count($batchData);
                            $batchData = [];
                        }
                    }
                }

                // 插入剩余数据
                if (!empty($batchData)) {
                    TradeMarginBracket::query()->insert($batchData);
                    $savedCount += count($batchData);
                }

                logger('margin_sync')->info('批次逐仓杠杆档位配置数据保存完成', [
                    'batch_number' => $batchNumber,
                    'saved_count' => $savedCount,
                    'symbols_processed' => count($tierData)
                ]);

                return $savedCount > 0;
            });

        } catch (\Throwable $e) {
            logger('margin_sync')->error('保存批次逐仓杠杆档位配置数据异常', [
                'batch_number' => $batchNumber,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw $e;
        }
    }
}