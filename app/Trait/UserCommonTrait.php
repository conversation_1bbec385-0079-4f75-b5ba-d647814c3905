<?php

namespace App\Trait;

use App\Enum\Config\UserVipLevelKey;
use App\Http\Api\Service\User\UserService;
use App\Model\User\UserVipLevel;
use Hyperf\Redis\Redis;

trait UserCommonTrait
{
    /**
     * 获取用户的vip 等级id
     * @param int $userId
     * @param Redis|null $redis
     * @return int
     */
    protected function getUserVipLevelId(int $userId, ?Redis $redis): int
    {
        try {
            if (is_null($redis)) {
                $redis = redis();
            }
            $key = sprintf(UserService::USER_CACHE_KEY, $userId);
            return (int)($redis->hGet($key, 'vip_level_id') ?? 1);
        } catch (\Throwable) {
            return 1;
        }
    }

    /**
     * 获取用户对应的等级费率配置
     * @param int $userId
     * @param Redis|null $redis
     * @return bool|array|\Redis|null
     */
    protected function getVipConfig(int $userId, ?Redis $redis = null): bool|array|\Redis|null
    {
        try {
            if (is_null($redis)) {
                $redis = redis();
            }
            $vipId = $this->getUserVipLevelId($userId, $redis);
            return $redis->hGetAll(UserVipLevelKey::getConfigKey($vipId)) ?? UserVipLevel::getUserFeeRates($userId);
        } catch (\Throwable) {
            return UserVipLevel::getUserFeeRates($userId);
        }
    }
}