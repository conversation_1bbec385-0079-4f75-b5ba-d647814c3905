<?php

/**
 * UserAssetsChangedCacheJob.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/7
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Job\User;

use App\Enum\AsyncExecutorKey;
use App\Enum\MarketData\TickerSyncKey;
use App\Enum\MarketType;
use App\Enum\User\UserAssetsCacheKey;
use App\Model\Enums\User\AccountType;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;


class UserAssetsChangedCacheJob extends Job
{
    public function __construct(
        public int $user_id,
        public int $account_type,
        public int $currency_id
    )
    {}

    public function handle(): void
    {
        try {
            match ($this->account_type){
                AccountType::MARGIN->value, AccountType::ISOLATED->value => $this->MarginAssetsCached($this->user_id,$this->account_type,$this->currency_id),
                AccountType::FUTURES->value => $this->ContractAssetsCached($this->user_id,$this->account_type,$this->currency_id),
                default => false,
            };
        }catch (\Throwable $t){
            echo "用户资产缓存任务执行失败：{$t->getMessage()}";
        }
    }

    /**
     * 用户合约账户资产缓存
     * @param int $user_id
     * @param int $account_type
     * @param int $currency_id
     * @return void
     */
    protected function ContractAssetsCached(int $user_id,int $account_type,int $currency_id)
    {
        try {
            $accountsAssetService = ApplicationContext::getContainer()->get(UserAccountsAssetService::class);
            $assets = $accountsAssetService->getUserAsset(
                $user_id,
                $account_type,
                $currency_id
            );

            if (!$assets) {
                return;
            }

            $available = $assets->getAvailable();
            $frozen = $assets->getFrozen();

            $key = UserAssetsCacheKey::getFuturesAssetsKey($user_id, $account_type);

            $redis = redis();

            $cache_assets = [
                $currency_id => $available,
                "{$currency_id}-frozen" => $frozen,
            ];

            $redis->hMSet($key, $cache_assets);

        } catch (\RedisException $t) {
            var_dump($t->getMessage());
            pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value, new UserAssetsChangedCacheJob($user_id, $account_type, $currency_id), 2);
        } catch (\Throwable $t) {
            echo "合约账户资产数据缓存失败：{$t->getMessage()}\n";
        }
    }

    /**
     * 用户杠杆资金缓存
     * @param int $user_id
     * @param int $account_type
     * @param int $currency_id
     * @return void
     */
    protected function MarginAssetsCached(int $user_id, int $account_type, int $currency_id): void
    {
        try {
            $accountsAssetService = ApplicationContext::getContainer()->get(UserAccountsAssetService::class);
            $assets = $accountsAssetService->getUserAsset(
                $user_id,
                $account_type,
                $currency_id
            );

            if (!$assets) {
                return;
            }

            $available = $assets->getAvailable();
            $frozen = $assets->getFrozen();
            $borrow = $assets->getBorrowedAmount();

            $key = match ($account_type) {
                AccountType::MARGIN->value => UserAssetsCacheKey::getCrosstAssetsKey($user_id, $account_type),
                AccountType::ISOLATED->value => UserAssetsCacheKey::getIsolatedAssetsKey($user_id, $account_type, $currency_id),
            };

            $redis = redis();

            $pipeline = $redis->pipeline();
            $pipeline->hGet(TickerSyncKey::getOuterTradeKey($currency_id, MarketType::CRYPTO->value), "price");

            $results = $pipeline->exec();
            $currency_price = isset($results[0]) && $results[0] > 0 ? $results[0] : 1;
            $assets_total = bcmul(strval($currency_price), strval($available), 8);

            $cache_assets = [
                "{$currency_id}-0" => $assets_total,
                $currency_id => $available,
                "{$currency_id}-frozen" => $frozen,
                "{$currency_id}-borrow" => $borrow
            ];
            if($account_type === AccountType::ISOLATED->value){
                $cache_assets["{$currency_id}-margin_quote"] = $assets->getMarginQuote();
                $cache_assets["{$currency_id}-margin_borrow"] = $assets->getMarginBorrow();
            }

            $pipeline->hMSet($key,$cache_assets );
            $pipeline->exec();

        }catch (\RedisException $t){
            var_dump($t->getMessage());
            pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value,new UserAssetsChangedCacheJob($user_id,$account_type,$currency_id),2);
        } catch (\Throwable $t) {
            echo "用户资产数据缓存失败：{$t->getMessage()}\n";
        }
    }

}