<?php

declare(strict_types=1);

namespace App\Job\Contract;

use App\Http\Api\Service\V1\Contract\PerpetualLiquidationService;
use App\Http\Api\Service\V1\Contract\PerpetualTradeService;
use App\Enum\Contract\PerpetualPositionCacheKey;
use App\Model\Enums\Trade\Perpetual\PositionStatus;
use App\Model\Trade\TradePerpetualPosition;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;
use Hyperf\Redis\Redis;

/**
 * 永续合约强平异步任务
 */
class PerpetualLiquidationJob extends Job
{
    protected int $userId;
    protected int $positionId;
    protected array $riskData;

    public function __construct(int $userId, int $positionId, array $riskData,)
    {
        $this->userId = $userId;
        $this->positionId = $positionId;
        $this->riskData = $riskData;
    }

    public function handle(): void
    {
        $container = ApplicationContext::getContainer();
        $liquidationService = $container->get(PerpetualLiquidationService::class);
        $redis = $container->get(Redis::class);
        $logger = logger('perpetual-liquidation', 'perpetual/liquidation.log');

        $logger->info('开始执行强平任务', [
            'user_id' => $this->userId,
            'position_id' => $this->positionId,
            'margin_ratio' => $this->riskData['margin_ratio'] ?? 0,
            'maintenance_margin_rate' => $this->riskData['maintenance_margin_rate'] ?? 0
        ]);

        $position = TradePerpetualPosition::query()->where('id',$this->positionId)->first();

        if($position->status != PositionStatus::HOLDING->value){
            return;
        }

        try {
            // 检查是否是全仓强平，需要先撤销挂单
            $needCancelOrders = $this->riskData['cross_margin_info']['need_cancel_orders'] ?? false;

            if ($needCancelOrders) {
                // 撤销用户在该币种的所有未成交挂单
                $this->cancelUserPendingOrders($container, $logger);
            }

            // 执行强平，传递缓存的仓位数据
            $result = $liquidationService->executeLiquidation($this->userId, $this->positionId, $position);

            if ($result) {
                $logger->info('强平执行成功', [
                    'user_id' => $this->userId,
                    'position_id' => $this->positionId,
                    'margin_type' => $this->riskData['margin_type'] ?? 'unknown',
                    'cancelled_orders' => $needCancelOrders
                ]);

                // 更新强平统计
                $this->updateLiquidationStats($redis);
            } else {
                $logger->error('强平执行失败', [
                    'user_id' => $this->userId,
                    'position_id' => $this->positionId
                ]);
            }

        } catch (\Throwable $e) {
            $logger->error('强平任务执行异常', [
                'user_id' => $this->userId,
                'position_id' => $this->positionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        } finally {
            // 释放强平锁定
            $lockKey = PerpetualPositionCacheKey::getLiquidationLockKey($this->positionId);
            $redis->del($lockKey);

            $logger->debug('强平锁定已释放', [
                'position_id' => $this->positionId
            ]);
        }
    }

    /**
     * 更新强平统计
     */
    protected function updateLiquidationStats(Redis $redis): void
    {
        try {
            $statsKey = PerpetualPositionCacheKey::getGlobalStatsKey();
            
            $redis->hIncrBy($statsKey, 'total_liquidations', 1);
            $redis->hSet($statsKey, 'last_liquidation_time', time());

            if (isset($this->riskData['currency_id'])) {
                $currencyStatsKey = PerpetualPositionCacheKey::getCurrencyRiskKey($this->riskData['currency_id']);
                $redis->hIncrBy($currencyStatsKey, 'liquidation_count', 1);
                $redis->hSet($currencyStatsKey, 'last_liquidation_time', time());
            }

        } catch (\Throwable $e) {
            logger('perpetual-liquidation')->error('更新强平统计失败', [
                'user_id' => $this->userId,
                'position_id' => $this->positionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $reason): void
    {
        $container = ApplicationContext::getContainer();
        $redis = $container->get(Redis::class);
        $logger = logger('perpetual-liquidation', 'perpetual/liquidation.log');

        $logger->error('强平任务失败', [
            'user_id' => $this->userId,
            'position_id' => $this->positionId,
            'reason' => $reason->getMessage(),
            'trace' => $reason->getTraceAsString()
        ]);

        // 释放强平锁定
        $lockKey = PerpetualPositionCacheKey::getLiquidationLockKey($this->positionId);
        $redis->del($lockKey);

        // 记录失败统计
        try {
            $statsKey = PerpetualPositionCacheKey::getGlobalStatsKey();
            $redis->hIncrBy($statsKey, 'failed_liquidations', 1);
            $redis->hSet($statsKey, 'last_failed_liquidation_time', time());
        } catch (\Throwable $e) {
            $logger->error('记录强平失败统计异常', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 撤销用户未成交的挂单
     */
    protected function cancelUserPendingOrders($container, $logger): void
    {
        try {
            $tradeService = $container->get(PerpetualTradeService::class);
            $currencyId = $this->riskData['currency_id'] ?? 0;

            if ($currencyId <= 0) {
                $logger->warning('无法获取币种ID，跳过撤单', [
                    'user_id' => $this->userId,
                    'position_id' => $this->positionId
                ]);
                return;
            }

            // 撤销用户在该币种的所有未成交订单
            $cancelResult = $tradeService->cancelAllOrders($this->userId, $currencyId);

            $logger->info('全仓强平撤单完成', [
                'user_id' => $this->userId,
                'currency_id' => $currencyId,
                'cancel_result' => $cancelResult
            ]);

        } catch (\Throwable $e) {
            $logger->error('全仓强平撤单失败', [
                'user_id' => $this->userId,
                'currency_id' => $this->riskData['currency_id'] ?? 0,
                'error' => $e->getMessage()
            ]);

            // 撤单失败不影响强平执行，只记录日志
        }
    }
}
