<?php

/**
 * AsyncFunExecutorJob.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/24
 * Website:algoquant.org
 */

namespace App\Job;

use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Throwable;

class AsyncFunExecutorJob extends Job
{
    public string $className;
    public string $methodName;
    public array $parameters;

    public function __construct(string $className, string $methodName, array $parameters = [])
    {
        $this->className = $className;
        $this->methodName = $methodName;
        $this->parameters = $parameters;
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws \Throwable
     * @throws NotFoundExceptionInterface
     */
    public function handle(): void
    {
        try {
            go(function(){
                $container = ApplicationContext::getContainer();
                $instance = $container->get($this->className);
                call_user_func_array([$instance, $this->methodName], $this->parameters);
            });
        }catch (Throwable $t){
            echo $t->getMessage()."\n";
        }
    }

}