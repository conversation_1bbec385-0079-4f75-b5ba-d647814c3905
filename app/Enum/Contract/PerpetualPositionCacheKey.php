<?php

declare(strict_types=1);

namespace App\Enum\Contract;

/**
 * 永续合约仓位缓存Key枚举类
 */
class PerpetualPositionCacheKey
{
    /**
     * 用户仓位缓存
     * 结构: Hash
     * 键: perpetual:positions:user:{user_id}
     * 字段: {currency_id}_{side}_{margin_mode} => position_data_json
     * 用途: 快速查询用户所有仓位
     */
    public static function getUserPositionsKey(int $userId): string
    {
        return "perpetual:positions:user:{$userId}";
    }

    /**
     * 币种仓位索引缓存
     * 结构: Hash
     * 键: perpetual:positions:currency:{currency_id}
     * 字段: {user_id}_{side}_{margin_mode} => position_id
     * 用途: 风险监控按币种查询所有仓位
     */
    public static function getCurrencyPositionsKey(int $currencyId): string
    {
        return "perpetual:positions:currency:{$currencyId}";
    }

    /**
     * 仓位详情缓存
     * 结构: Hash
     * 键: perpetual:position:detail:{position_id}
     * 字段: 完整的仓位数据字段
     * 用途: 单个仓位详情快速查询
     */
    public static function getPositionDetailKey(int $positionId): string
    {
        return "perpetual:position:detail:{$positionId}";
    }

    /**
     * 风险等级索引缓存
     * 结构: Sorted Set
     * 键: perpetual:risk:level:{risk_level}
     * Score: margin_ratio (保证金率)
     * Member: position_id
     * 用途: 按风险等级快速查询仓位
     */
    public static function getRiskLevelKey(string $riskLevel): string
    {
        return "perpetual:risk:level:{$riskLevel}";
    }

    /**
     * 币种风险监控缓存
     * 结构: Hash
     * 键: perpetual:risk:currency:{currency_id}
     * 字段: last_price, last_calculated, position_count, risk_summary
     * 用途: 币种级别的风险监控统计
     */
    public static function getCurrencyRiskKey(int $currencyId): string
    {
        return "perpetual:risk:currency:{$currencyId}";
    }

    /**
     * 活跃仓位集合
     * 结构: Set
     * 键: perpetual:active_positions
     * 成员: position_id
     * 用途: 全局活跃仓位统计和查询
     */
    public static function getActivePositionsKey(): string
    {
        return "perpetual:active_positions";
    }

    /**
     * 用户风险统计缓存
     * 结构: Hash
     * 键: perpetual:risk:user:{user_id}
     * 字段: total_margin, total_pnl, risk_level, last_calculated
     * 用途: 用户级别的风险统计
     */
    public static function getUserRiskKey(int $userId): string
    {
        return "perpetual:risk:user:{$userId}";
    }

    /**
     * 待风险计算队列
     * 结构: Set
     * 键: perpetual:risk:pending:{currency_id}
     * 成员: position_id
     * 用途: 标记需要重新计算风险的仓位
     */
    public static function getPendingRiskCalculationKey(int $currencyId): string
    {
        return "perpetual:risk:pending:{$currencyId}";
    }

    /**
     * 强平锁定缓存
     * 结构: String
     * 键: perpetual:liquidation:lock:{position_id}
     * 值: timestamp
     * 用途: 防止重复强平
     */
    public static function getLiquidationLockKey(int $positionId): string
    {
        return "perpetual:liquidation:lock:{$positionId}";
    }

    /**
     * 价格最后计算缓存
     * 结构: Hash
     * 键: perpetual:price:last_calculated
     * 字段: {currency_id} => last_price
     * 用途: 记录每个币种最后计算风险时的价格
     */
    public static function getPriceLastCalculatedKey(): string
    {
        return "perpetual:price:last_calculated";
    }

    /**
     * 仓位变更事件队列
     * 结构: List
     * 键: perpetual:position:events
     * 值: event_data_json
     * 用途: 异步处理仓位变更事件
     */
    public static function getPositionEventsQueueKey(): string
    {
        return "perpetual:position:events";
    }

    /**
     * 全局统计缓存
     * 结构: Hash
     * 键: perpetual:stats:global
     * 字段: total_positions, total_users, total_margin, total_pnl, last_updated
     * 用途: 系统级别的统计数据
     */
    public static function getGlobalStatsKey(): string
    {
        return "perpetual:stats:global";
    }

    /**
     * 生成仓位唯一键
     * 格式: {currency_id}_{side}_{margin_mode}
     * 用途: 在Hash结构中作为字段名
     */
    public static function generatePositionKey(int $currencyId, int $side, int $marginMode): string
    {
        return "{$currencyId}_{$side}_{$marginMode}";
    }

    /**
     * 生成用户仓位键
     * 格式: {user_id}_{side}_{margin_mode}
     * 用途: 在币种索引中标识特定用户的仓位
     */
    public static function generateUserPositionKey(int $userId, int $side, int $marginMode): string
    {
        return "{$userId}_{$side}_{$marginMode}";
    }

    /**
     * 解析仓位键
     * 将仓位键解析为组成部分
     */
    public static function parsePositionKey(string $positionKey): array
    {
        $parts = explode('_', $positionKey);
        if (count($parts) !== 3) {
            throw new \InvalidArgumentException("Invalid position key format: {$positionKey}");
        }

        return [
            'currency_id' => (int)$parts[0],
            'side' => (int)$parts[1],
            'margin_mode' => (int)$parts[2]
        ];
    }

    /**
     * 获取所有风险等级
     */
    public static function getAllRiskLevels(): array
    {
        return ['safe', 'warning', 'danger', 'liquidation'];
    }

    /**
     * 获取缓存过期时间配置
     * 注意：仓位相关的核心缓存不设置过期时间，随业务逻辑维护
     * 只有临时性质的缓存才设置过期时间
     */
    public static function getCacheExpireTimes(): array
    {
        return [
            'risk_level' => 600,           // 风险等级索引 10分钟
            'liquidation_lock' => 300,     // 强平锁定 5分钟
            'pending_calculation' => 180,  // 待计算队列 3分钟（临时队列）
        ];
    }
}
