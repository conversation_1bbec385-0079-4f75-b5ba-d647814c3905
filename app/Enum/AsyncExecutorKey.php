<?php

/**
 * AsyncExecutorKey.php
 * Author    chen<PERSON><PERSON> (<EMAIL>)
 * Version   1.0
 * Date      2025/6/27
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum;

enum AsyncExecutorKey:string
{
    /**
     * 异步方法执行key
     */
    case ASYNC_EXECUTOR_QUEUE = 'async-func-executor'; //异步方法执行job进程

    case MATCH_ORDER_QUEUE = 'match-order'; //订单异步处理job进程

    case SOCKET_MESSAGE = 'socket-message'; //socket 消息处理进程key

    case PERPETUAL_CONTRACT_QUEUE = 'perpetual-contract'; //永续合约异步处理job进程

    case ASSETS_FLOWS_QUEUE = "assets-flows"; //资金流水记录异步任务外

    case ASSETS_SYNC_QUEUE = "assets-sync"; //资金同步异步任务
}