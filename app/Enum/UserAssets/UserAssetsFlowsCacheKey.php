<?php

declare(strict_types=1);

namespace App\Enum\UserAssets;

/**
 * 用户资产流水缓存键枚举类
 */
class UserAssetsFlowsCacheKey
{
    /**
     * 资金流水队列键
     * 使用ZSET存储，score为微秒时间戳，member为流水数据JSON
     */
    const FLOWS_QUEUE = 'user_assets_flows_queue';

    const ASSETS_SYNC_QUEUE = "user_assets_sync_queue";

    /**
     * 获取用户数据库资金同步队列key
     * @return string
     */
    public static function getAssetsSyncQueue(): string
    {
        return self::ASSETS_SYNC_QUEUE;
    }

    /**
     * 获取资金流水队列键
     */
    public static function getFlowsQueueKey(): string
    {
        return self::FLOWS_QUEUE;
    }

    /**
     * 生成流水数据的唯一标识
     */
    public static function generateFlowId(): string
    {
        return uniqid('flow_', true) . '_' . getmypid();
    }
}
