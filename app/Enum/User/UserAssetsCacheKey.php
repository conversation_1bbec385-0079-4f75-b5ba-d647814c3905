<?php

/**
 * UserAssetsCacheKey.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/7
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum\User;

/**
 * 用户资产Redis缓存键枚举
 * 支持全部账户类型的资金缓存
 */
enum UserAssetsCacheKey:string
{
    // 通用资产缓存键 - 支持所有账户类型
    case USER_ASSETS_CACHE_KEY = 'user:assets:{user_id}:{account_type}:{currency_id}';

    // 用户资产索引集合键
    case USER_ASSETS_INDEX_KEY = 'user:assets:index:{user_id}';

    // 兼容旧版本的键（保持向后兼容）
    case USER_ASSETS_CROSS_CACHE_KEY = 'user:assets:{user_id}-{account_type}'; //全仓杠杆账户资产缓存
    case USER_ASSETS_ISOLATED_CACHE_KEY = 'user:assets:{user_id}-{account_type}-{currency_id}'; //逐仓杠杆账户资产缓存
    case USER_ASSETS_FUTURES_CACHE_KEY = 'user:futures:assets:{user_id}-{account_type}'; //合约账户资产缓存

    /**
     * 获取通用资产缓存key
     * 适用于所有账户类型
     *
     * @param int $user_id 用户ID
     * @param int $account_type 账户类型
     * @param int $currency_id 币种ID
     * @return string
     */
    public static function getAssetKey(int $user_id, int $account_type, int $currency_id): string
    {
        return str_replace(
            ["{user_id}", "{account_type}", "{currency_id}"],
            [$user_id, $account_type, $currency_id],
            self::USER_ASSETS_CACHE_KEY->value
        );
    }

    /**
     * 获取用户资产索引集合键
     *
     * @param int $user_id 用户ID
     * @return string
     */
    public static function getUserAssetsIndexKey(int $user_id): string
    {
        return str_replace(
            ["{user_id}"],
            [$user_id],
            self::USER_ASSETS_INDEX_KEY->value
        );
    }

    /**
     * 获取全仓资产缓存key（兼容旧版本）
     * @param int $user_id
     * @param int $account_type
     * @return array|string
     */
    public static function getCrosstAssetsKey(int $user_id, int $account_type): array|string
    {
        return str_replace(["{user_id}","{account_type}"],[$user_id,$account_type],self::USER_ASSETS_CROSS_CACHE_KEY->value);
    }

    /**
     * 获取逐仓资产缓存key（兼容旧版本）
     * @param int $user_id
     * @param int $account_type
     * @param int $currency_id
     * @return array|string
     */
    public static function getIsolatedAssetsKey(int $user_id, int $account_type, int $currency_id): array|string
    {
        return str_replace(["{user_id}","{account_type}","{currency_id}"],[$user_id,$account_type,$currency_id],self::USER_ASSETS_ISOLATED_CACHE_KEY->value);
    }

    /**
     * 获取合约账户资产缓存key（兼容旧版本）
     * @param int $user_id
     * @param int $account_type
     * @return array|string
     */
    public static function getFuturesAssetsKey(int $user_id, int $account_type): array|string
    {
        return str_replace(["{user_id}","{account_type}"],[$user_id,$account_type],self::USER_ASSETS_FUTURES_CACHE_KEY->value);
    }
}