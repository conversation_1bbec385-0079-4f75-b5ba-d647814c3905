<?php

/**
 * MatchEngineCallbackEvent.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/12
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum\MatchEngine;

enum MatchEngineCallbackEvent:string
{
    case ON_TRADE = "trade"; //成交事件注册

    case ON_PRICE = "price"; //价格变化事件

    case ON_ORDER_CANCELED = "order_canceled"; //订单取消事件

    case ON_ORDER_FILLED = "order_filled"; //订单全部成交事件

    case ON_ORDER_PARTIAL_FILLED = "order_partial_filled"; //订单部分成交事件

    case ON_ORDER_PLACED = "order_placed";//订单放置事件

    case ON_DEPTH_UPDATE = "depth_updated";//深度数据回调事件

    case ON_ORDER_MODIFIED = "order_modified";//订单修改事件

    case ON_ORDER_MODIFIED_FAILED = "order_modify_failed";//订单修改失败事件
}