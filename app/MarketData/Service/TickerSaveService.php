<?php

/**
 * TickerSaveService.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/24
 * Website:algoquant.org
 */

namespace App\MarketData\Service;

use App\Enum\MarketData\TickerSyncKey;
use App\Logger\LoggerFactory;
use App\Service\RedisFactory\MarketRedis;
use Hyperf\Di\Annotation\Inject;

class TickerSaveService
{
    #[Inject]
    public MarketRedis $redis;

    public LoggerFactory $loggerFactory;

    /**
     * 计算指定时间点的时间戳
     * @param string $timeStr 时间字符串，如 '08:00'
     * @return int
     */
    private function calculateExpireTimestamp(string $timeStr): int
    {
        $today = date('Y-m-d');
        $expireDateTime = $today . ' ' . $timeStr . ':00';
        $expireTimestamp = strtotime($expireDateTime);
        
        // 如果指定时间已经过了今天，则设置为明天的该时间
        if ($expireTimestamp <= time()) {
            $expireTimestamp = strtotime('+1 day', $expireTimestamp);
        }
        
        return $expireTimestamp;
    }

    /**
     * 批量ticker数据记录到redis中
     * @param array $tickers
     * @return bool
     */
    public function batchTickerSyncRedis(array $tickers): bool
    {
        if (empty($tickers)) {
            return true;
        }

        try {
            $pipe = $this->redis->pipeline();
            foreach ($tickers as $tickerArray) {
                if (is_array($tickerArray) && isset($tickerArray['currency_id'], $tickerArray['market_type'])) {
                    $key = str_replace(["{currency_id}","{market_type}"],
                        [$tickerArray['currency_id'], $tickerArray['market_type']],
                        TickerSyncKey::TICKER_SYNC_KEY_OUT->value);
                    $pipe->hMset($key, $tickerArray);
                }
            }
            $result = $pipe->exec();
            return $result !== false;
        } catch (\Throwable $t) {
            $this->loggerFactory->get(self::class)->error("批量ticker数据写入异常 : {$t->getMessage()}");
            return false;
        }
    }
}