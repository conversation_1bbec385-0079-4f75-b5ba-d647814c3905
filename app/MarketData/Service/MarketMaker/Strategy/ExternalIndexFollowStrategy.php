<?php

declare(strict_types=1);

/**
 * ExternalIndexFollowStrategy.php
 * 外部指数价格跟随策略
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/1/16
 * Website:algoquant.org
 */

namespace App\MarketData\Service\MarketMaker\Strategy;

use App\Enum\MarketData\TradeSubscribeKey;
use App\MarketData\Service\MarketMaker\Contract\StrategyInterface;
use App\Process\MatchEngineBaseProcess;
use App\Service\MatchEngineOrderService;
use App\Service\RedisFactory\MarketRedis;
use Hyperf\Di\Annotation\Inject;
use Swoole\Coroutine;
use Swoole\Coroutine\Channel;

/**
 * 外部指数价格跟随策略
 * 订阅Redis channel获取外部价格，在价格上下指定范围内进行做市挂单
 */
class ExternalIndexFollowStrategy implements StrategyInterface
{
    /**
     * Redis 连接
     */
    #[Inject]
    private MarketRedis $redis;

    /**
     * 策略配置
     */
    private array $config;

    /**
     * 当前指数价格
     */
    private float $currentIndexPrice = 0.0;

    /**
     * 上一次价格（用于判断价格变化）
     */
    private float $lastIndexPrice = 0.0;

    /**
     * 活跃订单记录 [order_id => order_info]
     */
    private array $activeOrders = [];

    /**
     * 是否已启动
     */
    private bool $started = false;

    /**
     * 是否完成
     */
    private bool $completed = false;

    /**
     * Redis订阅协程ID
     */
    private int $subscribeCoroutineId = 0;

    /**
     * 最后一次挂单时间
     */
    private int $lastOrderTime = 0;

    /**
     * 最后一次清理时间
     */
    private int $lastCleanupTime = 0;

    /**
     * 挂单队列
     */
    private Channel $orderQueue;

    /**
     * 队列处理协程ID
     */
    private int $queueProcessorCoroutineId = 0;

    /**
     * 过期检查队列
     */
    private Channel $expireCheckQueue;

    /**
     * 过期检查协程ID
     */
    private int $expireCheckCoroutineId = 0;

    /**
     * 市价单生成协程ID
     */
    private int $marketOrderCoroutineId = 0;

    #[Inject]
    private MatchEngineOrderService $orderService;

    public function __construct(array $config)
    {
        $this->config = array_merge([
            'symbol' => 'BTCUSDT',              // 币种符号
            'market_type' => 1,                 // 市场类型 1-现货 5-合约
            'price_change_amount' => 1,        // 价格变化阈值（绝对差值）
            'bot_user_id' => 'bot_001',         // 机器人用户ID
            'market_order_probability' => 100,   // 生成市价单的概率（百分比）
            'base_precision' => 5,              // 基础资产精度（数量精度）
            'quote_precision' => 2,             // 计价资产精度（价格精度）
            'order_delay' => 0.3,               // 挂单间隔（秒）
            'order_lifetime' => 5,             // 订单存活时间（秒）
            'cleanup_interval' => 5,           // 清理间隔（秒）
            
            // 精细化深度配置
            'tick_size' => 0.1,                // 最小价格变动单位（适合大价格数值）
            'base_quantity' => 10,            // 基础挂单数量
            'price_follow_percent' => 100.0,   // 价格跟随百分比（100%=完全跟随，99.5%=偏离0.5%）
            'depth_levels' => [                // 深度配置（从最优价开始）
                ['spread_percent' => 0.0001, 'quantity_ratio' => 1],  // 极小价差：接近指数价格
                ['spread_percent' => 0.0002, 'quantity_ratio' => 1],   // 最优价：价差0.001%，数量100%
                ['spread_percent' => 0.0005, 'quantity_ratio' => 0.9],   // 二档：价差0.005%，数量90%
                ['spread_percent' => 0.001, 'quantity_ratio' => 0.8],    // 三档：价差0.01%，数量80%
                ['spread_percent' => 0.015,  'quantity_ratio' => 0.7],  // 四档：价差0.015%，数量70%
                ['spread_percent' => 0.02,  'quantity_ratio' => 0.6],   // 五档：价差0.02%，数量60%
                ['spread_percent' => 0.025, 'quantity_ratio' => 0.5],   // 六档：价差0.025%，数量50%
                ['spread_percent' => 0.03, 'quantity_ratio' => 0.4],    // 七档：价差0.03%，数量40%
                ['spread_percent' => 0.035, 'quantity_ratio' => 0.3],   // 八档：价差0.035%，数量30%
                ['spread_percent' => 0.04,  'quantity_ratio' => 0.2],   // 九档：价差0.04%，数量20%
                ['spread_percent' => 0.045,  'quantity_ratio' => 0.1],  // 十档：价差0.045%，数量10%（修正了0.45的错误）
            ],
        ], $config);

        $this->validateConfig();
        
        // 初始化挂单队列（容量100）
        $this->orderQueue = new Channel(100);
        
        // 初始化过期检查队列（容量200）
        $this->expireCheckQueue = new Channel(200);
    }

    /**
     * 验证配置参数
     */
    private function validateConfig(): void
    {
        $required = ['symbol', 'market_type', 'base_precision', 'quote_precision'];
        foreach ($required as $field) {
            if (!isset($this->config[$field]) || empty($this->config[$field])) {
                throw new \InvalidArgumentException("Missing required config field: {$field}");
            }
        }

        if ($this->config['base_quantity'] <= 0) {
            throw new \InvalidArgumentException("Invalid base_quantity, must be greater than 0");
        }

        if ($this->config['tick_size'] <= 0) {
            throw new \InvalidArgumentException("Invalid tick_size, must be greater than 0");
        }

        if (empty($this->config['depth_levels']) || !is_array($this->config['depth_levels'])) {
            throw new \InvalidArgumentException("depth_levels must be a non-empty array");
        }

        if ($this->config['base_precision'] < 0 || $this->config['base_precision'] > 18) {
            throw new \InvalidArgumentException("Invalid base_precision, must be between 0 and 18");
        }

        if ($this->config['quote_precision'] < 0 || $this->config['quote_precision'] > 18) {
            throw new \InvalidArgumentException("Invalid quote_precision, must be between 0 and 18");
        }

        if ($this->config['price_change_amount'] <= 0) {
            throw new \InvalidArgumentException("Invalid price_change_amount, must be greater than 0");
        }

        if ($this->config['price_follow_percent'] <= 0 || $this->config['price_follow_percent'] > 200) {
            throw new \InvalidArgumentException("Invalid price_follow_percent, must be between 0 and 200");
        }
    }

    /**
     * 策略启动时执行
     */
    public function onStart(): void
    {
        if ($this->started) {
            return;
        }

        echo "[ExternalIndexFollow] Starting strategy for {$this->config['symbol']} (market_type: {$this->config['market_type']})\n";
        echo "[ExternalIndexFollow] Config: depth_levels=" . count($this->config['depth_levels']) . ", base_quantity={$this->config['base_quantity']}, lifetime={$this->config['order_lifetime']}s, price_change_amount={$this->config['price_change_amount']}, price_follow={$this->config['price_follow_percent']}%\n";

        // 启动Redis订阅协程
        $this->startPriceSubscription();

        // 启动队列处理协程
        $this->startQueueProcessor();

        // 启动过期检查协程
        $this->startExpireChecker();

        // 启动市价单生成协程
        //$this->startMarketOrderGenerator();

        $this->started = true;
        $this->lastCleanupTime = time();
    }

        /**
     * 每个周期执行一次
     */
    public function execute(): void
    {
        if (!$this->started) {
            return;
        }

        // execute方法只做基本的状态检查，具体的挂单和清理都由各自的队列处理
        // 所有的挂单生成、过期检查都通过专门的协程和队列来处理
    }

    /**
     * 策略结束时调用
     */
    public function onComplete(): void
    {
        echo "[ExternalIndexFollow] Strategy completing for {$this->config['symbol']}\n";

        // 停止价格订阅
        $this->stopPriceSubscription();

        // 停止队列处理
        $this->stopQueueProcessor();

        // 停止过期检查
        $this->stopExpireChecker();

        // 停止市价单生成
        $this->stopMarketOrderGenerator();

        // 取消所有活跃订单
        $this->cancelAllOrders();

        $this->completed = true;
    }

    /**
     * 当前策略是否完成
     */
    public function isComplete(): bool
    {
        return $this->completed;
    }

    /**
     * 执行周期（秒）
     */
    public function getInterval(): int
    {
        return 3; // 3秒执行一次
    }

    /**
     * 启动价格订阅
     */
    private function startPriceSubscription(): void
    { $channel = TradeSubscribeKey::getOuterTradeKey(
        $this->config['symbol'],
        $this->config['market_type']
    );


        echo "[ExternalIndexFollow] Subscribing to Redis channel: {$channel}\n";

        $this->subscribeCoroutineId = Coroutine::create(function() use ($channel) {
            try {
                // 订阅价格频道
                $this->redis->subscribe([$channel], function($redis, $channel, $message) {
                    $this->handlePriceUpdate($message);
                });

            } catch (\Throwable $e) {
                echo "[ExternalIndexFollow] Price subscription error: {$e->getMessage()}\n";
            }
        });
    }

    /**
     * 停止价格订阅
     */
    private function stopPriceSubscription(): void
    {
        if ($this->subscribeCoroutineId > 0) {
            // 注意：Swoole协程无法直接kill，这里只是标记
            $this->subscribeCoroutineId = 0;
        }
    }

    /**
     * 启动队列处理协程
     */
    private function startQueueProcessor(): void
    {
        $this->queueProcessorCoroutineId = Coroutine::create(function() {
            try {
                while (!$this->completed) {
                    // 从队列中获取挂单任务
                    $orderData = $this->orderQueue->pop(1.0); // 1秒超时
                    
                    if ($orderData === false) {
                        continue; // 超时，继续循环
                    }

                    // 处理挂单
                    $this->processQueuedOrder($orderData);
                    
                    // 挂单间隔
                    Coroutine::sleep($this->config['order_delay']);
                }
            } catch (\Throwable $e) {
                echo "[ExternalIndexFollow] Queue processor error: {$e->getMessage()}\n";
            }
        });
    }

    /**
     * 停止队列处理
     */
    private function stopQueueProcessor(): void
    {
        if ($this->queueProcessorCoroutineId > 0) {
            $this->queueProcessorCoroutineId = 0;
        }
        
        // 关闭队列
        $this->orderQueue->close();
    }

    /**
     * 启动过期检查协程
     */
    private function startExpireChecker(): void
    {
        $this->expireCheckCoroutineId = Coroutine::create(function() {
            try {
                while (!$this->completed) {
                    // 从队列中获取过期检查任务
                    $expireData = $this->expireCheckQueue->pop(1.0); // 1秒超时
                    
                    if ($expireData === false) {
                        continue; // 超时，继续循环
                    }

                    // 检查订单是否过期
                    $this->checkOrderExpire($expireData);
                }
            } catch (\Throwable $e) {
                echo "[ExternalIndexFollow] Expire checker error: {$e->getMessage()}\n";
            }
        });
    }

    /**
     * 停止过期检查协程
     */
    private function stopExpireChecker(): void
    {
        if ($this->expireCheckCoroutineId > 0) {
            $this->expireCheckCoroutineId = 0;
        }
        
        // 关闭队列
        $this->expireCheckQueue->close();
    }

    /**
     * 检查订单过期
     */
    private function checkOrderExpire(array $expireData): void
    {
        $currentTime = time();
        $orderId = $expireData['order_id'];
        $expireAt = $expireData['expire_at'];

        // 如果还没到过期时间，重新加入队列等待
        if ($currentTime < $expireAt) {
            $waitTime = $expireAt - $currentTime;
            Coroutine::sleep($waitTime);
            
            // 重新检查订单是否还存在
            if (!isset($this->activeOrders[$orderId])) {
                return; // 订单已被删除
            }
        }

        // 检查订单是否过期
        if (isset($this->activeOrders[$orderId])) {
            $order = $this->activeOrders[$orderId];
            $orderAge = $currentTime - $order['created_at'];
            
            if ($orderAge >= $this->config['order_lifetime']) {
                // 检查是否可以撤销该订单（确保深度不会过低）
                if ($this->canCancelOrder($order['side'])) {
                   // echo "[ExternalIndexFollow] Order {$orderId} expired (age: {$orderAge}s), canceling...\n";
                    
                    try {
                        MatchEngineBaseProcess::cancelOrder(
                            $order['symbol'],
                            (int)$order['order_id']
                        );
                        unset($this->activeOrders[$orderId]);
                    } catch (\Throwable $e) {
                        //echo "[ExternalIndexFollow] Error canceling expired order {$orderId}: {$e->getMessage()}\n";
                    }
                } else {
                    //echo "[ExternalIndexFollow] Order {$orderId} expired but cannot cancel (maintaining minimum depth)\n";
                    // 重新加入队列，稍后再检查
                    $newExpireData = [
                        'order_id' => $orderId,
                        'expire_at' => $currentTime + 5 // 5秒后再检查
                    ];
                    $this->expireCheckQueue->push($newExpireData, 0.1);
                }
            }
        }
    }

    /**
     * 检查是否可以撤销订单（确保深度不会过低）
     */
    private function canCancelOrder(string $side): bool
    {
        $minDepth = count($this->config['depth_levels']);
        
        // 统计当前买单和卖单数量
        $buyOrders = 0;
        $sellOrders = 0;
        
        foreach ($this->activeOrders as $order) {
            if ($order['side'] === 'buy') {
                $buyOrders++;
            } elseif ($order['side'] === 'sell') {
                $sellOrders++;
            }
        }
        
        // 检查撤销后是否还能保持最小深度
        if ($side === 'buy') {
            return $buyOrders > $minDepth;
        } elseif ($side === 'sell') {
            return $sellOrders > $minDepth;
        }
        
        return false;
    }

    /**
     * 处理队列中的挂单
     */
    private function processQueuedOrder(array $orderData): void
    {
        try {
            $orderId = intval(time() . mt_rand(1000, 9999));
            $order = [
                'user_id' => "bot_1001",
                'order_id' => $orderId,
                'market_type' => $this->config['market_type'],
                'side' => $orderData['side'],
                'quantity' => $orderData['quantity'],
                'price' => strval($orderData['price']),
                'order_force' => 'gtc',
                'symbol' => $this->config['symbol'],
                'is_bot' => 1
            ];

            $success = $this->orderService->addOrder(
                $this->config['market_type'],
                $this->config['symbol'],
                "bot_1001",
                $order
            );

            if ($success) {
                $currentTime = time();
                // 记录订单信息
                $orderInfo = [
                    'order_id' => $orderId,
                    'symbol' => $orderData['symbol'],
                    'side' => $orderData['side'],
                    'price' => $orderData['price'],
                    'quantity' => $orderData['quantity'],
                    'created_at' => $currentTime,
                    'user_id' => $orderData['user_id']
                ];
                
                $this->activeOrders[$orderId] = $orderInfo;

                // 将订单加入过期检查队列
                $expireCheckData = [
                    'order_id' => $orderId,
                    'expire_at' => $currentTime + $this->config['order_lifetime']
                ];
                $this->expireCheckQueue->push($expireCheckData, 0.1);

                // "[ExternalIndexFollow] Placed {$orderData['side']} order: {$orderData['price']} x {$orderData['quantity']}\n";
            }

        } catch (\Throwable $e) {
            echo "[ExternalIndexFollow] Error processing queued order: {$e->getMessage()}\n";
        }
    }

    /**
     * 处理价格更新
     */
    private function handlePriceUpdate(string $message): void
    {
        try {
            $data = json_decode($message, true);
            if (!$data || !isset($data['price'])) {
                return;
            }

            $newPrice = (float)$data['price'];
            if ($newPrice <= 0) {
                return;
            }

            $oldPrice = $this->currentIndexPrice;
            $this->currentIndexPrice = $newPrice;

            // 计算价格变化绝对差值
            if ($oldPrice > 0) {
                $changeAmount = abs($newPrice - $oldPrice);
                if ($changeAmount >= $this->config['price_change_amount']) {
                    $formattedOldPrice = $this->formatPrice($oldPrice);
                    $formattedNewPrice = $this->formatPrice($newPrice);
                    echo "[ExternalIndexFollow] Price changed by {$changeAmount}: {$formattedOldPrice} -> {$formattedNewPrice}\n";

                    // 价格变化超过阈值，更新最后价格并立即更新订单
                    $this->lastIndexPrice = $oldPrice;
                    $this->updateMarketOrders();
                }
            } else {
                $formattedNewPrice = $this->formatPrice($newPrice);
                echo "[ExternalIndexFollow] Initial price received: {$formattedNewPrice}\n";

                // 首次收到价格，立即挂单
                if ($this->started) {
                    $this->lastIndexPrice = 0;
                    $this->updateMarketOrders();
                }
            }

        } catch (\Throwable $e) {
            echo "[ExternalIndexFollow] Error parsing price update: {$e->getMessage()}\n";
        }
    }

    /**
     * 判断是否需要更新订单
     */
    private function shouldUpdateOrders(): bool
    {
        $currentTime = time();

        // 如果还没有挂过单，需要挂单
        if (empty($this->activeOrders)) {
            return true;
        }

        // 如果距离上次挂单时间过短，不更新（防止频繁挂单）
        if ($currentTime - $this->lastOrderTime < 5) {
            return false;
        }

        // 检查当前价格是否超出已挂单的价格范围
        if ($this->currentIndexPrice > 0) {
            // 应用价格跟随百分比
            $followPercent = $this->config['price_follow_percent'] / 100;
            $basePrice = $this->currentIndexPrice * $followPercent;
            
            // 获取最大价差范围（使用最后一级的价差）
            $maxLevel = end($this->config['depth_levels']);
            $maxSpreadAmount = $basePrice * ($maxLevel['spread_percent'] / 100);
            $minPrice = $basePrice - $maxSpreadAmount;
            $maxPrice = $basePrice + $maxSpreadAmount;

            foreach ($this->activeOrders as $order) {
                $orderPrice = $order['price'];
                if ($orderPrice < $minPrice || $orderPrice > $maxPrice) {
                    echo "[ExternalIndexFollow] Order price {$orderPrice} out of range [{$minPrice}, {$maxPrice}], need update\n";
                    return true; // 有订单超出范围，需要更新
                }
            }
        }

        return false;
    }

    /**
     * 更新市场订单
     */
    private function updateMarketOrders(): void
    {
        // 先取消所有现有订单
        $this->cancelAllOrders();

        // 重新挂单
        $this->placeNewOrders();

        $this->lastOrderTime = time();
    }

    /**
     * 挂新订单
     */
    private function placeNewOrders(): void
    {
        if ($this->currentIndexPrice <= 0) {
            return;
        }

        // 应用价格跟随百分比
        $followPercent = $this->config['price_follow_percent'] / 100;
        $basePrice = $this->currentIndexPrice * $followPercent;

        $formattedCurrentPrice = $this->formatPrice($this->currentIndexPrice);
        $formattedBasePrice = $this->formatPrice($basePrice);
        //echo "[ExternalIndexFollow] Placing new precise orders around index price {$formattedCurrentPrice}, base price {$formattedBasePrice} (follow: {$this->config['price_follow_percent']}%)\n";

        // 根据深度配置精细化挂单
        foreach ($this->config['depth_levels'] as $level) {
            $spreadPercent = $level['spread_percent'];
            $quantityRatio = $level['quantity_ratio'];
            
            // 基于调整后的基础价格计算价格
            $spreadAmount = $basePrice * ($spreadPercent / 100);
            $buyPrice = $basePrice - $spreadAmount;
            $sellPrice = $basePrice + $spreadAmount;
            
            // 确保价格符合tick_size
            $buyPrice = $this->alignToTickSize($buyPrice);
            $sellPrice = $this->alignToTickSize($sellPrice);
            
            // 计算数量
            $quantity = $this->calculateDepthQuantity($quantityRatio);
            
            // 交叉挂单
            $this->queueOrder('buy', $buyPrice, $quantity);
            $this->queueOrder('sell', $sellPrice, $quantity);
            
           // echo "[ExternalIndexFollow] Level spread={$spreadPercent}%: Buy@{$buyPrice} Sell@{$sellPrice} Qty={$quantity}\n";
        }
    }

    /**
     * 将订单加入队列
     */
    private function queueOrder(string $side, float $price, float $quantity): void
    {
        // 确保价格和数量符合精度要求
        $formattedPrice = $this->formatPrice($price);
        $formattedQuantity = $this->formatQuantity($quantity);
        
        $orderData = [
            'symbol' => $this->config['symbol'],
            'user_id' => $this->config['bot_user_id'],
            'side' => $side,
            'type' => 'limit',
            'price' => $formattedPrice,
            'quantity' => $formattedQuantity,
        ];

        // 将订单加入队列
        $success = $this->orderQueue->push($orderData, 0.1); // 0.1秒超时
        
        if (!$success) {
            //echo "[ExternalIndexFollow] Failed to queue {$side} order: queue is full\n";
        }
    }

    /**
     * 根据价格变化方向生成市价单
     */
    private function maybeCreateMarketOrders(): void
    {
        if ($this->currentIndexPrice <= 0) {
            return;
        }

        // 计算价格变化幅度
        $priceChange = $this->currentIndexPrice - $this->lastIndexPrice;
        $changeAmount = abs($priceChange);
        
        // 判断是否需要生成市价单
        $shouldCreateOrder = false;
        $orderType = 'normal'; // normal 或 aggressive
        
        // 如果价格变化超过阈值，强制生成激进市价单
        if ($changeAmount >= $this->config['price_change_amount']) {
            $shouldCreateOrder = true;
            $orderType = 'aggressive';
            echo "[ExternalIndexFollow] Large price change detected ({$changeAmount} >= {$this->config['price_change_amount']}), triggering aggressive market order\n";
        } else {
            // 正常概率生成普通市价单
            $probability = $this->config['market_order_probability'];
            if (mt_rand(1, 100) <= $probability) {
                $shouldCreateOrder = true;
                $orderType = 'normal';
            }
        }
        
        if (!$shouldCreateOrder) {
            return;
        }

        // 根据价格变化方向决定市价单方向
        $side = $this->determineMarketOrderSide();
        
        // 根据订单类型计算市价单数量
        $quantity = $this->calculateAdaptiveMarketOrderQuantity($changeAmount, $orderType);
        $formattedQuantity = $this->formatQuantity($quantity);
        
        $direction = $priceChange > 0 ? '↗' : ($priceChange < 0 ? '↘' : '→');
        $targetLevels = $this->getTargetDepthLevels($changeAmount, $orderType);
        
        echo "[ExternalIndexFollow] Creating {$orderType} market {$side} order: quantity={$formattedQuantity} (price {$direction} {$this->lastIndexPrice}->{$this->currentIndexPrice}, change={$changeAmount}, targeting {$targetLevels} levels)\n";
        
        try {
            $orderId = intval(time() . mt_rand(1000, 9999));
            $order = [
                'symbol' => $this->config['symbol'],
                'user_id' => 'bot_1002',
                'order_id' => $orderId,
                'side' => $side,
                'type' => 'market',
                'time_in_force' => 'gtc',
                'quantity' => strval($formattedQuantity),
                'is_bot' => 0
            ];
            $success = $this->orderService->addOrder(
                $this->config['market_type'],
                $this->config['symbol'],
                "bot_1002",
                $order
            );

            if ($success) {
                echo "[ExternalIndexFollow] {$orderType} market {$side} order executed: {$formattedQuantity} (swept {$targetLevels} levels)\n";
                
                // 立即在对应方向的最优价补单
                $this->replenishAfterMarketOrder($side, $formattedQuantity);
            } else {
                echo "[ExternalIndexFollow] Failed to place {$orderType} market {$side} order\n";
            }

        } catch (\Throwable $e) {
            echo "[ExternalIndexFollow] Error placing {$orderType} market {$side} order: {$e->getMessage()}\n";
        }
    }

    /**
     * 根据价格变化方向决定市价单方向
     */
    private function determineMarketOrderSide(): string
    {
        // 如果没有上一个价格，随机选择
        if ($this->lastIndexPrice <= 0) {
            return mt_rand(0, 1) ? 'buy' : 'sell';
        }

        $priceChange = $this->currentIndexPrice - $this->lastIndexPrice;
        
        // 价格上涨：倾向于买入（吃卖单）
        if ($priceChange > 0) {
            // 80% 概率买入，20% 概率卖出（增加一些随机性）
            return mt_rand(1, 100) <= 80 ? 'buy' : 'sell';
        } 
        // 价格下跌：倾向于卖出（吃买单）
        elseif ($priceChange < 0) {
            // 80% 概率卖出，20% 概率买入
            return mt_rand(1, 100) <= 80 ? 'sell' : 'buy';
        } 
        // 价格无变化：随机选择但稍微倾向于买入（模拟市场偏好）
        else {
            return mt_rand(1, 100) <= 55 ? 'buy' : 'sell';
        }
    }

    /**
     * 市价单执行后补单
     */
    private function replenishAfterMarketOrder(string $marketSide, float $quantity): void
    {
        if ($this->currentIndexPrice <= 0) {
            return;
        }

        //echo "[ExternalIndexFollow] Market {$marketSide} order consumed depth, replenishing based on latest index price: {$this->currentIndexPrice}\n";

        // 基于最新指数价格重新计算所有档位的最优价格并补单
        // 但只在被吃掉的那一方补单
        $targetSide = ($marketSide === 'buy') ? 'sell' : 'buy';
        
        // 应用价格跟随百分比
        $followPercent = $this->config['price_follow_percent'] / 100;
        $basePrice = $this->currentIndexPrice * $followPercent;
        
        // 判断是否是大单（激进订单），如果是则补充多档深度
        $baseQuantity = $this->config['base_quantity'];
        $isLargeOrder = $quantity > ($baseQuantity * 2); // 超过基础数量2倍认为是大单
        
        if ($isLargeOrder) {
            // 大单后快速补充前3档深度
            //echo "[ExternalIndexFollow] Large market order detected, replenishing multiple depth levels for {$targetSide} side\n";
            
            $levelsToReplenish = min(3, count($this->config['depth_levels']));
            for ($i = 0; $i < $levelsToReplenish; $i++) {
                $level = $this->config['depth_levels'][$i];
                $spreadPercent = $level['spread_percent'];
                $quantityRatio = $level['quantity_ratio'];
                
                // 基于调整后的基础价格计算补单价格
                $spreadAmount = $basePrice * ($spreadPercent / 100);
                
                if ($targetSide === 'sell') {
                    $replenishPrice = $basePrice + $spreadAmount;
                } else {
                    $replenishPrice = $basePrice - $spreadAmount;
                }
                
                $replenishPrice = $this->alignToTickSize($replenishPrice);
                
                // 计算补单数量
                $replenishQuantity = $this->calculateDepthQuantity($quantityRatio);
                
                $levelNum = $i + 1;
                //echo "[ExternalIndexFollow] Replenishing {$targetSide} level {$levelNum}: price={$replenishPrice}, quantity={$replenishQuantity}\n";
                
                // 将补单加入队列
                $this->queueOrder($targetSide, $replenishPrice, $replenishQuantity);
            }
        } else {
            // 普通单只补充最优档位
            $bestLevel = $this->config['depth_levels'][0];
            $spreadPercent = $bestLevel['spread_percent'];
            $quantityRatio = $bestLevel['quantity_ratio'];
            
            // 基于调整后的基础价格计算补单价格
            $spreadAmount = $basePrice * ($spreadPercent / 100);
            
            if ($targetSide === 'sell') {
                $replenishPrice = $basePrice + $spreadAmount;
            } else {
                $replenishPrice = $basePrice - $spreadAmount;
            }
            
            $replenishPrice = $this->alignToTickSize($replenishPrice);
            
            // 计算补单数量：使用最优档位的基础数量
            $replenishQuantity = $this->calculateDepthQuantity($quantityRatio);
            
            //echo "[ExternalIndexFollow] Replenishing {$targetSide} order at latest optimal price {$replenishPrice} (based on index {$this->currentIndexPrice} × {$this->config['price_follow_percent']}% = {$basePrice}), quantity={$replenishQuantity}\n";
            
            // 将补单加入队列
            $this->queueOrder($targetSide, $replenishPrice, $replenishQuantity);
        }
    }

    /**
     * 根据价格变化幅度自适应计算市价单数量
     */
    private function calculateAdaptiveMarketOrderQuantity(float $changeAmount, string $orderType): float
    {
        $baseQuantity = $this->config['base_quantity'];
        
        if ($orderType === 'aggressive') {
            // 激进订单：基于价格变化幅度计算吃单深度
            $targetLevels = $this->getTargetDepthLevels($changeAmount, $orderType);
            
            // 计算目标档位的总数量（4-6档深度）
            $totalQuantity = 0;
            $levelsToSweep = min($targetLevels, count($this->config['depth_levels']));
            
            for ($i = 0; $i < $levelsToSweep; $i++) {
                $level = $this->config['depth_levels'][$i];
                $levelQuantity = $baseQuantity * $level['quantity_ratio'];
                $totalQuantity += $levelQuantity;
            }
            
            // 增加20%-50%的额外数量确保能够清扫目标档位
            $extraPercent = mt_rand(20, 50);
            $totalQuantity *= (1 + $extraPercent / 100);
            
            //echo "[ExternalIndexFollow] Aggressive order targeting {$levelsToSweep} levels, calculated quantity: {$totalQuantity}\n";
            
            return $totalQuantity;
        } else {
            // 普通订单：保持原有逻辑（base_quantity的30%-70%随机）
            $minPercent = 30; // 30%
            $maxPercent = 70; // 70%
            $randomPercent = mt_rand($minPercent, $maxPercent);
            
            $quantity = $baseQuantity * ($randomPercent / 100);
            
            return $quantity;
        }
    }
    
    /**
     * 根据价格变化幅度确定目标清扫档位数
     */
    private function getTargetDepthLevels(float $changeAmount, string $orderType): int
    {
        if ($orderType === 'normal') {
            return 1; // 普通订单只影响1档
        }
        
        $threshold = $this->config['price_change_amount'];
        
        // 根据价格变化幅度的倍数决定清扫档位
        $multiplier = $changeAmount / $threshold;
        
        if ($multiplier >= 3.0) {
            return 3; // 变化幅度>=3倍阈值：清扫6档
        } elseif ($multiplier >= 2.0) {
            return 2; // 变化幅度>=2倍阈值：清扫5档
        } elseif ($multiplier >= 1.0) {
            return 1; // 变化幅度>=1倍阈值：清扫4档
        } else {
            return 1; // 小幅变化：清扫2档
        }
    }

    /**
     * 根据深度比例计算数量
     */
    private function calculateDepthQuantity(float $quantityRatio): float
    {
        $baseQuantity = $this->config['base_quantity'];
        $quantity = $baseQuantity * $quantityRatio;
        
        // 添加少量随机变化（±10%）增加真实感
        $randomFactor = 1 + (mt_rand(-10, 10) / 100);
        $quantity *= $randomFactor;
        
        return $this->formatQuantity($quantity);
    }

    /**
     * 价格对齐到tick_size
     */
    private function alignToTickSize(float $price): float
    {
        $tickSize = $this->config['tick_size'];
        $aligned = round($price / $tickSize) * $tickSize;
        return $this->formatPrice($aligned);
    }

    /**
     * 取消所有订单
     */
    private function cancelAllOrders(): void
    {
        if (empty($this->activeOrders)) {
            return;
        }

        //echo "[ExternalIndexFollow] Canceling " . count($this->activeOrders) . " active orders\n";

        foreach ($this->activeOrders as $orderId => $order) {
            try {
                $this->orderService->cancelOrder(
                    $this->config['market_type'],
                    $this->config['symbol'],
                    (int)$order['order_id'],
                    "bot_1003"
                );
            } catch (\Throwable $e) {
                //echo "[ExternalIndexFollow] Error canceling order {$orderId}: {$e->getMessage()}\n";
            }
        }

        $this->activeOrders = [];
    }



    /**
     * 清理超出价格范围的订单
     */
    private function cleanupOutOfRangeOrders(): void
    {
        if ($this->currentIndexPrice <= 0 || empty($this->activeOrders)) {
            return;
        }

        // 应用价格跟随百分比
        $followPercent = $this->config['price_follow_percent'] / 100;
        $basePrice = $this->currentIndexPrice * $followPercent;
        
        // 获取最大价差范围（使用最后一级的价差）
        $maxLevel = end($this->config['depth_levels']);
        $maxSpreadAmount = $basePrice * ($maxLevel['spread_percent'] / 100);
        $minPrice = $basePrice - $maxSpreadAmount;
        $maxPrice = $basePrice + $maxSpreadAmount;

        $outOfRangeOrders = [];

        foreach ($this->activeOrders as $orderId => $order) {
            $orderPrice = $order['price'];
            if ($orderPrice < $minPrice || $orderPrice > $maxPrice) {
                $outOfRangeOrders[] = $orderId;
            }
        }

        if (!empty($outOfRangeOrders)) {
            //echo "[ExternalIndexFollow] Cleaning up " . count($outOfRangeOrders) . " out-of-range orders\n";

            foreach ($outOfRangeOrders as $orderId) {
                $order = $this->activeOrders[$orderId];
                try {
                    MatchEngineBaseProcess::cancelOrder(
                        $order['symbol'],
                        (int)$order['order_id']
                    );
                    unset($this->activeOrders[$orderId]);
                } catch (\Throwable $e) {
                   // echo "[ExternalIndexFollow] Error canceling out-of-range order {$orderId}: {$e->getMessage()}\n";
                }
            }
        }
    }

    /**
     * 获取策略状态
     */
    public function getStatus(): array
    {
        $buyOrders = 0;
        $sellOrders = 0;
        foreach ($this->activeOrders as $order) {
            if ($order['side'] === 'buy') {
                $buyOrders++;
            } elseif ($order['side'] === 'sell') {
                $sellOrders++;
            }
        }

        return [
            'symbol' => $this->config['symbol'],
            'market_type' => $this->config['market_type'],
            'current_price' => $this->formatPrice($this->currentIndexPrice),
            'last_price' => $this->formatPrice($this->lastIndexPrice),
            'active_orders' => count($this->activeOrders),
            'buy_orders' => $buyOrders,
            'sell_orders' => $sellOrders,
            'order_queue_length' => $this->orderQueue->length(),
            'expire_check_queue_length' => $this->expireCheckQueue->length(),
            'started' => $this->started,
            'completed' => $this->completed,
            'last_order_time' => $this->lastOrderTime,
            'last_cleanup_time' => $this->lastCleanupTime,
            'base_precision' => $this->config['base_precision'],
            'quote_precision' => $this->config['quote_precision'],
            'price_change_amount' => $this->config['price_change_amount'],
            'order_lifetime' => $this->config['order_lifetime'],
            'min_depth' => count($this->config['depth_levels']),
            'tick_size' => $this->config['tick_size'],
            'base_quantity' => $this->config['base_quantity'],
            'depth_levels_count' => count($this->config['depth_levels']),
            'price_follow_percent' => $this->config['price_follow_percent'],
        ];
    }

    /**
     * 格式化价格精度
     */
    private function formatPrice(float $price): float
    {
        $precision = $this->config['quote_precision'];
        return round($price, $precision);
    }

    /**
     * 格式化数量精度
     */
    private function formatQuantity(float $quantity): float
    {
        $precision = $this->config['base_precision'];
        return round($quantity, $precision);
    }



    /**
     * 启动市价单生成协程
     */
    private function startMarketOrderGenerator(): void
    {
        $this->marketOrderCoroutineId = Coroutine::create(function() {
            try {
                while (!$this->completed) {
                    // 定期生成市价单（每5秒检查一次）
                    Coroutine::sleep(1);
                    
                    if ($this->currentIndexPrice > 0) {
                        $this->maybeCreateMarketOrders();
                    }
                }
            } catch (\Throwable $e) {
                //echo "[ExternalIndexFollow] Market order generator error: {$e->getMessage()}\n";
            }
        });
    }

    /**
     * 停止市价单生成协程
     */
    private function stopMarketOrderGenerator(): void
    {
        if ($this->marketOrderCoroutineId > 0) {
            $this->marketOrderCoroutineId = 0;
        }
    }
}