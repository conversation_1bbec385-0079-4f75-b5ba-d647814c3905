<?php

declare(strict_types=1);

/**
 * SpotInnerDataAggregatorService.php
 * 现货内部数据聚合服务 - 处理撮合引擎产生的成交数据聚合
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/1/15
 * Website:algoquant.org
 */

namespace App\MarketData\Service;

use App\Enum\AsyncExecutorKey;
use App\Enum\MarketData\KlineIndex;
use App\Enum\MarketData\KlinePeriod;
use App\Enum\MarketData\TickerSubscribeKey;
use App\Enum\MarketData\TickerSyncKey;
use App\Enum\MarketType;
use App\Job\MarketData\KlineBulkRetryJob;
use App\MarketData\Process\MatchEngine\Logger\MatchEngineLogger;
use App\Service\RedisFactory\MarketRedis;
use Elasticsearch\Client;
use Hyperf\Di\Annotation\Inject;
use Psr\Container\ContainerInterface;

class InnerDataAggregatorService
{
    #[Inject]
    private ContainerInterface $container;

    #[Inject]
    private MarketRedis $redis;

    private Client $elasticsearch;
    private MatchEngineLogger $logger;

    /**
     * 当前K线数据缓存 [currency_id][market_type][period] => kline_data
     */
    private array $currentKlines = [];

    /**
     * Ticker数据缓存 [currency_id][market_type] => ticker_data
     */
    private array $tickerData = [];

    /**
     * 最新价格数据缓存 [currency_id][market_type] => price_data
     */
    private array $latestPrices = [];

    /**
     * 币种数据映射
     */
    private array $currencies = [];

    /**
     * K线持久化缓冲区 - 按周期分组存储完成的K线
     */
    private array $persistenceBuffer = [];

    /**
     * 周期完成标记 - 记录已完成的周期时间戳
     */
    private array $completedPeriods = [];

    /**
     * 上次缓存时间
     */
    private int $lastCacheTime = 0;

    /**
     * @var MarketType 交易市场
     */
    private MarketType $marketType;

    /**
     * 周期毫秒映射
     */
    private array $periodMilliseconds = [
        '1m' => 60 * 1000,
        '3m' => 3 * 60 * 1000,
        '5m' => 5 * 60 * 1000,
        '15m' => 15 * 60 * 1000,
        '30m' => 30 * 60 * 1000,
        '1h' => 60 * 60 * 1000,
        '2h' => 2 * 60 * 60 * 1000,
        '4h' => 4 * 60 * 60 * 1000,
        '1d' => 24 * 60 * 60 * 1000,
        '1w' => 7 * 24 * 60 * 60 * 1000,
        '1M' => 30 * 24 * 60 * 60 * 1000,
    ];

    /**
     * 周期执行定时器 ID
     */
    private int $periodicTimerId = 0;

    public function __construct(MatchEngineLogger $logger)
    {
        $this->logger = $logger;
    }

    /**
     * 创建不使用代理的Elasticsearch客户端
     */
    private function createElasticsearchClientWithoutProxy(): \Elasticsearch\Client
    {
        $container = \Hyperf\Context\ApplicationContext::getContainer();
        $builder = $container->get(\Hyperf\Elasticsearch\ClientBuilderFactory::class)->create();

        $host = [
            'host' => env('ES_HOST', 'localhost'),
            'port' => env('ES_PORT', 9200),
            'scheme' => env('ES_SSL_VERIFY', false) ? 'https' : 'http',
        ];

        // 如果配置了用户名和密码，添加认证
        $username = env('ES_USER');
        $password = env('ES_PASSWORD');
        if ($username && $password) {
            $host['user'] = $username;
            $host['pass'] = $password;
        }

        $builder->setHosts([$host]);

        // SSL 验证设置
        if (!env('ES_SSL_VERIFY', false)) {
            $builder->setSSLVerification(false);
        }

        // 关键：设置不使用代理的HTTP客户端配置
        $builder->setConnectionParams([
            'client' => [
                'timeout' => 30,
                'connect_timeout' => 10,
                // 明确禁用代理
                'proxy' => null,
                // 确保不继承全局代理配置
                'curl' => [
                    CURLOPT_PROXY => null,
                    CURLOPT_PROXYPORT => null,
                ],
            ]
        ]);

        $client = $builder->build();
        return $client;
    }

    /**
     * 初始化服务
     */
    public function initialize(array $currencies,MarketType $marketType): void
    {
        try {
            // 创建专用的Elasticsearch客户端，禁用代理
            $this->elasticsearch = $this->createElasticsearchClientWithoutProxy();

            $this->currencies = $currencies;

            $this->marketType = $marketType;
            
            // 尝试从Redis恢复状态
            $this->restoreStateFromRedis();
            
            // 启动周期性任务，每10秒执行一次
            if ($this->periodicTimerId === 0) {
                $this->periodicTimerId = \Swoole\Timer::tick(10000, function() {
                    go(function() {
                        try {
                            $this->runPeriodicTasks();
                        } catch (\Throwable $e) {
                            $this->logger->logError('Inner data aggregator periodic tasks error: ' . $e->getMessage(), $e);
                        }
                    });
                });
            }
            
        } catch (\Throwable $e) {
            $this->logger->logError("Failed to initialize inner data aggregator", $e);
            throw $e;
        }
    }

    /**
     * 处理成交事件（协程异步处理）
     */
    public function handleTradeEvent(array $tradeEvent): void
    {
        // 使用协程异步处理，避免阻塞撮合引擎
        go(function() use ($tradeEvent) {
            try {
                $this->processTradeEvent($tradeEvent);
            } catch (\Throwable $e) {
                $this->logger->logError("Failed to process inner trade event", $e);
            }
        });
    }

    /**
     * 实际处理成交事件
     */
    private function processTradeEvent(array $tradeEvent): void
    {
        // 提取必要数据
        $symbol = $tradeEvent['symbol'] ?? '';
        $price = (float)($tradeEvent['price'] ?? 0);
        $quantity = (float)($tradeEvent['quantity'] ?? 0);
        // 撮合引擎返回的时间戳已经是毫秒级别，fallback时转换当前时间为毫秒
        $tradeTime = (int)($tradeEvent['trade_time'] ?? time() * 1000);
        $side = $tradeEvent['side'] ?? 'buy';



        // 验证基础数据
        if ($price <= 0 || $quantity <= 0) {
            return;
        }

        // 验证时间戳不能是未来时间（允许5分钟的时钟偏差）
        // 撮合引擎返回的时间戳已经是毫秒级别，无需乘以1000
        $maxAllowedTime = (time() + 300) * 1000;
        if ($tradeTime > $maxAllowedTime) {
            $this->logger->logError("Future timestamp in trade event", [
                'trade_time' => $tradeTime,
                'max_allowed' => $maxAllowedTime
            ]);
            return;
        }

        if(!isset($tradeEvent['currency_id'])){
            // 查找币种ID
            $currencyId = $this->findCurrencyIdBySymbol($symbol);
            if (!$currencyId) {
                return;
            }
        }else{
            $currencyId = $tradeEvent['currency_id'];
        }

        // 同步处理三种数据聚合，保留但不处理k线的聚合
//        try {
//            // K线数据更新
//            $this->updateKlineData($currencyId, $marketType, $price, $quantity, $tradeTime);
//        } catch (\Throwable $e) {
//            $this->logger->logError("Failed to update kline data", [
//                'error' => $e->getMessage(),
//                'currency_id' => $currencyId,
//                'symbol' => $symbol
//            ]);
//        }

        try {
            // Ticker数据更新
            $this->updateTickerData($currencyId, $this->marketType->value, $price, $quantity, $tradeTime);
        } catch (\Throwable $e) {
            $this->logger->logError("Failed to update ticker data", [
                'error' => $e->getMessage(),
                'currency_id' => $currencyId
            ]);
        }

        try {
            // 最新价格更新
            $this->updateLatestPrice($currencyId, $this->marketType->value, $price, $quantity, $tradeTime, $side);
        } catch (\Throwable $e) {
            $this->logger->logError("Failed to update latest price", [
                'error' => $e->getMessage(),
                'currency_id' => $currencyId
            ]);
        }
    }

    /**
     * 根据交易对符号查找币种ID
     */
    private function findCurrencyIdBySymbol(string $symbol): ?int
    {
        foreach ($this->currencies as $currencyKey => $currencyData) {
            if ($currencyData['symbol'] === $symbol) {
                return $currencyData['id'];
            }
        }
        return null;
    }

    /**
     * 更新K线数据
     */
    private function updateKlineData(int $currencyId, int $marketType, float $price, float $quantity, int $tradeTime): void
    {
        foreach (KlinePeriod::cases() as $period) {
            $periodValue = $period->value;

            // 跳过1Y周期
            if ($periodValue === '1Y') {
                continue;
            }
            
            $periodStart = $this->getPeriodStartTime($tradeTime, $periodValue);

            // 检查是否需要初始化或切换周期
            if (!isset($this->currentKlines[$currencyId][$marketType][$periodValue])) {
                $this->currentKlines[$currencyId][$marketType][$periodValue] = $this->createEmptyKline(
                    $currencyId, $marketType, $periodValue, $periodStart
                );
            }

            $kline = &$this->currentKlines[$currencyId][$marketType][$periodValue];

            // 检查是否跨周期
            if ($periodStart > $kline['open_time']) {
                // K线周期完成，标记为待持久化
                if ($kline['trades_count'] > 0) {
                    $this->markKlineForPersistence($periodValue, $kline['open_time'], $kline);
                }

                // 获取上一根K线的收盘价作为新K线的开盘价
                $previousClosePrice = ($kline['trades_count'] > 0) ? $kline['ohlcv'][3] : 0;

                // 创建新周期K线
                $kline = $this->createEmptyKline($currencyId, $marketType, $periodValue, $periodStart, $previousClosePrice);
            }

            // 更新OHLCV数据
            $this->updateOHLCV($kline, $price, $quantity);
        }
    }

    /**
     * 更新24小时Ticker数据（基于每天早上8点重置）
     */
    private function updateTickerData(int $currencyId, int $marketType, float $price, float $quantity, int $tradeTime): void
    {
        if (!isset($this->tickerData[$currencyId][$marketType])) {
            $this->tickerData[$currencyId][$marketType] = [
                'price_change' => 0.0,
                'price_changeP' => 0.0,
                'pre_close_price' => $price,
                'last_price' => $price,
                'last_qty' => $quantity,
                'open_price' => $price,
                'high_price' => $price,
                'low_price' => $price,
                'volume' => 0.0,
                'timestamp' => $tradeTime,
                'reset_period' => $this->get8amTimestamp($tradeTime) // 记录当前8点周期
            ];
        }

        $ticker = &$this->tickerData[$currencyId][$marketType];
        
        // 防止旧数据覆盖新数据
        if ($tradeTime < $ticker['timestamp']) {
            return;
        }
        
        // 检查是否需要重置ticker数据（基于每天早上8点）
        $current8amTimestamp = $this->get8amTimestamp($tradeTime);
        $tickerResetPeriod = $ticker['reset_period'] ?? $this->get8amTimestamp($ticker['timestamp']);
        
        if ($current8amTimestamp !== $tickerResetPeriod) {
            // 新的8点周期，重置数据
            $ticker['pre_close_price'] = $ticker['last_price'];
            $ticker['open_price'] = $price;
            $ticker['high_price'] = $price;
            $ticker['low_price'] = $price;
            $ticker['volume'] = 0.0;
            $ticker['reset_period'] = $current8amTimestamp; // 更新重置周期
        }

        // 更新数据
        $ticker['last_price'] = $price;
        $ticker['last_qty'] = $quantity;
        $ticker['high_price'] = max($ticker['high_price'], $price);
        $ticker['low_price'] = min($ticker['low_price'], $price);
        $ticker['volume'] += $quantity;
        $ticker['timestamp'] = $tradeTime;

        // 计算价格变化
        if ($ticker['pre_close_price'] > 0) {
            $ticker['price_change'] = $price - $ticker['pre_close_price'];
            $ticker['price_changeP'] = ($ticker['price_change'] / $ticker['pre_close_price']) * 100;
        }
        
        // 计算到下一个8点的时间戳，用于设置Redis过期时间
        $nextResetTimestamp = $this->getNext8amTimestamp($tradeTime);
        $ttl = max(1, intval(($nextResetTimestamp - intval($tradeTime / 1000))));
        
        // 实时写入Redis
        try {
            $key = TickerSyncKey::getInnerAggregatorTickerKey($currencyId, $marketType);
            $this->redis->hMSet($key, $ticker);
            $this->redis->expire($key, $ttl); // 设置到下一个8点的过期时间

            //将ticker数据发布到redis 中用于推送前端
            $ticker['currency_id'] = $currencyId;
            $ticker['market_type'] = $marketType;
            $this->redis->publish(TickerSubscribeKey::INNER_TICKER_MESSAGE->value,json_encode($ticker));
        } catch (\Throwable $e) {
            $this->logger->logError("Failed to write ticker to Redis", [
                'currency_id' => $currencyId,
                'market_type' => $marketType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 更新最新价格数据
     */
    private function updateLatestPrice(int $currencyId, int $marketType, float $price, float $quantity, int $tradeTime, string $side): void
    {
        // 防止旧数据覆盖新数据
        if (isset($this->latestPrices[$currencyId][$marketType])) {
            $existingTimestamp = $this->latestPrices[$currencyId][$marketType]['timestamp'];
            if ($tradeTime < $existingTimestamp) {
                return;
            }
        }
        
        $this->latestPrices[$currencyId][$marketType] = [
            'price' => $price,
            'quantity' => $quantity,
            'timestamp' => $tradeTime,
            'side' => $side
        ];
        
        // 实时写入Redis
        try {
            $key = TickerSyncKey::getInnerAggregatorTradeKey($currencyId, $marketType);
            $this->redis->hMSet($key, $this->latestPrices[$currencyId][$marketType]);
            $this->redis->expire($key, 3600); // 1小时过期
        } catch (\Throwable $e) {
            $this->logger->logError("Failed to write latest price to Redis", [
                'currency_id' => $currencyId,
                'market_type' => $marketType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 创建空K线数据
     */
    private function createEmptyKline(int $currencyId, int $marketType, string $period, int $openTime, ?float $openPrice = null): array
    {
        $closeTime = $openTime + $this->periodMilliseconds[$period] - 1;
        $price = $openPrice ?? 0.0;

        return [
            'currency_id' => $currencyId,
            'market_type' => $marketType,
            'period' => $period,
            'open_time' => $openTime,
            'close_time' => $closeTime,
            'ohlcv' => [$price, $price, $price, $price, 0.0, 0.0, 0], // [open, high, low, close, volume, quote_volume, trades_count]
            'trades_count' => 0,
            'is_dirty' => false
        ];
    }

    /**
     * 获取指定时间的周期开始时间
     */
    private function getPeriodStartTime(int $timestamp, string $period): int
    {
        $periodMs = $this->periodMilliseconds[$period];
        return intval($timestamp / $periodMs) * $periodMs;
    }

    /**
     * 更新OHLCV数据
     */
    private function updateOHLCV(array &$kline, float $price, float $quantity): void
    {
        $ohlcv = &$kline['ohlcv'];

        // 首次交易
        if ($kline['trades_count'] == 0) {
            if ($ohlcv[0] == 0.0) {
                $ohlcv[0] = $price; // open
            }
            $ohlcv[1] = max($ohlcv[0], $price); // high
            $ohlcv[2] = min($ohlcv[0], $price); // low
            $ohlcv[3] = $price; // close
        } else {
            // 更新高低价和收盘价
            $ohlcv[1] = max($ohlcv[1], $price); // high
            $ohlcv[2] = min($ohlcv[2], $price); // low
            $ohlcv[3] = $price; // close
        }

        // 累加成交量和成交额
        $ohlcv[4] += $quantity; // volume
        $ohlcv[5] += $price * $quantity; // quote_volume
        $ohlcv[6]++; // trades_count (索引6)
        $kline['trades_count']++;

        // 标记为需要持久化
        $kline['is_dirty'] = true;
    }

    /**
     * 标记K线为待持久化
     */
    private function markKlineForPersistence(string $period, int $openTime, array $kline): void
    {
        $periodKey = "{$period}-{$openTime}";
        
        // 初始化该周期的缓冲区
        if (!isset($this->persistenceBuffer[$periodKey])) {
            $this->persistenceBuffer[$periodKey] = [];
        }
        
        // 添加K线到对应周期的缓冲区
        $this->persistenceBuffer[$periodKey][] = $kline;
        
        // 标记该周期为完成
        $this->completedPeriods[$periodKey] = time();
    }

    /**
     * 运行周期性任务
     */
    public function runPeriodicTasks(): void
    {
        try {
            // 持久化完成的K线
//            $this->persistCompletedKlines();
            
            // 缓存数据到Redis
            $now = time();
            if (($now - $this->lastCacheTime) >= 10) {
                $this->cacheToRedis();
                $this->lastCacheTime = $now;
            }
        } catch (\Throwable $e) {
            $this->logger->logError("Error during inner data aggregator periodic tasks", $e);
        }
    }

    /**
     * 持久化完成的K线到ES
     */
    private function persistCompletedKlines(): void
    {
        // 1. 持久化已完成周期的K线
        $this->persistFinishedPeriodKlines();
        
        // 2. 持久化当前正在聚合的K线（定时任务）
        $this->persistCurrentKlines();
    }
    
    /**
     * 持久化已完成周期的K线
     */
    private function persistFinishedPeriodKlines(): void
    {
        if (empty($this->persistenceBuffer)) {
            return;
        }

        $readyPeriods = [];
        
        // 立即持久化所有已完成的周期
        foreach ($this->completedPeriods as $periodKey => $completedTime) {
            $readyPeriods[] = $periodKey;
        }
        
        if (empty($readyPeriods)) {
            return;
        }

        // 同步持久化就绪的周期数据
        try {
            foreach ($readyPeriods as $periodKey) {
                if (isset($this->persistenceBuffer[$periodKey])) {
                    $klines = $this->persistenceBuffer[$periodKey];
                    $this->bulkPersistToES($klines);
                    
                    // 清理已持久化的数据
                    unset($this->persistenceBuffer[$periodKey]);
                    unset($this->completedPeriods[$periodKey]);
                }
            }
        } catch (\Throwable $e) {
            $this->logger->logError("Failed to persist finished period klines to ES", $e);
        }
    }
    
    /**
     * 持久化当前正在聚合的K线（定时任务）
     */
    private function persistCurrentKlines(): void
    {
        $currentKlines = [];
        
        // 收集所有有交易的当前K线
        foreach ($this->currentKlines as $currencyId => $marketTypes) {
            foreach ($marketTypes as $marketType => $periods) {
                foreach ($periods as $period => $kline) {
                    if ($kline['trades_count'] > 0) {
                        $currentKlines[] = $kline;
                    }
                }
            }
        }
        
        if (!empty($currentKlines)) {
            try {
                $this->bulkPersistToES($currentKlines);
            } catch (\Throwable $e) {
                $this->logger->logError("Failed to persist current klines to ES", $e);
            }
        }
    }

    /**
     * 批量持久化K线数据到ES
     */
    private function bulkPersistToES(array $klines): void
    {
        if (empty($klines)) {
            return;
        }

        // 预处理K线数据，添加时间戳验证，防止旧数据覆盖新数据
        $processedKlines = [];
        $params = ['body' => []];
        
        foreach ($klines as $kline) {
            $symbol = $this->getSymbolByCurrencyId($kline['currency_id']);
            if (!$symbol) {
                continue;
            }

            // 验证数据完整性和时间戳
            if (!$this->validateKlineData($kline)) {
                $this->logger->logError("Invalid kline data detected", ['kline' => $kline]);
                continue;
            }

            $indexName = KlineIndex::getInnerIndexName($symbol, $kline['market_type']);
            $docId = KlineIndex::generateInnerKlineId($symbol, $kline['market_type'], $kline['period'], $kline['open_time']);

            // 构建兼容KlineBulkRetryJob的数据格式
            $retryCompatibleKline = [
                'symbol' => $symbol,
                'market_type' => $kline['market_type'],
                'period' => $kline['period'],
                'open_time' => $kline['open_time'],
                'close_time' => $kline['close_time'],
                'ohlcv' => array_map('floatval', $kline['ohlcv'])
            ];

            $processedKlines[] = $retryCompatibleKline;

            $params['body'][] = [
                'update' => ['_index' => $indexName, '_id' => $docId]
            ];
            $params['body'][] = [
                'doc' => $retryCompatibleKline,
                'doc_as_upsert' => true
            ];
        }

        if (empty($processedKlines)) {
            return;
        }

        try {
            $response = $this->elasticsearch->bulk($params);
            
            if ($response['errors']) {
                // 解析失败的项目
                $failedKlines = $this->parseFailedKlines($response, $processedKlines);
                
                if (!empty($failedKlines)) {
                    $this->logger->logError("ES bulk update partial failure for inner klines", [
                        'failed_count' => count($failedKlines),
                        'total_count' => count($processedKlines),
                        'response_errors' => $response['errors']
                    ]);
                    
                    // 将失败的数据放入异步队列重试
                    $this->enqueueFailedKlines($failedKlines);
                } else {
                    $this->logger->logProcessStatus("All inner klines persisted successfully despite errors flag");
                }
            }
            
        } catch (\Throwable $e) {
            $this->logger->logError("Failed to bulk persist inner klines", $e);
            
            // 整批失败，将所有数据放入异步队列重试
            $this->enqueueFailedKlines($processedKlines);
        }
    }

    /**
     * 验证K线数据的完整性和时间戳
     */
    private function validateKlineData(array $kline): bool
    {
        // 检查必要字段
        $requiredFields = ['currency_id', 'market_type', 'period', 'open_time', 'close_time', 'ohlcv'];
        foreach ($requiredFields as $field) {
            if (!isset($kline[$field])) {
                return false;
            }
        }

        // 验证时间戳逻辑性
        if ($kline['open_time'] >= $kline['close_time']) {
            return false;
        }

        // 验证OHLCV数据
        if (!is_array($kline['ohlcv']) || count($kline['ohlcv']) < 7) {
            return false;
        }

        // 验证时间戳不能是未来时间（允许5分钟的时钟偏差）
        // 撮合引擎返回的时间戳已经是毫秒级别，无需乘以1000
        $maxAllowedTime = (time() + 300) * 1000; // 当前时间 + 5分钟，转换为毫秒
        if ($kline['open_time'] > $maxAllowedTime) {
            $this->logger->logError("Future timestamp detected in kline data", [
                'open_time' => $kline['open_time'],
                'current_time' => time() * 1000,
                'max_allowed' => $maxAllowedTime
            ]);
            return false;
        }

        return true;
    }

    /**
     * 解析ES响应中失败的K线数据
     */
    private function parseFailedKlines(array $response, array $processedKlines): array
    {
        $failedKlines = [];
        
        if (!isset($response['items']) || !is_array($response['items'])) {
            // 如果无法解析具体失败项，返回所有数据进行重试
            return $processedKlines;
        }

        foreach ($response['items'] as $index => $item) {
            $operation = $item['update'] ?? $item['index'] ?? null;
            
            if ($operation && isset($operation['error'])) {
                // 检查是否是版本冲突（数据已存在且更新）
                if (isset($operation['error']['type']) && $operation['error']['type'] === 'version_conflict_engine_exception') {
                    // 版本冲突通常意味着数据已经存在，可以忽略
                    $this->logger->logWarning("Version conflict for kline, data may already exist", [
                        'index' => $index,
                        'error' => $operation['error']
                    ]);
                    continue;
                }
                
                // 其他类型的错误需要重试
                if (isset($processedKlines[$index])) {
                    $failedKlines[] = $processedKlines[$index];
                    $this->logger->logError("ES operation failed for kline", [
                        'index' => $index,
                        'error' => $operation['error'],
                        'kline' => $processedKlines[$index]
                    ]);
                }
            }
        }

        return $failedKlines;
    }

    /**
     * 将失败的K线数据放入异步队列重试
     */
    private function enqueueFailedKlines(array $failedKlines): void
    {
        if (empty($failedKlines)) {
            return;
        }

        try {
            // 使用KlineBulkRetryJob进行重试
            pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value, new KlineBulkRetryJob($failedKlines));
            
            $this->logger->logProcessStatus("Enqueued " . count($failedKlines) . " failed inner klines for retry");
            
        } catch (\Throwable $e) {
            $this->logger->logError("Failed to enqueue failed klines for retry", $e);
        }
    }

    /**
     * 缓存数据到Redis
     */
    private function cacheToRedis(): void
    {
        try {
            $tickerCount = 0;
            $priceCount = 0;
            $klineCount = 0;
            
            // 统计要缓存的数据
            foreach ($this->tickerData as $currencyId => $marketTypes) {
                $tickerCount += count($marketTypes);
            }
            foreach ($this->latestPrices as $currencyId => $marketTypes) {
                $priceCount += count($marketTypes);
            }
            foreach ($this->currentKlines as $currencyId => $marketTypes) {
                foreach ($marketTypes as $marketType => $periods) {
                    foreach ($periods as $period => $kline) {
                        if ($kline['is_dirty']) {
                            $klineCount++;
                        }
                    }
                }
            }
            
            if ($klineCount > 0) {
                $processId = getmypid();
                $this->logger->logProcessStatus("K-line cache data written to Redis [PID: {$processId}]: {$klineCount} klines");
            }
            
            $pipe = $this->redis->pipeline();

            // 缓存Ticker数据
            foreach ($this->tickerData as $currencyId => $marketTypes) {
                foreach ($marketTypes as $marketType => $ticker) {
                    $key = TickerSyncKey::getInnerAggregatorTickerKey($currencyId, $marketType);
                    $pipe->hMSet($key, $ticker);
                    
                    // 计算到下一个8点的过期时间
                    $nextResetTimestamp = $this->getNext8amTimestamp($ticker['timestamp']);
                    $ttl = max(1, intval(($nextResetTimestamp - time())));
                    $pipe->expire($key, $ttl);
                }
            }

            // 缓存最新价格数据
            foreach ($this->latestPrices as $currencyId => $marketTypes) {
                foreach ($marketTypes as $marketType => $priceData) {
                    $key = TickerSyncKey::getInnerAggregatorTradeKey($currencyId, $marketType);
                    $pipe->hMSet($key, $priceData);
                    $pipe->expire($key, 3600); // 1小时过期
                }
            }

            // 缓存当前正在聚合的K线数据（最后一根K线）
//            foreach ($this->currentKlines as $currencyId => $marketTypes) {
//                foreach ($marketTypes as $marketType => $periods) {
//                    foreach ($periods as $period => $kline) {
//                        if ($kline['is_dirty']) {
//                            // 获取交易对符号
//                            $symbol = $this->getSymbolByCurrencyId($currencyId);
//                            if (!$symbol) {
//                                continue;
//                            }
//
//                            // 使用枚举类定义的键命名格式
//                            $key = KlineIndex::getInnerCacheKey($symbol, $marketType, $period);
//                            $cacheData = [
//                                'period' => $kline['period'],
//                                'open_time' => $kline['open_time'],
//                                'close_time' => $kline['close_time'],
//                                'ohlcv' => json_encode($kline['ohlcv']),
//                                'trades_count' => $kline['trades_count']
//                            ];
//                            $pipe->hMSet($key, $cacheData);
//                            $pipe->expire($key, 3600); // 1小时过期
//                        }
//                    }
//                }
//            }

            $pipe->exec();
            
            // 重置dirty标记
//            foreach ($this->currentKlines as $currencyId => $marketTypes) {
//                foreach ($marketTypes as $marketType => $periods) {
//                    foreach ($periods as $period => $kline) {
//                        if ($kline['is_dirty']) {
//                            $this->currentKlines[$currencyId][$marketType][$period]['is_dirty'] = false;
//                        }
//                    }
//                }
//            }
            
        } catch (\Throwable $e) {
            $this->logger->logError("Failed to cache inner data to Redis", $e);
        }
    }

    /**
     * 从Redis恢复状态
     */
    private function restoreStateFromRedis(): void
    {
        // 实现状态恢复逻辑（可选）
        // 这里可以从Redis恢复Ticker和K线数据
    }

    /**
     * 根据币种ID获取交易对符号
     */
    private function getSymbolByCurrencyId(int $currencyId): ?string
    {
        foreach ($this->currencies as $currencyKey => $currencyData) {
            if ($currencyData['id'] === $currencyId) {
                return $currencyData['symbol'];
            }
        }
        return null;
    }

    /**
     * 获取K线数据
     */
    public function getKlineData(int $currencyId, int $marketType, string $period): ?array
    {
        return $this->currentKlines[$currencyId][$marketType][$period] ?? null;
    }

    /**
     * 获取Ticker数据
     */
    public function getTickerData(int $currencyId, int $marketType): ?array
    {
        return $this->tickerData[$currencyId][$marketType] ?? null;
    }

    /**
     * 获取最新价格数据
     */
    public function getLatestPrice(int $currencyId, int $marketType): ?array
    {
        return $this->latestPrices[$currencyId][$marketType] ?? null;
    }

    /**
     * 停止服务
     */
    public function stop(): void
    {
        // 清理定时器
        if ($this->periodicTimerId > 0) {
            \Swoole\Timer::clear($this->periodicTimerId);
            $this->periodicTimerId = 0;
        }

        // 强制持久化所有缓冲区中的数据
        if (!empty($this->persistenceBuffer)) {
            foreach ($this->persistenceBuffer as $periodKey => $klines) {
                if (!empty($klines)) {
                    $this->bulkPersistToES($klines);
                    $this->logger->logProcessStatus("Force persisted {$periodKey} klines: " . count($klines) . " currencies");
                }
            }
            $this->persistenceBuffer = [];
        }

        // 最后缓存一次
        $this->cacheToRedis();

        $this->logger->logProcessStatus("Inner data aggregator stopped");
    }

    /**
     * 获取指定时间戳对应的当天早上8点的时间戳（秒）
     */
    private function get8amTimestamp(int $timestampMs): int
    {
        $timestampSec = intval($timestampMs / 1000);
        $date = date('Y-m-d', $timestampSec);
        return strtotime($date . ' 08:00:00');
    }

    /**
     * 获取指定时间戳之后的下一个早上8点时间戳（秒）
     */
    private function getNext8amTimestamp(int $timestampMs): int
    {
        $timestampSec = intval($timestampMs / 1000);
        $current8am = $this->get8amTimestamp($timestampMs);
        
        if ($timestampSec >= $current8am) {
            // 当前时间已经过了今天的8点，返回明天8点
            return $current8am + 86400; // 24小时后
        } else {
            // 当前时间还没到今天8点，返回今天8点
            return $current8am;
        }
    }
} 