<?php

declare(strict_types=1);

/**
 * ContractEventHandler.php
 * 合约事件处理器类
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/1/15
 * Website:algoquant.org
 */

namespace App\MarketData\Service\MatchEngine\Contract;

use App\Enum\AsyncExecutorKey;
use App\Enum\MarketData\TradeSubscribeKey;
use App\Enum\MarketType;
use App\Enum\MatchEngine\MatchEngineCallbackEvent;
use App\Job\AsyncFunExecutorJob;
use App\Job\MatchOrder\MatchOrderCancel;
use App\Job\MatchOrder\MatchOrderConfirm;
use App\Job\MatchOrder\MatchOrderFilled;
use App\Job\MatchOrder\MatchOrderModify;
use App\Job\MatchOrder\MatchOrderModifyFail;
use App\Job\MatchOrder\MatchOrderPartialFilled;
use App\Job\MatchOrder\MatchOrderTrade;
use App\MarketData\Process\MatchEngine\Logger\MatchEngineLogger;
use App\MarketData\Service\MatchEngine\Common\AbstractEventHandler;
use App\Service\RedisFactory\MarketRedis;
use Hyperf\Context\ApplicationContext;
use Hyperf\Di\Annotation\Inject;
use Swoole\CryptoExchange;

class ContractEventHandler extends AbstractEventHandler
{
    #[Inject]
    private MarketRedis $redis;

    private array $currency = [];

    public function __construct(MatchEngineLogger $logger)
    {
        $this->logger = $logger;
    }

    public function setCurrencyIdMap(array $currency): void
    {
        $this->currency = $currency;
    }

    /**
     * 注册事件监听器
     */
    public function registerEventListeners(CryptoExchange $exchange): void
    {
        try {
            // 成交事件
            $exchange->on(MatchEngineCallbackEvent::ON_TRADE->value, function($event) {
                $this->handleTradeEvent($event);
            });

            // 价格更新事件
            $exchange->on(MatchEngineCallbackEvent::ON_PRICE->value, function($event) {
                $this->handlePriceEvent($event);
            });

            // 订单放置事件
            $exchange->on(MatchEngineCallbackEvent::ON_ORDER_PLACED->value, function($event) {
                $this->handleOrderPlacedEvent($event);
            });

            // 订单完全成交事件
            $exchange->on(MatchEngineCallbackEvent::ON_ORDER_FILLED->value, function($event) {
                $this->handleOrderFilledEvent($event);
            });

            // 订单部分成交事件
            $exchange->on(MatchEngineCallbackEvent::ON_ORDER_PARTIAL_FILLED->value, function($event) {
                $this->handleOrderPartialFilledEvent($event);
            });

            // 订单取消事件
            $exchange->on(MatchEngineCallbackEvent::ON_ORDER_CANCELED->value, function($event) {
                $this->handleOrderCanceledEvent($event);
            });

            // 深度更新事件
            $exchange->on(MatchEngineCallbackEvent::ON_DEPTH_UPDATE->value, function($event) {
                $this->handleDepthUpdatedEvent($event);
            });

            $exchange->on(MatchEngineCallbackEvent::ON_ORDER_MODIFIED->value, function($event) {
                $this->handleOrderModifiedEvent($event);
            });

            $exchange->on(MatchEngineCallbackEvent::ON_ORDER_PARTIAL_FILLED->value,function ($event){
                $this->handleOrderModifiedFailEvent($event);
            });

            $this->logInfo("Contract event listeners registered successfully");

        } catch (\Throwable $e) {
            $this->logError("Failed to register contract event listeners", $e);
        }
    }


    public function handleOrderModifiedFailEvent(array $event): void
    {
        try {
            echo "撮合引擎订单修改失败: ".json_encode($event)."\n";
            //todo 根据撮合引擎的markettype 撮决定触发哪些事件
            $event['market_type'] = MarketType::MARGIN->value;
            pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value,new MatchOrderModifyFail($event));
        }catch (\Throwable $t){
            $this->logError("订单引擎修改订单失败",$t);
        }
    }

    /**
     * 处理订单修改事件
     */
    public function handleOrderModifiedEvent(array $event): void
    {
        try {
            // 使用专用的订单日志
            //$this->logInfo("撮合引擎事件[订单修改]: {$event['symbol']} - 订单id: {$event['order_id']}, 用户: {$event['user_id']}, 价格: {$event['price']}, 数量: {$event['remaining_quantity']}");
            $new_order = $event['new_order'];
            echo "撮合引擎事件[订单修改]: {$event['symbol']} - 订单id: {$new_order['order_id']}, 用户: {$new_order['user_id']}, 价格: {$new_order['price']}, 数量: {$new_order['quantity']} \n";
            pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value,new MatchOrderModify($new_order));
        } catch (\Throwable $e) {
            $this->logError("Error handling order modified event", $e);
        }
    }

    /**
     * 处理成交事件
     */
    public function handleTradeEvent(array $event): void
    {
        try {
            // 使用专用的交易日志
            echo "合约引擎[成交事件]: {$event['symbol']} - 价格: {$event['price']}, 数量: {$event['quantity']}, 成交id: {$event['trade_id']}\n";
            // 处理内部数据聚合（异步）
            go(function() use ($event) {
                //成交事件异步处理
                $event['currency_id'] = $this->currency[strtoupper($event['symbol'])]['id'] ?? 0;
                $event['market_type'] = MarketType::MARGIN->value;
                pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value,new MatchOrderTrade($event));

                try {
                    $klineTradeData = [
                        'currency_id' => $this->currency[strtoupper($event['symbol'])]['id'] ?? 0,
                        'market_type' => MarketType::MARGIN->value, // 合约市场
                        'price' => $event['price'],
                        'quantity' => $event['quantity'],
                        'trade_time' => $event['timestamp'],
                        'out_trade' => 0
                    ];
                    $this->redis->publish(TradeSubscribeKey::getCryptoTradeChannelKey(MarketType::MARGIN->value),json_encode($klineTradeData));
                }catch (\RedisException){
                    //重新建立redis 链接
                    $this->redis = ApplicationContext::getContainer()->get(MarketRedis::class);
                }catch (\Exception $e){
                    $this->logError("合约引擎内部成交事件发布redis 失败:{$e->getMessage()}");
                }
            });
            
        } catch (\Throwable $e) {
            $this->logError("Error handling contract trade event", $e);
        }
    }

    /**
     * 处理价格更新事件
     */
    public function handlePriceEvent(array $event): void
    {
        return;
        try {
            echo "合约撮合引擎事件[价格变化]: {$event['symbol']} - 价格: {$event['price']} ".json_encode($event)."\n";
        } catch (\Throwable $e) {
            $this->logError("Error handling contract price event", $e);
        }
    }

    /**
     * 处理订单放置事件
     */
    public function handleOrderPlacedEvent(array $event): void
    {
        try {
            echo "合约撮合引擎事件[订单确认]: {$event['symbol']} - {$event['side']} - 订单id: {$event['order_id']}, 用户: {$event['user_id']}, 价格: {$event['price']}, 数量: {$event['remaining_quantity']} \n";
            pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value,new MatchOrderConfirm($event));
        } catch (\Throwable $e) {
            $this->logError("Error handling contract order placed event", $e);
        }
    }

    /**
     * 处理订单完全成交事件
     */
    public function handleOrderFilledEvent(array $event): void
    {
        try {
            echo "合约撮合引擎事件[订单完全成交]: {$event['symbol']} - 订单id: {$event['order_id']}, 用户id: {$event['user_id']}, 成交数量: {$event['filled_quantity']}, 平均价格: {$event['avg_price']} ". json_encode($event)."\n";
            pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value,new MatchOrderFilled($event),1);
        } catch (\Throwable $e) {
            $this->logError("Error handling contract order filled event", $e);
        }
    }

    /**
     * 处理订单部分成交事件
     */
    public function handleOrderPartialFilledEvent(array $event): void
    {
        try {
            echo "合约撮合引擎事件[部分成交]: {$event['symbol']} - 订单id: {$event['order_id']}, 用户: {$event['user_id']}, 成交: {$event['filled_quantity']}, 剩余: {$event['remaining_quantity']} ".json_encode($event)."\n";
            pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value,new MatchOrderPartialFilled($event),1);
        } catch (\Throwable $e) {
            $this->logError("Error handling contract order partial filled event", $e);
        }
    }

    /**
     * 处理订单取消事件
     */
    public function handleOrderCanceledEvent(array $event): void
    {
        try {
            $this->logInfo("合约撮合引擎事件[订单取消]: {$event['symbol']} - OrderID: {$event['order_id']}, User: {$event['user_id']}, Reason: {$event['reason']}, Remaining: {$event['remaining_quantity']}");
            echo "合约撮合引擎事件[订单取消]: {$event['symbol']} - OrderID: {$event['order_id']}, User: {$event['user_id']}, Reason: {$event['reason']}, Remaining: {$event['remaining_quantity']}\n";
            pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value,new MatchOrderCancel($event),1);
        } catch (\Throwable $e) {
            $this->logError("Error handling contract order canceled event", $e);
        }
    }

    /**
     * 处理深度更新事件
     */
    public function handleDepthUpdatedEvent(array $event): void
    {
        try {
            // 异步保存深度数据到Redis
            go(function() use ($event) {
                pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value, new AsyncFunExecutorJob(
                    'App\MarketData\Service\MatchEngine\DepthSaveService',
                    'asyncSaveRedis',
                    [$event['symbol'], MarketType::MARGIN->value, $event]
                ));
            });
        } catch (\Throwable $e) {
            $this->logError("Error handling contract depth updated event", $e);
        }
    }
} 