<?php

declare(strict_types=1);

/**
 * ContractMatchEngineService.php
 * 合约撮合引擎服务类
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/1/15
 * Website:algoquant.org
 */

namespace App\MarketData\Service\MatchEngine\Contract;

use App\Enum\MarketType;
use App\MarketData\Process\MatchEngine\Logger\MatchEngineLogger;
use App\MarketData\Service\MatchEngine\Common\AbstractMatchEngineService;
use App\MarketData\Service\MatchEngine\DepthSaveService;
use App\Model\Currency\Currency;
use App\Model\Match\MatchTrade;
use Hyperf\Di\Annotation\Inject;
use Psr\Container\ContainerInterface;
use Swoole\CryptoExchange;

class ContractMatchEngineService extends AbstractMatchEngineService
{
    /**
     * 成交ID起始值
     */
    private int $tradeIdStart = 2000000;

    /**
     * 进程索引
     */
    private int $processIndex = 0;

    #[Inject]
    private ContainerInterface $container;

    #[Inject]
    private DepthSaveService $depthSaveService;

    public function __construct(MatchEngineLogger $logger)
    {
        $this->logger = $logger;
    }

    /**
     * 设置进程索引
     */
    public function setProcessIndex(int $processIndex): void
    {
        $this->processIndex = $processIndex;
    }

    /**
     * 初始化撮合引擎
     */
    public function initialize(): bool
    {
        try {
            $this->exchange = new CryptoExchange();
            $this->logInfo("Contract match engine service initialized successfully");
            return true;
        } catch (\Throwable $e) {
            $this->logError("Failed to initialize contract match engine service", $e);
            return false;
        }
    }

    /**
     * 加载合约币种数据
     */
    public function loadContractCurrenciesData()
    {
        try {
            $currencies = Currency::query()
                ->where('status', 1)
                ->where('market_type', MarketType::CRYPTO->value)
                ->where('is_marginTrade', 1)
                ->get(['id', 'symbol', 'm_price_precision', 'm_quantity_precision']);

            $this->currencies = [];
            foreach ($currencies as $currency) {
                $symbol = strtoupper($currency->getSymbol());
                $this->currencies[$symbol] = [
                    'id' => $currency->getId(),
                    'symbol' => $symbol,
                    'm_price_precision' => $currency->getMPricePrecision(),
                    'm_quantity_precision' => $currency->getMQuantityPrecision(),
                    'currency_obj' => $currency
                ];
            }
            return $this->currencies;
        } catch (\Throwable $e) {
            $this->logError("Failed to load contract currencies", $e);
            $this->currencies = [];
        }
    }

    /**
     * 创建市场
     */
    public function createMarkets(array $assignedCurrencies): void
    {
        try {
            $this->marketEngines = [];

            foreach ($assignedCurrencies as $symbol => $currencyData) {
                $this->createMarketFromData($symbol, $currencyData);
                $this->initDepth($symbol, $currencyData['id']);
            }

            $this->logInfo("Process {$this->processIndex} created " . count($assignedCurrencies) . " contract markets: " . implode(',', array_keys($assignedCurrencies)));

        } catch (\Throwable $e) {
            $this->logError("Failed to create contract markets", $e);
            throw $e;
        }
    }

    /**
     * 从币种数据创建市场
     */
    private function createMarketFromData(string $symbol, array $currencyData): void
    {
        try {
            $latestTradeId = MatchTrade::query()->where('currency_id',$currencyData['id'])->where('market_type',MarketType::MARGIN->value)->orderByDesc('id')->value('trade_id');
            if($latestTradeId <= 0){
                $tradeIdStart = $this->tradeIdStart + ($this->processIndex * 100000);
            }else{
                $tradeIdStart = ++$latestTradeId;
            }
            $config = [
                'trade_id_start' => $tradeIdStart,
                'price_precision' => $currencyData['m_price_precision'],
                'quantity_precision' => $currencyData['m_quantity_precision']
            ];

            $result = $this->exchange->createMarket($symbol, 'contract', $config);

            if ($result) {
                $this->marketEngines[$symbol] = $this->exchange;
            } else {
                $this->logError("Failed to create contract market for {$symbol}");
            }

        } catch (\Throwable $e) {
            $this->logError("Error creating contract market for {$symbol}", $e);
        }
    }

    /**
     * 初始化深度数据
     */
    private function initDepth(string $symbol, int $currency_id = 0): void
    {
        try {
            if ($currency_id <= 0) {
                return;
            }
            $depth = $this->depthSaveService->getDepthFromDatabase($currency_id, MarketType::MARGIN->value);

            $this->exchange->initDepth($symbol, $depth['bids'] ?? [], $depth['asks'] ?? []);

            $this->logInfo("{$symbol} 合约深度数据初始化完成 asks:" . count($asks['orders'] ?? []) . " bids:" . count($bids['orders'] ?? []));
        } catch (\Throwable $e) {
            $this->logError("{$symbol} 合约深度数据初始化异常：{$e->getMessage()}", $e);
        }
    }

    /**
     * 添加订单
     */
    public function addOrder(string $symbol, array $orderData): int
    {
        $symbol = strtoupper($symbol);

        if (!isset($this->marketEngines[$symbol])) {
            $this->logWarning("Contract market not found for symbol: {$symbol}. Available markets: " . implode(', ', array_keys($this->marketEngines)));
            return 0;
        }

        try {
            // 构建订单数据 - 根据撮合引擎文档规范
            $order = [
                'user_id' => (string)($orderData['user_id'] ?? '0'),
                'type' => $orderData['type'] ?? 'limit',
                'side' => $orderData['side'] ?? 'buy',
                'time_in_force' => $orderData['time_in_force'] ?? 'gtc',
                'quantity' => (float)($orderData['quantity'] ?? 0),
                'leverage' => (float)($orderData['leverage'] ?? 1.0)
            ];

            if (!isset($orderData['order_id'])) {
                $order['order_id'] = intval(microtime(true) * 1000) + rand(0, intval(time() / 1000));
            } else {
                $order['order_id'] = $orderData['order_id'];
            }

            // 限价单需要价格，市价单可省略
            if ($order['type'] === 'limit') {
                $order['price'] = (float)($orderData['price'] ?? 0);
            }

            // 添加到撮合引擎
            $orderId = $this->exchange->addOrder($symbol, $order);

            return $orderId;

        } catch (\Throwable $e) {
            $this->logError("解析合约订单命令失败 {$symbol}", $e);
            return 0;
        }
    }

    /**
     * 取消订单
     */
    public function cancelOrder(string $symbol, int $orderId): bool
    {
        $symbol = strtoupper($symbol);

        if (!isset($this->marketEngines[$symbol]) || $orderId <= 0) {
            return false;
        }

        try {
            $result = $this->exchange->cancelOrder($symbol, $orderId);
            return $result;
        } catch (\Throwable $e) {
            $this->logError("Failed to cancel contract order {$orderId} for {$symbol}", $e);
            return false;
        }
    }

    /**
     * 修改撮合引擎的订单
     * @param $symbol
     * @param $order_id
     * @param $price
     * @param $quantity
     * @param $time_in_force
     * @return false|void
     */
    public function modifyOrder($symbol,$order_id,$price,$quantity,$time_in_force)
    {
        if(!isset($this->marketEngines[strtoupper($symbol)]) || $order_id < 0){
            return false;
        }
        try {
            $this->exchange->modifyOrder($symbol,$order_id,$price,$quantity,$time_in_force);
        }catch (\Throwable $t){
            echo "修改订单撮合引擎执行异常：{$t->getMessage()}";
        }
    }

    /**
     * 获取深度数据
     */
    public function getDepth(string $symbol, int $levels = 20): array
    {
        $symbol = strtoupper($symbol);

        if (!isset($this->marketEngines[$symbol])) {
            return [];
        }

        try {
            return $this->exchange->getDepth($symbol, $levels);
        } catch (\Throwable $e) {
            $this->logError("Failed to get contract depth for {$symbol}", $e);
            return [];
        }
    }

    /**
     * 获取所有币种数据
     */
    public function getAllCurrencies(): array
    {
        return $this->currencies;
    }

    /**
     * 清理资源（重写父类方法）
     */
    public function cleanup(): void
    {
        // 调用父类清理方法
        parent::cleanup();
    }
} 