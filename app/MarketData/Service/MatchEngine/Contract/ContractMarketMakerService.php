<?php

declare(strict_types=1);

/**
 * ContractMarketMakerService.php
 * 合约做市机器人管理服务
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/1/16
 * Website:algoquant.org
 */

namespace App\MarketData\Service\MatchEngine\Contract;

use App\MarketData\Service\MarketMaker\Contract\StrategyInterface;
use App\MarketData\Service\MarketMaker\Runner\StrategyChainRunner;
use Psr\Log\LoggerInterface;
use Swoole\Timer;

class ContractMarketMakerService
{
    /**
     * @var StrategyChainRunner|null
     */
    private ?StrategyChainRunner $strategyRunner = null;

    /**
     * 策略管理列表 [strategy_id => strategy_info]
     */
    private array $managedStrategies = [];

    /**
     * 事件循环控制
     */
    private bool $isRunning = false;

    /**
     * 事件循环定时器ID
     */
    private int $eventLoopTimerId = 0;

    /**
     * 执行间隔（秒）
     */
    private int $executionInterval = 1;

    protected LoggerInterface $logger;

    public function __construct()
    {
        $this->logger = logger('做市服务','match-engine/match-engine-maker.log');
    }

    /**
     * 初始化服务
     * @param array $strategiesConfig 格式: ['StrategyClassName' => ['config' => 'value']]
     */
    public function initialize(array $strategiesConfig = []): void
    {
        try {
            // 如果有策略配置，初始化策略执行器和策略
            if (!empty($strategiesConfig)) {
                $this->initializeStrategies($strategiesConfig);
            }
            
            $this->logger->info("Contract market maker service initialized successfully");
            
        } catch (\Throwable $e) {
            $this->logger->error("Failed to initialize contract market maker service");
            throw $e;
        }
    }

    /**
     * 添加策略
     * @param string $strategyClassName 策略类名
     * @param array $config 策略配置
     */
    public function addStrategy(string $strategyClassName, array $config = []): void
    {
        try {
            // 检查策略是否已存在
            if (isset($this->managedStrategies[$strategyClassName])) {
                throw new \InvalidArgumentException("Strategy '{$strategyClassName}' already exists");
            }

            // 1. 如果是第一个策略，初始化策略执行器
            if ($this->strategyRunner === null) {
                $this->strategyRunner = new StrategyChainRunner();
                $this->logger->info("Contract strategy runner initialized");
            }

            // 2. 创建策略实例
            $strategy = $this->createStrategyInstance($strategyClassName, $config);

            // 3. 添加到管理列表
            $this->managedStrategies[$strategyClassName] = [
                'strategy' => $strategy,
                'status' => 'active',
                'created_at' => time(),
                'last_execution' => 0,
                'execution_count' => 0,
                'error_count' => 0,
                'config' => $config
            ];

            // 4. 同步添加到 StrategyChainRunner
            $this->strategyRunner->addStrategy($strategy);

            $this->logger->info("Contract strategy '{$strategyClassName}' added successfully");

        } catch (\Throwable $e) {
            $this->logger->error("Failed to add contract strategy '{$strategyClassName}'");
            throw $e;
        }
    }

    /**
     * 移除策略
     * @param string $strategyClassName 策略类名
     */
    public function removeStrategy(string $strategyClassName): void
    {
        try {
            if (!isset($this->managedStrategies[$strategyClassName])) {
                throw new \InvalidArgumentException("Contract strategy '{$strategyClassName}' not found");
            }

            $strategyInfo = $this->managedStrategies[$strategyClassName];
            $strategy = $strategyInfo['strategy'];

            // 1. 从 StrategyChainRunner 移除
            if ($this->strategyRunner !== null) {
                $this->strategyRunner->removeStrategy($strategy);
            }

            // 2. 从管理列表移除
            unset($this->managedStrategies[$strategyClassName]);

            // 3. 如果没有策略了，清理策略执行器
            if (empty($this->managedStrategies) && $this->strategyRunner !== null) {
                $this->strategyRunner->stopAll();
                $this->strategyRunner = null;
                $this->logger->info("Contract strategy runner cleared (no strategies remaining)");
            }

            $this->logger->info("Contract strategy '{$strategyClassName}' removed successfully");

        } catch (\Throwable $e) {
            $this->logger->error("Failed to remove contract strategy '{$strategyClassName}'");
            throw $e;
        }
    }

    /**
     * 暂停策略
     * @param string $strategyClassName 策略类名
     */
    public function pauseStrategy(string $strategyClassName): void
    {
        try {
            if (!isset($this->managedStrategies[$strategyClassName])) {
                throw new \InvalidArgumentException("Contract strategy '{$strategyClassName}' not found");
            }

            $strategyInfo = &$this->managedStrategies[$strategyClassName];
            $strategy = $strategyInfo['strategy'];

            // 1. 在 StrategyChainRunner 中暂停
            if ($this->strategyRunner !== null) {
                $this->strategyRunner->pauseStrategy($strategy);
            }

            // 2. 更新管理状态
            $strategyInfo['status'] = 'paused';

            $this->logger->info("Contract strategy '{$strategyClassName}' paused successfully");

        } catch (\Throwable $e) {
            $this->logger->error("Failed to pause contract strategy '{$strategyClassName}'", $e);
            throw $e;
        }
    }

    /**
     * 恢复策略
     * @param string $strategyClassName 策略类名
     */
    public function resumeStrategy(string $strategyClassName): void
    {
        try {
            if (!isset($this->managedStrategies[$strategyClassName])) {
                throw new \InvalidArgumentException("Contract strategy '{$strategyClassName}' not found");
            }

            $strategyInfo = &$this->managedStrategies[$strategyClassName];
            $strategy = $strategyInfo['strategy'];

            // 1. 在 StrategyChainRunner 中恢复
            if ($this->strategyRunner !== null) {
                $this->strategyRunner->resumeStrategy($strategy);
            }

            // 2. 更新管理状态
            $strategyInfo['status'] = 'active';

            $this->logger->info("Contract strategy '{$strategyClassName}' resumed successfully");

        } catch (\Throwable $e) {
            $this->logger->error("Failed to resume contract strategy '{$strategyClassName}'");
            throw $e;
        }
    }

    /**
     * 启动事件循环
     */
    public function startEventLoop(): void
    {
        if ($this->isRunning) {
            $this->logger->info("Contract market maker event loop is already running");
            return;
        }

        $this->isRunning = true;

        // 启动定时器，每秒执行一次
        $this->eventLoopTimerId = Timer::tick($this->executionInterval * 1000, function() {
            try {
                $this->processStrategies();
            } catch (\Throwable $e) {
                $this->logger->error("Error in contract market maker event loop");
            }
        });

        $this->logger->info("Contract market maker event loop started (interval: {$this->executionInterval}s)");
    }

    /**
     * 停止事件循环
     */
    public function stopEventLoop(): void
    {
        if (!$this->isRunning) {
            return;
        }

        $this->isRunning = false;

        // 清理定时器
        if ($this->eventLoopTimerId > 0) {
            Timer::clear($this->eventLoopTimerId);
            $this->eventLoopTimerId = 0;
        }

        // 停止所有策略
        if ($this->strategyRunner !== null) {
            $this->strategyRunner->stopAll();
        }

        $this->logger->info("Contract market maker event loop stopped");
    }

    /**
     * 处理策略执行
     */
    private function processStrategies(): void
    {
        if (empty($this->managedStrategies) || $this->strategyRunner === null) {
            return;
        }

        try {
            // 更新执行统计
            $this->updateExecutionStats();

            // 让 StrategyChainRunner 执行其管理的所有策略（串行执行）
            $this->strategyRunner->execute();

        } catch (\Throwable $e) {
            $this->logger->error("Error processing contract strategies");
            $this->incrementErrorCounts();
        }
    }

    /**
     * 更新执行统计
     */
    private function updateExecutionStats(): void
    {
        $currentTime = time();
        
        foreach ($this->managedStrategies as $strategyId => &$strategyInfo) {
            if ($strategyInfo['status'] === 'active') {
                $strategyInfo['last_execution'] = $currentTime;
                $strategyInfo['execution_count']++;
            }
        }
    }

    /**
     * 增加错误计数
     */
    private function incrementErrorCounts(): void
    {
        foreach ($this->managedStrategies as $strategyId => &$strategyInfo) {
            if ($strategyInfo['status'] === 'active') {
                $strategyInfo['error_count']++;
            }
        }
    }

    /**
     * 获取所有策略状态
     */
    public function getStrategiesStatus(): array
    {
        $status = [];
        
        foreach ($this->managedStrategies as $strategyId => $strategyInfo) {
            $status[$strategyId] = [
                'status' => $strategyInfo['status'],
                'created_at' => $strategyInfo['created_at'],
                'last_execution' => $strategyInfo['last_execution'],
                'execution_count' => $strategyInfo['execution_count'],
                'error_count' => $strategyInfo['error_count'],
                'strategy_class' => get_class($strategyInfo['strategy'])
            ];
        }
        
        return $status;
    }

    /**
     * 获取单个策略信息
     */
    public function getStrategyInfo(string $strategyId): ?array
    {
        if (!isset($this->managedStrategies[$strategyId])) {
            return null;
        }

        $strategyInfo = $this->managedStrategies[$strategyId];
        
        return [
            'strategy_id' => $strategyId,
            'status' => $strategyInfo['status'],
            'created_at' => $strategyInfo['created_at'],
            'last_execution' => $strategyInfo['last_execution'],
            'execution_count' => $strategyInfo['execution_count'],
            'error_count' => $strategyInfo['error_count'],
            'strategy_class' => get_class($strategyInfo['strategy']),
            'strategy_status' => $strategyInfo['strategy']->getStatus() ?? []
        ];
    }

    /**
     * 获取策略执行器
     */
    public function getStrategyRunner(): ?StrategyChainRunner
    {
        return $this->strategyRunner;
    }

    /**
     * 检查是否正在运行
     */
    public function isRunning(): bool
    {
        return $this->isRunning;
    }

    /**
     * 获取策略数量
     */
    public function getStrategyCount(): int
    {
        return count($this->managedStrategies);
    }

    /**
     * 获取活跃策略数量
     */
    public function getActiveStrategyCount(): int
    {
        $count = 0;
        foreach ($this->managedStrategies as $strategyInfo) {
            if ($strategyInfo['status'] === 'active') {
                $count++;
            }
        }
        return $count;
    }

    /**
     * 设置执行间隔
     */
    public function setExecutionInterval(int $seconds): void
    {
        $this->executionInterval = max(1, $seconds);
        
        // 如果正在运行，重启定时器
        if ($this->isRunning) {
            $this->stopEventLoop();
            $this->startEventLoop();
        }
    }

    /**
     * 初始化策略
     * @param array $strategiesConfig 格式: ['StrategyClassName' => ['config' => 'value']]
     */
    private function initializeStrategies(array $strategiesConfig): void
    {
        // 初始化策略执行器
        if ($this->strategyRunner === null) {
            $this->strategyRunner = new StrategyChainRunner();
            $this->logger->info("Contract strategy runner initialized");
        }

        foreach ($strategiesConfig as $strategyClassName => $config) {
            try {
                $this->createAndAddStrategy($strategyClassName, $config);
            } catch (\Throwable $e) {
                $this->logger->error("Failed to initialize contract strategy '{$strategyClassName}'");
            }
        }
    }

    /**
     * 创建并添加策略
     */
    private function createAndAddStrategy(string $strategyClassName, array $config): void
    {
        // 检查策略是否已存在
        if (isset($this->managedStrategies[$strategyClassName])) {
            $this->logger->info("Contract strategy '{$strategyClassName}' already exists, skipping");
            return;
        }

        // 创建策略实例
        $strategy = $this->createStrategyInstance($strategyClassName, $config);
        
        // 添加到管理列表
        $this->managedStrategies[$strategyClassName] = [
            'strategy' => $strategy,
            'status' => 'active',
            'created_at' => time(),
            'last_execution' => 0,
            'execution_count' => 0,
            'error_count' => 0,
            'config' => $config
        ];

        // 添加到策略执行器
        $this->strategyRunner->addStrategy($strategy);
        
        $this->logger->info("Contract strategy '{$strategyClassName}' created and added successfully");
    }

    /**
     * 创建策略实例
     */
    private function createStrategyInstance(string $strategyClassName, array $config): StrategyInterface
    {
        // 构建完整的类名
        $fullClassName = $this->buildStrategyClassName($strategyClassName);
        
        // 检查类是否存在
        if (!class_exists($fullClassName)) {
            throw new \InvalidArgumentException("Contract strategy class '{$fullClassName}' does not exist");
        }

        // 检查是否实现了策略接口
        if (!is_subclass_of($fullClassName, StrategyInterface::class)) {
            throw new \InvalidArgumentException("Contract strategy class '{$fullClassName}' must implement StrategyInterface");
        }

        // 合并默认配置
        $finalConfig = array_merge($this->getDefaultStrategyConfig(), $config);
        
        // 实例化策略
        return new $fullClassName($finalConfig);
    }

    /**
     * 构建策略类的完整名称
     */
    private function buildStrategyClassName(string $strategyClassName): string
    {
        // 如果已经是完整类名，直接返回
        if (strpos($strategyClassName, '\\') !== false) {
            return $strategyClassName;
        }
        
        // 构建完整的命名空间
        return 'App\\MarketData\\Service\\MarketMaker\\Strategy\\' . $strategyClassName;
    }

    /**
     * 获取默认策略配置
     */
    private function getDefaultStrategyConfig(): array
    {
        return [];
    }

    /**
     * 清理资源
     */
    public function cleanup(): void
    {
        $this->stopEventLoop();
        
        // 清理所有策略
        foreach (array_keys($this->managedStrategies) as $strategyClassName) {
            try {
                $this->removeStrategy($strategyClassName);
            } catch (\Throwable $e) {
                $this->logger->error("Error cleaning up contract strategy '{$strategyClassName}'");
            }
        }
        
        $this->logger->info("Contract market maker service cleaned up");
    }
} 