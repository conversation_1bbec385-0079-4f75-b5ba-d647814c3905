<?php

declare(strict_types=1);

/**
 * SpotCommandHandler.php
 * 现货命令处理器类
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/1/15
 * Website:algoquant.org
 */

namespace App\MarketData\Service\MatchEngine\Spot;

use App\MarketData\Process\MatchEngine\Logger\MatchEngineLogger;
use App\MarketData\Service\MatchEngine\Common\AbstractCommandHandler;

class SpotCommandHandler extends AbstractCommandHandler
{
    private SpotMatchEngineService $spotMatchEngineService;

    public function __construct(MatchEngineLogger $logger, SpotMatchEngineService $spotMatchEngineService)
    {
        $this->logger = $logger;
        $this->spotMatchEngineService = $spotMatchEngineService;
        $this->matchEngineService = $spotMatchEngineService;
    }

    /**
     * 处理命令
     */
    public function handleCommand(string $type, array $data): void
    {
        switch ($type) {
            case 'add_order':
                $this->handleAddOrderCommand($data);
                break;
            case 'cancel_order':
                $this->handleCancelOrderCommand($data);
                break;
            case 'get_depth':
                $this->handleGetDepthCommand($data);
                break;
            case 'get_markets':
                $this->handleGetMarketsCommand();
                break;
            case 'process_order':
                $this->handleProcessOrderCommand($data);
                break;
            case 'modify_order':
                $this->handleModifyOrderCommand($data);
                break;
            default:
                $this->logWarning("Unknown command type: {$type}");
                break;
        }
    }

    /**
     * 处理添加订单命令
     */
    public function handleAddOrderCommand(array $data): void
    {
        if (!$this->validateCommandData($data, ['symbol', 'user_id', 'side', 'type', 'quantity'])) {
            return;
        }

        $symbol = $this->normalizeSymbol($data['symbol']);
        
        if (!$this->spotMatchEngineService->hasMarket($symbol)) {
            $this->logWarning("Market not found for symbol: {$symbol}");
            return;
        }

        $orderData = [
            'user_id' => $data['user_id'],
            'side' => $data['side'],
            'type' => $data['type'],
            'quantity' => (float)$data['quantity'],
            'time_in_force' => $data['time_in_force'] ?? 'gtc',
            'leverage' => (float)($data['leverage'] ?? 1.0)
        ];

        // 限价单需要价格
        if ($orderData['type'] === 'limit') {
            if (!isset($data['price']) || empty($data['price'])) {
                $this->logWarning("Price is required for limit order");
                return;
            }
            $orderData['price'] = (float)$data['price'];
        }

        try {
            $orderId = $this->spotMatchEngineService->addOrder($symbol, $orderData);
        } catch (\Throwable $e) {
            $this->logError("Failed to add command order for {$symbol}", $e);
        }
    }

    /**
     * 处理取消订单命令
     */
    public function handleCancelOrderCommand(array $data): void
    {
        if (!$this->validateCommandData($data, ['symbol', 'order_id'])) {
            return;
        }

        $symbol = $this->normalizeSymbol($data['symbol']);
        $orderId = (int)$data['order_id'];
        
        if (!$this->spotMatchEngineService->hasMarket($symbol)) {
            $this->logWarning("Market not found for symbol: {$symbol}");
            return;
        }

        if ($orderId <= 0) {
            $this->logWarning("Invalid order ID: {$orderId}");
            return;
        }

        try {
            $result = $this->spotMatchEngineService->cancelOrder($symbol, $orderId);
            //echo "现货撮合引擎撤单执行结果：{$result} {$symbol}-{$orderId}\n";
        } catch (\Throwable $e) {
            $this->logError("Failed to cancel order {$orderId} for {$symbol}", $e);
        }
    }

    /**
     * 处理获取深度命令
     */
    public function handleGetDepthCommand(array $data): void
    {
        if (!$this->validateCommandData($data, ['symbol'])) {
            return;
        }

        $symbol = $this->normalizeSymbol($data['symbol']);
        $levels = (int)($data['levels'] ?? 20);
        
        if (!$this->spotMatchEngineService->hasMarket($symbol)) {
            $this->logWarning("Market not found for symbol: {$symbol}");
            return;
        }

        try {
            $depth = $this->spotMatchEngineService->getDepth($symbol, $levels);
        } catch (\Throwable $e) {
            $this->logError("Failed to get depth for {$symbol}", $e);
        }
    }

    /**
     * 处理获取市场列表命令
     */
    public function handleGetMarketsCommand(): void
    {
        try {
            $markets = $this->spotMatchEngineService->getAvailableMarkets();
        } catch (\Throwable $e) {
            $this->logError("Failed to get markets list", $e);
        }
    }

    /**
     * 处理来自管理进程分发的订单命令
     */
    public function handleProcessOrderCommand(array $data): void
    {
        if (!$this->validateCommandData($data, ['symbol'])) {
            return;
        }

        $symbol = $this->normalizeSymbol($data['symbol']);
        
        if (!$this->spotMatchEngineService->hasMarket($symbol)) {
            $this->logWarning("Market not found for symbol: {$symbol}. Available markets: " . implode(', ', $this->spotMatchEngineService->getAvailableMarkets()));
            return;
        }

        try {
            $orderId = $this->spotMatchEngineService->addOrder($symbol, $data);
        } catch (\Throwable $e) {
            $this->logError("Failed to process order for {$symbol}", $e);
        }
    }

    public function handleModifyOrderCommand(array $data): void {
        if (!$this->validateCommandData($data, ['symbol', 'order_id', 'price', 'quantity'])) {
            $this->logError("修改订单命令参数验证是失败");
            return;
        }

        $symbol = $this->normalizeSymbol($data['symbol']);
        $orderId = (int)$data['order_id'];
        $price = (float)$data['price'];
        $quantity = (float)$data['quantity'];
        $timeInForce = $data['time_in_force'] ?? 'gtc';

        if (!$this->spotMatchEngineService->hasMarket($symbol)) {
            $this->logWarning("Market not found for symbol: {$symbol}");
            return;
        }

        if ($orderId <= 0) {
            $this->logWarning("Invalid order ID: {$orderId}");
            return;
        }

        if ($price <= 0 || $quantity <= 0) {
            $this->logWarning("Invalid price or quantity");
            return;
        }

        try {
            $result = $this->spotMatchEngineService->modifyOrder($symbol, $orderId, $price, $quantity,$this->coverTimeInForceInt($timeInForce));
        } catch (\Throwable $e) {
            $this->logError("Failed to modify order {$orderId} for {$symbol}", $e);
        }   
    }

    protected function coverTimeInForceInt(string $value): int
    {
        return match (strtolower($value)){
            "gtc" => 1,
            'ioc' => 2,
            'fok' => 3
        };
    }
} 