<?php

namespace App\MarketData\Process\CryptoMargin;

use App\Enum\MarketData\TradeSubscribeKey;
use App\Enum\MarketType;
use App\Enum\ProcessCmdKey;
use App\Job\AsyncFunExecutorJob;
use App\Model\Currency\Currency;
use App\Process\BaseProcess;
use App\Service\RedisFactory\MarketRedis;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\Annotation\Process;
use Psr\Container\ContainerInterface;
use Swoole\Coroutine;
use Swoole\Coroutine\Http\Client;

#[Process(name: "crypto-margin-trade")]
class TradeProcess extends BaseProcess
{
    public bool $enableCoroutine = true;

    public int $nums = 1;

    public string $cmd_key = '';

    private ?Client $wsClient = null;

    private array $symbols = [];

    private array $symbolCurrencyMap = [];

    private bool $isConnected = false;

    private array $lastMessageTime = [];

    private array $priceBuffer = [];

    private int $heartbeatTimerId = 0;

    private int $statsTimerId = 0;

    private int $batchTimerId = 0;

    private int $connectionCheckTimerId = 0;

    private int $lastReceiveTime = 0;

    private int $reconnectAttempts = 0;

    private const MAX_RECONNECT_ATTEMPTS = 5;

    private bool $isReconnecting = false;

    private array $messageQueue = [];
    private int $messageQueueTimerId = 0;

    private int $sendFailedCount = 0;

    #[Inject]
    public MarketRedis $redis;

    public function __construct(ContainerInterface $container)
    {
        $this->cmd_key = ProcessCmdKey::CRYPTO_MARGIN_TRADE_CMD->value;
        parent::__construct($container);
    }

    public function handle(): void
    {
        parent::handle();
    }

    public function runBusinessLogic(): void
    {
        try {
            $this->loadCurrencyData();
            $this->connectWebSocket();

            while (true) {
                try {
                    if (!$this->isConnected && !empty($this->symbols) && !$this->isReconnecting) {
                        $this->handleReconnect();
                    }
                    Coroutine::sleep(3);
                } catch (\Throwable $t) {
                    $this->logger->get('margin-trade')->error("Margin trade process error: " . $t->getMessage());
                    Coroutine::sleep(3);
                }
            }
        } catch (\Throwable $e) {
            $this->logger->get('margin-trade')->error("Failed to initialize margin trade process: " . $e->getMessage());
        }
    }

    public function parse_cmd(array $message): void
    {
        if (isset($message['type']) && $message['type'] === 'start') {
            $this->loadCurrencyData();
            if (!empty($this->symbols)) {
                $this->connectWebSocket();
            }
        }
    }

    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER',false);
    }

    private function loadCurrencyData(): void
    {
        try {
            $currencies = Currency::query()
                ->where('status', 1)
                ->where('is_marginTrade', 1)
                ->where('market_type', MarketType::CRYPTO->value)
                ->get(['id', 'symbol']);

            $this->symbols = [];
            $this->symbolCurrencyMap = [];
            foreach ($currencies as $currency) {
                $symbol = strtolower($currency->getSymbol());
                $this->symbols[] = $symbol;
                $this->symbolCurrencyMap[$symbol] = $currency->getId();
            }

            $this->logger->get('margin-trade')->info("Loaded " . count($this->symbols) . " margin currency symbols");

        } catch (\Throwable $e) {
            $this->logger->get('margin-trade')->error("Failed to load margin currency data: " . $e->getMessage());
        }
    }

    private function connectWebSocket(): void
    {
        if (empty($this->symbols)) {
            return;
        }

        try {
            if ($this->wsClient) {
                $this->wsClient->close();
            }

            $this->wsClient = new Client('fstream.binance.com', 443, true);

            $config = [
                'timeout' => 60,
                'keep_alive' => true,
            ];

            if (env('REQUEST_PROXY', false)) {
                $httpProxy = env('HTTP_PROXY', 'http://127.0.0.1:1087');
                $proxyUrl = parse_url($httpProxy);
                $config['http_proxy_host'] = $proxyUrl['host'] ?? '127.0.0.1';
                $config['http_proxy_port'] = $proxyUrl['port'] ?? 1087;
            }

            $this->wsClient->set($config);

            $firstSymbol = $this->symbols[0];
            $path = "/ws/{$firstSymbol}@aggTrade";

            if (!$this->wsClient->upgrade($path)) {
                throw new \Exception("WebSocket upgrade failed");
            }

            $this->isConnected = true;
            $this->isReconnecting = false;
            $this->reconnectAttempts = 0;

            $this->subscribeAllSymbols();

            if ($this->heartbeatTimerId === 0) {
                $this->startHeartbeatTimer();
            }
            if ($this->statsTimerId === 0) {
                $this->startStatsTimer();
            }
            if ($this->batchTimerId === 0) {
                $this->startBatchTimer();
            }
            if ($this->connectionCheckTimerId === 0) {
                $this->startConnectionCheckTimer();
            }
            if ($this->messageQueueTimerId === 0) {
                $this->startMessageQueueTimer();
            }

            $this->handleWebSocketMessages();

            $this->logger->get("margin-trade")->info("Margin websocket connect success");

        } catch (\Throwable $e) {
            $this->isConnected = false;
            $this->isReconnecting = false;
            $this->logger->get('margin-trade')->error("Margin WebSocket connection failed: " . $e->getMessage());
        }
    }

    private function subscribeAllSymbols(): void
    {
        if (count($this->symbols) <= 1) {
            return;
        }

        $subscribeStreams = [];
        foreach ($this->symbols as $symbol) {
            $subscribeStreams[] = "{$symbol}@aggTrade";
        }

        $subscribeMessage = [
            'method' => 'SUBSCRIBE',
            'params' => $subscribeStreams,
            'id' => time()
        ];

        $this->queueMessage(json_encode($subscribeMessage));
        $this->logger->get('margin-trade')->info("Subscribed to " . count($subscribeStreams) . " margin trade streams");
    }

    private function handleWebSocketMessages(): void
    {
        $this->lastReceiveTime = time();

        while ($this->isConnected && $this->wsClient) {
            try {
                $frame = $this->wsClient->recv(1);
                if (!$frame) {
                    try {
                        $res = $this->wsClient->push(json_encode(['type' => 'ping']));
                        if(!$res){
                            if($this->sendFailedCount > 30){
                                $this->isConnected = false;
                                break;
                            }
                            $this->sendFailedCount++;
                        }else{
                            $this->sendFailedCount = 0;
                        }
                    }catch (\Throwable){
                        $this->isConnected = false;
                        break;
                    }
                    continue;
                }

                if($frame->opcode == WEBSOCKET_OPCODE_PING || $frame->opcode == WEBSOCKET_OPCODE_PONG){
                    $pingFrame = new \Swoole\WebSocket\Frame();
                    $pingFrame->opcode = WEBSOCKET_OPCODE_PONG;
                    $pingFrame->data = 'pong';
                    $this->wsClient->push($pingFrame, WEBSOCKET_OPCODE_PONG);
                    continue;
                }

                $this->lastReceiveTime = time();

                if ($frame->opcode === \SWOOLE_WEBSOCKET_OPCODE_TEXT) {
                    $data = json_decode($frame->data, true);
                    if ($data && isset($data['e']) && $data['e'] === 'aggTrade') {
                        $this->processTradeData($data);
                    }
                }

            } catch (\Throwable $e) {
                $this->logger->get('margin-trade')->error("Margin WebSocket message handling error: " . $e->getMessage());
                $this->isConnected = false;
                break;
            }
        }

        if (!$this->isConnected) {
            $this->logger->get('margin-trade')->warning("Margin WebSocket message loop exited");
        }
    }

    private function processTradeData(array $tradeData): void
    {
        try {
            $symbol = strtolower($tradeData['s']);

            if (!isset($this->symbolCurrencyMap[$symbol])) {
                return;
            }

            $this->lastMessageTime[$symbol] = time();

            $currencyId = $this->symbolCurrencyMap[$symbol];
            $price = (float)$tradeData['p'];
            $quantity = (float)$tradeData['q'];
            $tradeTime = (int)$tradeData['T'];

            $klineTradeData = [
                'currency_id' => $currencyId,
                'market_type' => MarketType::MARGIN->value,
                'price' => $price,
                'quantity' => $quantity,
                'trade_time' => $tradeTime,
                'out_trade' => 1
            ];

            //发布成交数据到redis
            go(function ()use($klineTradeData,$symbol){
                try {
                    $this->redis->publish(TradeSubscribeKey::getCryptoTradeChannelKey(MarketType::MARGIN->value),json_encode($klineTradeData)); //全部币种成交公共频道
                    $this->redis->publish(TradeSubscribeKey::getOuterTradeKey(strtoupper($symbol),MarketType::MARGIN->value),json_encode($klineTradeData)); //外部币种成交数据频道
                }catch (\Throwable){}
            });

            $this->priceBuffer[$symbol] = [
                'currency_id' => $currencyId,
                'symbol' => $symbol,
                'price' => $price,
                'timestamp' => time(),
                'market_type' => MarketType::MARGIN->value
            ];

        } catch (\Throwable $e) {
            // 静默处理错误
        }
    }

    private function handleReconnect(): void
    {
        if ($this->isReconnecting) {
            return;
        }

        $this->isReconnecting = true;

        try {
            if ($this->reconnectAttempts >= self::MAX_RECONNECT_ATTEMPTS) {
                $this->logger->get('margin-trade')->error("Max reconnect attempts reached, waiting longer before retry");
                Coroutine::sleep(60);
                $this->reconnectAttempts = 0;
            } else {
                $this->reconnectAttempts++;
                $delay = min(30, $this->reconnectAttempts * 5);
                $this->logger->get('margin-trade')->warning("Reconnecting in {$delay} seconds (attempt {$this->reconnectAttempts})");
                Coroutine::sleep($delay);
            }

            $this->connectWebSocket();

            if ($this->isConnected) {
                $this->logger->get('margin-trade')->info("Margin WebSocket reconnected successfully");
                $this->reconnectAttempts = 0;
            }
        } catch (\Throwable $e) {
            $this->logger->get('margin-trade')->error("Margin reconnection failed: " . $e->getMessage());
            $this->isReconnecting = false;
        }
    }

    private function startHeartbeatTimer(): void
    {
        $this->heartbeatTimerId = \Swoole\Timer::tick(25000, function () {
            if ($this->wsClient && $this->isConnected && !$this->isReconnecting) {
                try {
                    $frame = new \Swoole\WebSocket\Frame();
                    $frame->opcode = WEBSOCKET_OPCODE_PONG;
                    $frame->data = '';

                    $this->queueMessage($frame);
                } catch (\Throwable $e) {

                }
            }
        });
    }

    private function startStatsTimer(): void
    {
        $this->statsTimerId = \Swoole\Timer::tick(30000, function () {
            if ($this->isConnected && !$this->isReconnecting) {
                $this->outputStats();
            }
        });
    }

    private function startBatchTimer(): void
    {
        $this->batchTimerId = \Swoole\Timer::tick(500, function () {
            if ($this->isConnected && !$this->isReconnecting) {
                try{
                    $this->processBatchPrices();
                }catch (\Throwable $e) {

                }
            }
        });
    }

    private function startConnectionCheckTimer(): void
    {
        $this->connectionCheckTimerId = \Swoole\Timer::tick(10000, function () {
            $this->checkConnectionHealth();
        });
    }

    private function checkConnectionHealth(): void
    {
        if (!$this->isConnected || !$this->wsClient || $this->isReconnecting) {
            return;
        }

        $currentTime = time();
        $timeSinceLastReceive = $currentTime - $this->lastReceiveTime;

        if ($timeSinceLastReceive > 60) {
            $this->logger->get('margin-trade')->warning("Margin: No data received for {$timeSinceLastReceive} seconds, marking connection as dead");
            $this->isConnected = false;
        } elseif ($timeSinceLastReceive > 30) {
            $this->logger->get('margin-trade')->info("Margin: No data received for {$timeSinceLastReceive} seconds");
        }
    }

    private function processBatchPrices(): void
    {
        if (empty($this->priceBuffer)) {
            return;
        }

        $batch = array_values($this->priceBuffer);
        $this->priceBuffer = [];

        go(function () use ($batch) {
            $this->pushToQueueWithRetry($batch);
        });
    }

    private function pushToQueueWithRetry(array $batch, int $maxRetries = 3): void
    {
        $retryCount = 0;

        while ($retryCount < $maxRetries) {
            try {
                $job = new AsyncFunExecutorJob(
                    'App\MarketData\Service\TradeSaveService',
                    'batchPriceSyncRedis',
                    [$batch]
                );

                pushAsyncJob("async-func-executor",$job);
                return;

            } catch (\Throwable $e) {
                $retryCount++;
                if ($retryCount >= $maxRetries) {
                    $this->logger->get('margin-trade')->error("Failed to push margin price data to queue after {$maxRetries} retries: " . $e->getMessage());
                } else {
                    Coroutine::sleep(0.5 * $retryCount);
                }
            }
        }
    }

    private function outputStats(): void
    {
        $totalSymbols = count($this->symbols);
        $receivedSymbols = count($this->lastMessageTime);
        $currentTime = time();

        $timeoutSymbols = [];
        foreach ($this->symbols as $symbol) {
            if (!isset($this->lastMessageTime[$symbol]) ||
                ($currentTime - $this->lastMessageTime[$symbol]) > 60) {
                $timeoutSymbols[] = $symbol;
            }
        }

        if (!empty($timeoutSymbols) && $this->isConnected && !$this->isReconnecting) {
            $timeoutCount = count($timeoutSymbols);
            $timeoutRatio = $totalSymbols > 0 ? ($timeoutCount / $totalSymbols) : 0;

            if ($timeoutRatio > 0.5) {
                $this->logger->get('margin-trade')->warning("Margin: Too many timeout symbols ({$timeoutCount}/{$totalSymbols}), marking connection for reconnect");
                $this->isConnected = false;
            } else {
                $this->resubscribeTimeoutSymbols($timeoutSymbols);
            }
        }
    }

    private function resubscribeTimeoutSymbols(array $timeoutSymbols): void
    {
        if (!$this->wsClient || !$this->isConnected || $this->isReconnecting) {
            return;
        }

        try {
            $unsubscribeStreams = [];
            foreach ($timeoutSymbols as $symbol) {
                $unsubscribeStreams[] = "{$symbol}@aggTrade";
            }

            if (!empty($unsubscribeStreams)) {
                $unsubscribeMessage = [
                    'method' => 'UNSUBSCRIBE',
                    'params' => $unsubscribeStreams,
                    'id' => time()
                ];
                $this->queueMessage(json_encode($unsubscribeMessage));

                $subscribeMessage = [
                    'method' => 'SUBSCRIBE',
                    'params' => $unsubscribeStreams,
                    'id' => time() + 1
                ];
                $this->queueMessage(json_encode($subscribeMessage));

                foreach ($timeoutSymbols as $symbol) {
                    unset($this->lastMessageTime[$symbol]);
                }

                //$this->logger->get('margin-trade')->info("Resubscribed " . count($timeoutSymbols) . " margin timeout symbols");
            }

        } catch (\Throwable $e) {
            $this->logger->get('margin-trade')->error("Failed to resubscribe margin timeout symbols: " . $e->getMessage());
        }
    }

    private function startMessageQueueTimer(): void
    {
        $this->messageQueueTimerId = \Swoole\Timer::tick(1000, function () {
            if ($this->isConnected && !$this->isReconnecting) {
                try {
                    $this->processMessageQueue();
                } catch (\Throwable $e) {

                }
            }
        });
    }

    private function queueMessage($message): void
    {
        $this->messageQueue[] = $message;
    }

    private function processMessageQueue(): void
    {
        if (empty($this->messageQueue) || !$this->wsClient) {
            return;
        }

        $messagesToSend = array_splice($this->messageQueue, 0, 8);

        foreach ($messagesToSend as $message) {
            try {
                if ($message instanceof \Swoole\WebSocket\Frame) {
                    $result = $this->wsClient->push($message, $message->opcode);
                } else {
                    $result = $this->wsClient->push($message);
                }

                if (!$result) {
                    $this->isConnected = false;
                    break;
                }
            } catch (\Throwable $e) {
                $this->isConnected = false;
                break;
            }
        }
    }

    private function clearTimers(): void
    {
        if ($this->heartbeatTimerId > 0) {
            \Swoole\Timer::clear($this->heartbeatTimerId);
            $this->heartbeatTimerId = 0;
        }

        if ($this->statsTimerId > 0) {
            \Swoole\Timer::clear($this->statsTimerId);
            $this->statsTimerId = 0;
        }

        if ($this->batchTimerId > 0) {
            \Swoole\Timer::clear($this->batchTimerId);
            $this->batchTimerId = 0;
        }

        if ($this->connectionCheckTimerId > 0) {
            \Swoole\Timer::clear($this->connectionCheckTimerId);
            $this->connectionCheckTimerId = 0;
        }

        if ($this->messageQueueTimerId > 0) {
            \Swoole\Timer::clear($this->messageQueueTimerId);
            $this->messageQueueTimerId = 0;
        }
    }
}
