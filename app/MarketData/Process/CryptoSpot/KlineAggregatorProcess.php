<?php

/**
 * KlineAggregatorProcess.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/29
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\MarketData\Process\CryptoSpot;

use App\Enum\MarketData\TradeSubscribeKey;
use App\Enum\MarketType;
use App\MarketData\Service\InnerDataAggregatorService;
use App\MarketData\Service\KlineAggregatorService;
use App\Process\BaseProcess;
use App\Service\RedisFactory\MarketRedis;
use Hyperf\Context\ApplicationContext;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\Annotation\Process;
use Psr\Container\ContainerInterface;
use Swoole\Coroutine;

/**
 * k线聚合服务单独启动进程
 * k线聚合自适应使用平台成交或者外部数据成交维护k线数据
 */

#[Process(name: 'spot-kline-aggregator-process')]
class KlineAggregatorProcess extends BaseProcess
{
    public string $cmd_key = '';

    public int $nums = 1;

    public bool $enableCoroutine = true;

    #[Inject]
    public MarketRedis $redis;

    public ?KlineAggregatorService $klineAggregatorService;

    public ?InnerDataAggregatorService $spotInnerDataAggregatorService;

    public function __construct(ContainerInterface $container)
    {
        parent::__construct($container);
    }

    public function handle(): void
    {
        parent::handle();
    }

    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER',false);
    }

    public function runBusinessLogic(): void
    {
        //全局k线聚合处理服务
        $this->klineAggregatorService = $this->container->get(KlineAggregatorService::class);
        $this->klineAggregatorService->initializeForMarket(MarketType::CRYPTO->value);

        //内部行情数据ticker 与trade 处理服务
        $this->spotInnerDataAggregatorService = $this->container->get(InnerDataAggregatorService::class);
        $this->spotInnerDataAggregatorService->initialize([],MarketType::CRYPTO);

        $this->subscribeCurrencyTrade();
    }

    private function subscribeCurrencyTrade(): void
    {
        while (true){
            try {
                $this->redis->subscribe([TradeSubscribeKey::getCryptoTradeChannelKey(MarketType::CRYPTO->value)], function ($redis, $channel, $message) {
                    try {
                        $tradeData = json_decode($message, true);
                        if ($tradeData === null) {
                            return;
                        }
                        $klineTradeData = [
                            'currency_id' => $tradeData['currency_id'],
                            'market_type' => MarketType::CRYPTO->value, // 现货市场
                            'price' => $tradeData['price'],
                            'quantity' => $tradeData['quantity'],
                            'trade_time' => $tradeData['trade_time'],
                            'out_trade' => $tradeData['out_trade'] ?? 1
                        ];
                        $this->klineAggregatorService->updateFromTrade($klineTradeData);
                        if(intval($klineTradeData['out_trade']) === 0){
                            $this->spotInnerDataAggregatorService->handleTradeEvent($klineTradeData);
                        }
                    } catch (\Throwable $e) {
                        $this->logger->get('kline-aggregator')->error("Error");
                    }
                });
            }catch (\RedisException){
                Coroutine::sleep(3);
                $this->redis = ApplicationContext::getContainer()->get(MarketRedis::class);
            }catch (\Throwable){
                Coroutine::sleep(3);
            }
        }
    }
}