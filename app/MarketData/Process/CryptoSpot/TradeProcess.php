<?php

namespace App\MarketData\Process\CryptoSpot;

use App\Enum\MarketData\TradeSubscribeKey;
use App\Enum\MarketType;
use App\Enum\ProcessCmdKey;
use App\Job\AsyncFunExecutorJob;
use App\Model\Currency\Currency;
use App\Process\BaseProcess;
use App\Service\RedisFactory\MarketRedis;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\Annotation\Process;
use Psr\Container\ContainerInterface;
use Swoole\Coroutine;
use Swoole\Coroutine\Http\Client;
use Swoole\WebSocket\Frame;

#[Process(name: "crypto-spot-trade")]
class TradeProcess extends BaseProcess
{
    public bool $enableCoroutine = true;

    public int $nums = 1;

    public string $cmd_key = '';

    private ?Client $wsClient = null;

    private array $symbols = [];

    private array $symbolCurrencyMap = [];

    private bool $isConnected = false;

    private array $lastMessageTime = [];

    private array $priceBuffer = [];

    private int $heartbeatTimerId = 0;

    private int $statsTimerId = 0;

    private int $batchTimerId = 0;

    private int $connectionCheckTimerId = 0;

    private int $lastReceiveTime = 0;

    private int $reconnectAttempts = 0;

    private const MAX_RECONNECT_ATTEMPTS = 5;

    private bool $isReconnecting = false;

    private int $sendFailedCount = 0;

    private array $messageQueue = [];
    private int $messageQueueTimerId = 0;

    #[Inject]
    public MarketRedis $redis;

    public function __construct(ContainerInterface $container)
    {
        $this->cmd_key = ProcessCmdKey::CRYPTO_SPOT_TRADE_CMD->value;
        parent::__construct($container);
    }

    public function handle(): void
    {
        parent::handle();
    }

    public function runBusinessLogic(): void
    {
        try {
            $this->loadCurrencyData();
            $this->connectWebSocket();

            while (true) {
                try {
                    if (!$this->isConnected && !empty($this->symbols) && !$this->isReconnecting) {
                        $this->handleReconnect();
                    }
                    Coroutine::sleep(3);
                } catch (\Throwable $t) {
                    $this->logger->get('trade')->error("Trade process error: " . $t->getMessage());
                    Coroutine::sleep(3);
                }
            }
        } catch (\Throwable $e) {
            $this->logger->get('trade')->error("Failed to initialize trade process: " . $e->getMessage());
        }
    }

    public function parse_cmd(array $message): void
    {
        if (isset($message['type']) && $message['type'] === 'start') {
            $this->loadCurrencyData();
            if (!empty($this->symbols)) {
                $this->connectWebSocket();
            }
        }
    }

    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER',false);
    }

    private function loadCurrencyData(): void
    {
        try {
            $currencies = Currency::query()
                ->where('status', 1)
                ->where('is_spotTrade', 1)
                ->where('market_type', MarketType::CRYPTO->value)
                ->get(['id', 'symbol']);

            $this->symbols = [];
            $this->symbolCurrencyMap = [];
            foreach ($currencies as $currency) {
                $symbol = strtolower($currency->getSymbol());
                $this->symbols[] = $symbol;
                $this->symbolCurrencyMap[$symbol] = $currency->getId();
            }

        } catch (\Throwable $e) {
            $this->logger->get('trade')->error("Failed to load currency data: " . $e->getMessage());
        }
    }

    private function connectWebSocket(): void
    {
        if (empty($this->symbols)) {
            return;
        }

        try {
            // 先清理现有连接，但保留定时器
            if ($this->wsClient) {
                $this->wsClient->close();
            }
            $this->sendFailedCount = 0;

            $this->wsClient = new Client('stream.binance.com', 9443, true);

            $config = [
                'timeout' => 60,
                'keep_alive' => true,
            ];

            if (env('REQUEST_PROXY', false)) {
                $httpProxy = env('HTTP_PROXY', 'http://127.0.0.1:1087');
                $proxyUrl = parse_url($httpProxy);
                $config['http_proxy_host'] = $proxyUrl['host'] ?? '127.0.0.1';
                $config['http_proxy_port'] = $proxyUrl['port'] ?? 1087;
            }

            $this->wsClient->set($config);

            $firstSymbol = $this->symbols[0];
            $path = "/ws/{$firstSymbol}@trade";

            if (!$this->wsClient->upgrade($path)) {
                throw new \Exception("WebSocket upgrade failed");
            }

            $this->isConnected = true;
            $this->isReconnecting = false;
            $this->reconnectAttempts = 0;

            $this->subscribeAllSymbols();

            // 只在首次连接时启动定时器
            if ($this->heartbeatTimerId === 0) {
                $this->startHeartbeatTimer();
            }
            if ($this->statsTimerId === 0) {
                $this->startStatsTimer();
            }
            if ($this->batchTimerId === 0) {
                $this->startBatchTimer();
            }
            if ($this->connectionCheckTimerId === 0) {
                $this->startConnectionCheckTimer();
            }
            if ($this->messageQueueTimerId === 0) {
                $this->startMessageQueueTimer();
            }

            $this->handleWebSocketMessages();

            $this->logger->get("trade")->info("websocket connect success");

        } catch (\Throwable $e) {
            $this->isConnected = false;
            $this->isReconnecting = false;
            $this->logger->get('trade')->error("WebSocket connection failed: " . $e->getMessage());
        }
    }

    private function subscribeAllSymbols(): void
    {
        if (count($this->symbols) <= 1) {
            return;
        }

        $subscribeStreams = [];
        foreach ($this->symbols as $symbol) {
            $subscribeStreams[] = "{$symbol}@trade";
        }

        $subscribeMessage = [
            'method' => 'SUBSCRIBE',
            'params' => $subscribeStreams,
            'id' => time()
        ];

        $this->queueMessage(json_encode($subscribeMessage));
    }

    private function handleWebSocketMessages(): void
    {
        $this->lastReceiveTime = time();

        while ($this->isConnected && $this->wsClient) {
            try {
                $frame = $this->wsClient->recv(1);
                if (!$frame) {
                    try {
                        $pingFrame = new Frame();
                        $pingFrame->opcode = WEBSOCKET_OPCODE_PING;
                        $pingFrame->data = '';
                        $res = $this->wsClient->push($pingFrame,WEBSOCKET_OPCODE_PING);
                        if(!$res){
                            $this->sendFailedCount++;
                        }else{
                            $this->sendFailedCount = 0;
                        }
                        if($this->sendFailedCount > 50){
                            $this->isConnected = false;
                            $this->logger->get('trade')->info("主动发送数据监测断开");
                            break;
                        }
                        Coroutine::sleep(1);
                    }catch (\Throwable $t){
                        $this->isConnected = false;
                        $this->logger->get('trade')->info("主动发送数据异常断开：{$t->getMessage()}");
                        break;
                    }
                    continue;
                }

                if($frame->opcode == WEBSOCKET_OPCODE_PING){
                    $pingFrame = new Frame();
                    $pingFrame->opcode = WEBSOCKET_OPCODE_PONG;
                    $pingFrame->data = $frame->data;
                    $this->wsClient->push($pingFrame,WEBSOCKET_OPCODE_PONG);
                    continue;
                }

                // 更新最后接收时间
                $this->lastReceiveTime = time();

                if ($frame->opcode === \SWOOLE_WEBSOCKET_OPCODE_TEXT) {
                    $data = json_decode($frame->data, true);
                    if ($data && isset($data['e']) && $data['e'] === 'trade') {
                        $this->processTradeData($data);
                    }
                }

            } catch (\Throwable $e) {
                $this->logger->get('trade')->error("WebSocket message handling error: " . $e->getMessage());
                $this->isConnected = false;
                break;
            }
        }

        // 退出循环后，记录日志
        if (!$this->isConnected) {
            $this->logger->get('trade')->warning("WebSocket message loop exited");
        }
    }

    private function processTradeData(array $tradeData): void
    {
        try {
            // 直接从成交数据中获取symbol
            $symbol = strtolower($tradeData['s']);

            if (!isset($this->symbolCurrencyMap[$symbol])) {
                return;
            }

            $this->lastMessageTime[$symbol] = time();

            $currencyId = $this->symbolCurrencyMap[$symbol];
            $price = (float)$tradeData['p'];
            $quantity = (float)$tradeData['q'];
            $tradeTime = (int)$tradeData['T'];


            $klineTradeData = [
                'currency_id' => $currencyId,
                'market_type' => 1, // 现货市场
                'price' => $price,
                'quantity' => $quantity,
                'trade_time' => $tradeTime,
                'out_trade' => 1
            ];

            //发布成交数据到redis
            go(function ()use($klineTradeData,$symbol){
                try {
                    $this->redis->publish(TradeSubscribeKey::getCryptoTradeChannelKey(MarketType::CRYPTO->value),json_encode($klineTradeData)); //全部币种成交公共频道
                    $this->redis->publish(TradeSubscribeKey::getOuterTradeKey(strtoupper($symbol),MarketType::CRYPTO->value),json_encode($klineTradeData)); //外部币种成交数据频道
                }catch (\Throwable){}
            });

            // 2. 最新价格缓存（供其他功能使用）
            $this->priceBuffer[$symbol] = [
                'currency_id' => $currencyId,
                'symbol' => $symbol,
                'price' => $price,
                'timestamp' => time(),
                'market_type' => 1
            ];

        } catch (\Throwable $e) {
            // 静默处理错误
        }
    }

    private function handleReconnect(): void
    {
        if ($this->isReconnecting) {
            return; // 避免重复重连
        }
        
        $this->isReconnecting = true;
        
        try {
            if ($this->reconnectAttempts >= self::MAX_RECONNECT_ATTEMPTS) {
                $this->logger->get('trade')->error("Max reconnect attempts reached, waiting longer before retry");
                Coroutine::sleep(60);
                $this->reconnectAttempts = 0;
            } else {
                $this->reconnectAttempts++;
                $delay = min(30, $this->reconnectAttempts * 5);
                $this->logger->get('trade')->warning("Reconnecting in {$delay} seconds (attempt {$this->reconnectAttempts})");
                Coroutine::sleep($delay);
            }

            // 执行重连
            $this->connectWebSocket();
            
            if ($this->isConnected) {
                $this->logger->get('trade')->info("WebSocket reconnected successfully");
                $this->reconnectAttempts = 0;
            }
        } catch (\Throwable $e) {
            $this->logger->get('trade')->error("Reconnection failed: " . $e->getMessage());
            $this->isReconnecting = false;
        }
    }

    private function startHeartbeatTimer(): void
    {
        $this->heartbeatTimerId = \Swoole\Timer::tick(15000, function () {
            if ($this->wsClient && $this->isConnected && !$this->isReconnecting) {
                try {
                    $frame = new \Swoole\WebSocket\Frame();
                    $frame->opcode = WEBSOCKET_OPCODE_PONG;
                    $frame->data = '';

                    $this->queueMessage($frame);
                } catch (\Throwable $e) {

                }
            }
        });
    }

    private function startStatsTimer(): void
    {
        $this->statsTimerId = \Swoole\Timer::tick(30000, function () {
            if ($this->isConnected && !$this->isReconnecting) {
                $this->outputStats();
            }
        });
    }

    private function startBatchTimer(): void
    {
        $this->batchTimerId = \Swoole\Timer::tick(1000, function () {
            if ($this->isConnected && !$this->isReconnecting) {
                try{
                    $this->processBatchPrices();
                }catch (\Throwable $e) {

                }
            }
        });
    }

    private function startConnectionCheckTimer(): void
    {
        $this->connectionCheckTimerId = \Swoole\Timer::tick(10000, function () {
            $this->checkConnectionHealth();
        });
    }

    private function checkConnectionHealth(): void
    {
        if (!$this->isConnected || !$this->wsClient || $this->isReconnecting) {
            return;
        }

        $currentTime = time();
        $timeSinceLastReceive = $currentTime - $this->lastReceiveTime;

        // 检查是否超过60秒没有接收到任何数据
        if ($timeSinceLastReceive > 60) {
            $this->logger->get('trade')->warning("No data received for {$timeSinceLastReceive} seconds, marking connection as dead");
            $this->isConnected = false;
        } elseif ($timeSinceLastReceive > 30) {
            // 30秒没有数据，记录警告但不立即重连
            $this->logger->get('trade')->info("No data received for {$timeSinceLastReceive} seconds");
        }
    }

    private function processBatchPrices(): void
    {
        if (empty($this->priceBuffer)) {
            return;
        }

        $batch = array_values($this->priceBuffer);
        $this->priceBuffer = [];

        go(function () use ($batch) {
            $this->pushToQueueWithRetry($batch);
        });
    }

    private function pushToQueueWithRetry(array $batch): void
    {
        try {
            $job = new AsyncFunExecutorJob(
                'App\MarketData\Service\TradeSaveService',
                'batchPriceSyncRedis',
                [$batch]
            );
            pushAsyncJob("async-func-executor",$job);
        }catch (\Throwable){}
    }

    private function outputStats(): void
    {
        $totalSymbols = count($this->symbols);
        $receivedSymbols = count($this->lastMessageTime);
        $currentTime = time();

        $timeoutSymbols = [];
        foreach ($this->symbols as $symbol) {
            if (!isset($this->lastMessageTime[$symbol]) ||
                ($currentTime - $this->lastMessageTime[$symbol]) > 60) { // 增加到60秒
                $timeoutSymbols[] = $symbol;
            }
        }

        // 处理超时的币种重新订阅
        if (!empty($timeoutSymbols) && $this->isConnected && !$this->isReconnecting) {
            $timeoutCount = count($timeoutSymbols);
            $timeoutRatio = $totalSymbols > 0 ? ($timeoutCount / $totalSymbols) : 0;

            // 如果超时币种超过50%，标记需要重连
            if ($timeoutRatio > 0.5) {
                $this->logger->get('trade')->warning("Too many timeout symbols ({$timeoutCount}/{$totalSymbols}), marking connection for reconnect");
                $this->isConnected = false;
            } else {
                // 否则只重新订阅超时的币种
                $this->resubscribeTimeoutSymbols($timeoutSymbols);
            }
        }
    }

    private function resubscribeTimeoutSymbols(array $timeoutSymbols): void
    {
        if (!$this->wsClient || !$this->isConnected || $this->isReconnecting) {
            return;
        }

        try {
            // 先取消订阅超时的币种
            $unsubscribeStreams = [];
            foreach ($timeoutSymbols as $symbol) {
                $unsubscribeStreams[] = "{$symbol}@trade";
            }

            if (!empty($unsubscribeStreams)) {
                $unsubscribeMessage = [
                    'method' => 'UNSUBSCRIBE',
                    'params' => $unsubscribeStreams,
                    'id' => time()
                ];
                $this->queueMessage(json_encode($unsubscribeMessage));

                $subscribeMessage = [
                    'method' => 'SUBSCRIBE',
                    'params' => $unsubscribeStreams,
                    'id' => time() + 1
                ];
                $this->queueMessage(json_encode($subscribeMessage));

                // 清除这些币种的最后消息时间，让它们重新开始计时
                foreach ($timeoutSymbols as $symbol) {
                    unset($this->lastMessageTime[$symbol]);
                }
            }

        } catch (\Throwable $e) {
            $this->logger->get('trade')->error("Failed to resubscribe timeout symbols: " . $e->getMessage());
        }
    }

    private function startMessageQueueTimer(): void
    {
        $this->messageQueueTimerId = \Swoole\Timer::tick(1000, function () {
            if ($this->isConnected && !$this->isReconnecting) {
                try {
                    $this->processMessageQueue();
                } catch (\Throwable $e) {

                }
            }
        });
    }

    private function queueMessage($message): void
    {
        $this->messageQueue[] = $message;
    }

    private function processMessageQueue(): void
    {
        if (empty($this->messageQueue) || !$this->wsClient || !$this->isConnected) {
            return;
        }

        $messagesToSend = array_splice($this->messageQueue, 0, 3);

        foreach ($messagesToSend as $message) {
            try {
                if ($message instanceof \Swoole\WebSocket\Frame) {
                    $result = $this->wsClient->push($message, $message->opcode);
                } else {
                    $result = $this->wsClient->push($message);
                }
            } catch (\Throwable $e) {
                $this->isConnected = false;
                break;
            }
        }
    }

    private function clearTimers(): void
    {
        if ($this->heartbeatTimerId > 0) {
            \Swoole\Timer::clear($this->heartbeatTimerId);
            $this->heartbeatTimerId = 0;
        }

        if ($this->statsTimerId > 0) {
            \Swoole\Timer::clear($this->statsTimerId);
            $this->statsTimerId = 0;
        }

        if ($this->batchTimerId > 0) {
            \Swoole\Timer::clear($this->batchTimerId);
            $this->batchTimerId = 0;
        }

        if ($this->connectionCheckTimerId > 0) {
            \Swoole\Timer::clear($this->connectionCheckTimerId);
            $this->connectionCheckTimerId = 0;
        }

        if ($this->messageQueueTimerId > 0) {
            \Swoole\Timer::clear($this->messageQueueTimerId);
            $this->messageQueueTimerId = 0;
        }
    }
}
