<?php

/**
 * TickerProcess.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/23
 * Website:algoquant.org
 */

namespace App\MarketData\Process\CryptoSpot;

use App\Enum\MarketData\TickerSubscribeKey;
use App\Enum\MarketType;
use App\Enum\ProcessCmdKey;
use App\Job\AsyncFunExecutorJob;
use App\Model\Currency\Currency;
use App\Process\BaseProcess;
use App\Service\RedisFactory\MarketRedis;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\Annotation\Process;
use Psr\Container\ContainerInterface;
use Swoole\Coroutine;
use Swoole\Coroutine\Http\Client;

#[Process(name: "crypto-spot-ticker")]
class TickerProcess extends BaseProcess
{
    public bool $enableCoroutine = true;

    public int $nums = 1;

    public string $cmd_key = '';

    private ?Client $wsClient = null;

    private array $symbols = [];

    private array $symbolCurrencyMap = [];

    private bool $isConnected = false;

    private array $lastMessageTime = [];

    private array $tickerBuffer = [];

    private int $heartbeatTimerId = 0;

    private int $statsTimerId = 0;

    private int $batchTimerId = 0;

    #[Inject]
    public MarketRedis $redis;

    public function __construct(ContainerInterface $container)
    {
        $this->cmd_key = ProcessCmdKey::CRYPTO_SPOT_TICKER_CMD->value;
        parent::__construct($container);
    }

    /**
     * 加密数字货币现货的ticker 行情订阅以及处理
     * @return void
     */

    public function handle(): void
    {
        parent::handle();
    }

    public function runBusinessLogic():void
    {
        $this->loadCurrencyData();
        $this->connectWebSocket();

        while (true) {
            try {
                if (!$this->isConnected && !empty($this->symbols)) {
                    $this->connectWebSocket();
                }
                Coroutine::sleep(5);
            } catch (\Throwable $t) {
                $this->logger->get('ticker')->error("Ticker process error: " . $t->getMessage());
                Coroutine::sleep(5);
            }
        }
    }

    public function parse_cmd(array $message): void
    {
        if (isset($message['type']) && $message['type'] === 'start') {
            $this->logger->get('ticker')->info("Received start command, reloading currency data");
            $this->loadCurrencyData();
            if (!empty($this->symbols)) {
                $this->connectWebSocket();
            }
        }
    }

    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER',false);
    }

    private function loadCurrencyData(): void
    {
        try {
            $currencies = Currency::query()
                ->where('status', 1)
                ->where('is_spotTrade', 1)
                ->where('market_type', MarketType::CRYPTO->value)
                ->get(['id', 'symbol']);

            $this->symbols = [];
            $this->symbolCurrencyMap = [];
            foreach ($currencies as $currency) {
                $symbol = strtolower($currency->getSymbol());
                $this->symbols[] = $symbol;
                $this->symbolCurrencyMap[$symbol] = $currency->getId();
            }

            $this->logger->get('ticker')->info("Loaded " . count($this->symbols) . " currency symbols");
        } catch (\Throwable $e) {
            $this->logger->get('ticker')->error("Failed to load currency data: " . $e->getMessage());
        }
    }

    private function connectWebSocket(): void
    {
        if (empty($this->symbols)) {
            $this->logger->get('ticker')->warning("No symbols to subscribe, skipping WebSocket connection");
            return;
        }

        try {
            if ($this->wsClient) {
                $this->wsClient->close();
            }

            $this->wsClient = new Client('stream.binance.com', 9443, true);

            $config = [
                'timeout' => 60,
                'keep_alive' => true,
            ];

            if (env('REQUEST_PROXY', false)) {
                $httpProxy = env('HTTP_PROXY', 'http://127.0.0.1:1087');
                $proxyUrl = parse_url($httpProxy);
                $config['http_proxy_host'] = $proxyUrl['host'] ?? '127.0.0.1';
                $config['http_proxy_port'] = $proxyUrl['port'] ?? 1087;
                $this->logger->get('ticker')->info("Using proxy: {$config['http_proxy_host']}:{$config['http_proxy_port']}");
            }

            $this->wsClient->set($config);

            $firstSymbol = $this->symbols[0];
            $path = "/ws/{$firstSymbol}@ticker";

            if (!$this->wsClient->upgrade($path)) {
                throw new \Exception("WebSocket upgrade failed");
            }

            $this->isConnected = true;
            $this->logger->get('ticker')->info("WebSocket connected with first symbol: {$firstSymbol}");

            $this->subscribeAllSymbols();
            $this->startHeartbeatTimer();
            $this->startStatsTimer();
            $this->startBatchTimer();

            $this->handleWebSocketMessages();

        } catch (\Throwable $e) {
            $this->isConnected = false;
            $this->clearTimers();
            $this->logger->get('ticker')->error("WebSocket connection failed: " . $e->getMessage());
        }
    }

    private function subscribeAllSymbols(): void
    {
        if (count($this->symbols) <= 1) {
            return;
        }

        $subscribeStreams = [];
        foreach ($this->symbols as $symbol) {
            $subscribeStreams[] = "{$symbol}@ticker";
        }

        $subscribeMessage = [
            'method' => 'SUBSCRIBE',
            'params' => $subscribeStreams,
            'id' => time()
        ];

        $this->wsClient->push(json_encode($subscribeMessage));
        $this->logger->get('ticker')->info("Subscribed to " . count($subscribeStreams) . " ticker streams");
    }

    private function handleWebSocketMessages(): void
    {
        while ($this->isConnected && $this->wsClient) {
            try {
                $frame = $this->wsClient->recv(1);
                if ($frame === false) {
                    try {
                        $res = $this->wsClient->push(json_encode(['type' => 'ping']));
                        if(!$res){
                            $this->isConnected = false;
                            $this->clearTimers();
                            break;
                        }
                    }catch (\Throwable){
                        $this->isConnected = false;
                        $this->clearTimers();
                        break;
                    }
                    continue;
                }

                if($frame->opcode == WEBSOCKET_OPCODE_PING || $frame->opcode == WEBSOCKET_OPCODE_PONG) {
                    $pingFrame = new \Swoole\WebSocket\Frame();
                    $pingFrame->opcode = WEBSOCKET_OPCODE_PONG;
                    $pingFrame->data = 'pong';
                    $this->wsClient->push($pingFrame, WEBSOCKET_OPCODE_PONG);
                    continue;
                }

                if ($frame->data) {
                    $data = json_decode($frame->data, true);
                    if ($data && isset($data['s'])) {
                        $this->processTicker($data);
                    }
                }
            } catch (\Throwable $e) {
                $this->logger->get('ticker')->error("WebSocket message handling error: " . $e->getMessage());
                $this->isConnected = false;
                $this->clearTimers();
                break;
            }
        }
    }

    private function processTicker(array $tickerData): void
    {
        $symbol = strtolower($tickerData['s']);
        $this->lastMessageTime[$symbol] = time();

        if (!isset($this->symbolCurrencyMap[$symbol])) {
            return;
        }

        $currencyId = $this->symbolCurrencyMap[$symbol];

        $tickerArray = [
            'currency_id' => $currencyId,
            'market_type' => MarketType::CRYPTO->value,
            'price_change' => $tickerData['p'] ?? '0',
            'price_changeP' => $tickerData['P'] ?? '0',
            'pre_close_price' => $tickerData['x'] ?? '0',
            'last_price' => $tickerData['c'] ?? '0',
            'last_qty' => $tickerData['Q'] ?? '0',
            'open_price' => $tickerData['o'] ?? '0',
            'high_price' => $tickerData['h'] ?? '0',
            'low_price' => $tickerData['l'] ?? '0',
            'volume' => $tickerData['v'] ?? '0'
        ];

        $this->tickerBuffer[] = $tickerArray;

        $this->publishTickerData($tickerArray);
    }

    private function publishTickerData(array $ticker): void
    {
        go(function()use($ticker){
            try {
                $this->redis->publish(TickerSubscribeKey::OUT_TICKER_MESSAGE->value,json_encode($ticker));
            }catch (\Throwable $t){
                echo "spot out ticker publish error:{$t->getMessage()}\n";
            }
        });
    }

    private function startHeartbeatTimer(): void
    {
        $this->heartbeatTimerId = \Swoole\Timer::tick(15000, function () {
            try {
                if ($this->isConnected && $this->wsClient) {
                    $frame = new \Swoole\WebSocket\Frame();
                    $frame->opcode = WEBSOCKET_OPCODE_PING;
                    $frame->data = 'ping';

                    $result = $this->wsClient->push($frame,WEBSOCKET_OPCODE_PING);
                    if (!$result) {
                        $this->logger->get('ticker')->warning("Failed to send pong frame");
                        $this->isConnected = false;
                        $this->clearTimers();
                    }
                } else {
                    $this->clearTimers();
                }
            } catch (\Throwable $e) {
                $this->logger->get('ticker')->error("Heartbeat error: " . $e->getMessage());
                $this->isConnected = false;
                $this->clearTimers();
            }
        });
    }

    private function startStatsTimer(): void
    {
        $this->statsTimerId = \Swoole\Timer::tick(30000, function () {
            $this->outputStats();
        });
    }

    private function startBatchTimer(): void
    {
        $this->batchTimerId = \Swoole\Timer::tick(1000, function () {
            $this->processBatchTickers();
        });
    }

    private function processBatchTickers(): void
    {
        try {
            if (empty($this->tickerBuffer)) {
                return;
            }

            $batch = $this->tickerBuffer;
            $this->tickerBuffer = [];

            go(function () use ($batch) {
                $job = new AsyncFunExecutorJob(
                    'App\MarketData\Service\TickerSaveService',
                    'batchTickerSyncRedis',
                    [$batch]
                );

                pushAsyncJob("async-func-executor",$job);
            });
        }catch (\Throwable){}
    }

    private function outputStats(): void
    {
        $currentTime = time();
        $noDataSymbols = [];
        $timeoutSymbols = [];
        foreach ($this->symbols as $symbol) {
            if (!isset($this->lastMessageTime[$symbol]) ||
                ($currentTime - $this->lastMessageTime[$symbol]) > 30) {
                $timeoutSymbols[] = $symbol;
            }
        }

        if (!empty($timeoutSymbols) && $this->isConnected && $this->wsClient) {
            $this->resubscribeTimeoutSymbols($timeoutSymbols);
        }
    }

    private function resubscribeTimeoutSymbols(array $timeoutSymbols): void
    {
        try {
            $subscribeStreams = [];
            foreach ($timeoutSymbols as $symbol) {
                $subscribeStreams[] = "{$symbol}@ticker";
            }

            $subscribeMessage = [
                'method' => 'SUBSCRIBE',
                'params' => $subscribeStreams,
                'id' => time()
            ];

            $this->wsClient->push(json_encode($subscribeMessage));
            //$this->logger->get('ticker')->info("Resubscribed to " . count($subscribeStreams) . " timeout symbols: " . implode(', ', array_map('strtoupper', $timeoutSymbols)));
        } catch (\Throwable $e) {
            $this->logger->get('ticker')->error("Failed to resubscribe timeout symbols: " . $e->getMessage());
        }
    }

    private function clearTimers(): void
    {
        if ($this->heartbeatTimerId > 0) {
            \Swoole\Timer::clear($this->heartbeatTimerId);
            $this->heartbeatTimerId = 0;
        }

        if ($this->statsTimerId > 0) {
            \Swoole\Timer::clear($this->statsTimerId);
            $this->statsTimerId = 0;
        }

        if ($this->batchTimerId > 0) {
            \Swoole\Timer::clear($this->batchTimerId);
            $this->batchTimerId = 0;
        }
    }
}