<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 委托订单价格监控进程
 */

namespace App\Process\Spot;

use App\Enum\CommissionRedisKey;
use App\Enum\MarketData\TradeSubscribeKey;
use App\Enum\MarketType;
use App\Http\Api\Service\V1\TradeSpotCommissionService;
use App\Service\RedisFactory\CacheRedis;
use App\Service\RedisFactory\MarketRedis;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Process\AbstractProcess;
use Hyperf\Process\Annotation\Process;
use Hyperf\Redis\Redis;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;
use Swoole\Coroutine;
use Swoole\Coroutine\Channel;

/**
 * 现货委托订单处理进程
 */
#[Process(name: 'commission_price_monitor', enableCoroutine: true)]
class CommissionPriceMonitorProcess extends AbstractProcess
{
    #[Inject]
    protected TradeSpotCommissionService $commissionService;

    #[Inject]
    protected MarketRedis $redis;

    #[Inject]
    protected CacheRedis $cacheRedis;

    protected LoggerInterface $logger;

    /**
     * 成交数据处理队列
     */
    protected Channel $tradeDataChannel;

    /**
     * 队列容量
     */
    protected int $channelCapacity = 10000;

    /**
     * 清理间隔时间（秒）
     */
    protected int $cleanupInterval = 3600; // 1小时

    protected int $lastCleanupTime = 0;

    public function __construct(ContainerInterface $container, LoggerFactory $loggerFactory)
    {
        parent::__construct($container);
        $this->logger = $loggerFactory->get('commission_monitor');
        
        // 初始化Channel队列
        $this->tradeDataChannel = new Channel($this->channelCapacity);
    }

    public function handle(): void
    {
        $this->logger->info('委托订单价格监控进程启动');

        try {
            // 1. 初始化：加载现有委托订单到Redis
            $this->initializeCommissionOrders();

            // 2. 启动成交数据处理协程
            $this->startTradeDataProcessor();

            // 3. 启动定期清理协程
            $this->startPeriodicCleanup();

            // 4. 启动成交数据订阅（主协程）
            $this->subscribeToTradeData();

            while (true){
                Coroutine::sleep(1);
            }

        } catch (\Throwable $e) {
            $this->logger->error('委托订单监控进程启动失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 初始化：将数据库中的委托订单加载到Redis
     */
    protected function initializeCommissionOrders(): void
    {
        try {
            $this->logger->info('开始初始化委托订单到Redis');
            
            $result = $this->commissionService->loadPendingOrdersToRedis();
            
            $this->logger->info('委托订单初始化完成', [
                'loaded_count' => $result['count'],
                'currencies' => $result['currencies']
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error('初始化委托订单失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 启动成交数据处理协程
     */
    protected function startTradeDataProcessor(): void
    {
        Coroutine::create(function () {
            $this->logger->info('成交数据处理协程启动');
            
            while (true) {
                try {
                    // 从Channel队列中获取成交数据
                    $tradeData = $this->tradeDataChannel->pop(1.0); // 1秒超时
                    
                    if ($tradeData === false) {
                        // 超时，继续下一次循环
                        continue;
                    }
                    
                    // 处理成交数据
                    $this->processTradeData($tradeData);
                    
                } catch (\Exception $e) {
                    $this->logger->error('成交数据处理协程异常', [
                        'error' => $e->getMessage()
                    ]);
                    
                    // 异常后短暂休眠
                    Coroutine::sleep(0.1);
                }
            }
        });
    }

    /**
     * 启动定期清理协程
     */
    protected function startPeriodicCleanup(): void
    {
        Coroutine::create(function () {
            $this->logger->info('定期清理协程启动');
            
            while (true) {
                try {
                    // 每小时清理一次过期订单
                    Coroutine::sleep(3600);
                    $this->commissionService->cleanupExpiredCommissionOrders();
                    
                    $this->logger->info('定期清理过期委托订单完成');
                    
                } catch (\Exception $e) {
                    $this->logger->error('定期清理协程异常', [
                        'error' => $e->getMessage()
                    ]);
                }
            }
        });
    }

    /**
     * 订阅成交数据
     */
    protected function subscribeToTradeData(): void
    {
        Coroutine::create(function (){
            while (true){
                try {
                    // 获取现货市场的成交数据订阅频道
                    $channel = TradeSubscribeKey::getCryptoTradeChannelKey(MarketType::CRYPTO->value);

                    $this->logger->info('开始订阅成交数据', ['channel' => $channel]);

                    // 订阅成交数据频道
                    $this->redis->subscribe([$channel], function ($_, $channel, $message) {
                        $this->handleTradeMessage($channel, $message);
                    });

                } catch (\Exception $e) {
                    $this->logger->error('订阅成交数据失败', [
                        'error' => $e->getMessage()
                    ]);
                }
                Coroutine::sleep(2);
            }
        });
    }

    /**
     * 处理成交数据消息（快速入队）
     */
    protected function handleTradeMessage(string $channel, string $message): void
    {
        try {
            $data = json_decode($message, true);
            if (!$data || !$this->validateTradeData($data)) {
                return;
            }

            $currencyId = (int)$data['currency_id'];
            $price = (string)$data['price'];
            $marketType = (int)$data['market_type'];

            // 只处理现货交易数据
            if ($marketType !== MarketType::CRYPTO->value) {
                return;
            }

            // 快速将数据放入Channel队列
            $tradeData = [
                'currency_id' => $currencyId,
                'price' => $price,
                'market_type' => $marketType,
                'timestamp' => microtime(true)
            ];

            // 非阻塞推送到队列
//            if (!$this->tradeDataChannel->push($tradeData, 0.001)) {
//                // 队列满了，记录警告但不阻塞
//                $this->logger->warning('成交数据队列已满，丢弃数据', [
//                    'currency_id' => $currencyId,
//                    'price' => $price
//                ]);
//            }

        } catch (\Exception $e) {
            $this->logger->error('处理成交数据消息失败', [
                'channel' => $channel,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 验证成交数据格式
     */
    protected function validateTradeData(array $data): bool
    {
        $requiredFields = ['currency_id', 'market_type', 'price', 'quantity', 'trade_time', 'out_trade'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                $this->logger->warning('成交数据缺少必要字段', [
                    'missing_field' => $field,
                    'data' => $data
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * 处理成交数据（异步处理）
     */
    protected function processTradeData(array $tradeData): void
    {
        try {
            $currencyId = $tradeData['currency_id'];
            $price = $tradeData['price'];

            // 检查并触发符合条件的委托订单
            $this->checkAndTriggerOrdersByPrice($currencyId, $price);

        } catch (\Exception $e) {
            $this->logger->error('处理成交数据失败', [
                'trade_data' => $tradeData,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 根据价格检查并触发委托订单
     */
    protected function checkAndTriggerOrdersByPrice(int $currencyId, string $currentPrice): void
    {
        $triggeredCount = 0;

        try {
            // 检查买单止盈 (价格上涨触发：当前价格 >= 触发价格)
            $triggeredCount += $this->checkOrdersByCondition(
                CommissionRedisKey::getBuyStopProfitKey($currencyId),
                $currentPrice,
                'gte'
            );

            // 检查买单止损 (价格下跌触发：当前价格 <= 触发价格)
            $triggeredCount += $this->checkOrdersByCondition(
                CommissionRedisKey::getBuyStopLossKey($currencyId),
                $currentPrice,
                'lte'
            );

            // 检查卖单止盈 (价格下跌触发：当前价格 <= 触发价格)
            $triggeredCount += $this->checkOrdersByCondition(
                CommissionRedisKey::getSellStopProfitKey($currencyId),
                $currentPrice,
                'lte'
            );

            // 检查卖单止损 (价格上涨触发：当前价格 >= 触发价格)
            $triggeredCount += $this->checkOrdersByCondition(
                CommissionRedisKey::getSellStopLossKey($currencyId),
                $currentPrice,
                'gte'
            );

            // 检查计划买入 (价格下跌到指定价格触发：当前价格 <= 触发价格)
            $triggeredCount += $this->checkOrdersByCondition(
                CommissionRedisKey::getPlanBuyKey($currencyId),
                $currentPrice,
                'lte'
            );

            // 检查计划卖出 (价格上涨到指定价格触发：当前价格 >= 触发价格)
            $triggeredCount += $this->checkOrdersByCondition(
                CommissionRedisKey::getPlanSellKey($currencyId),
                $currentPrice,
                'gte'
            );

            // 检查追踪委托（需要特殊处理）
            $triggeredCount += $this->checkTrailingStopOrders($currencyId, $currentPrice);

            if ($triggeredCount > 0) {
                $this->logger->info('价格触发委托订单', [
                    'currency_id' => $currencyId,
                    'current_price' => $currentPrice,
                    'triggered_count' => $triggeredCount
                ]);
            }

        } catch (\Exception $e) {
            $this->logger->error('检查委托订单失败', [
                'currency_id' => $currencyId,
                'current_price' => $currentPrice,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 检查指定条件的订单
     */
    protected function checkOrdersByCondition(string $redisKey, string $currentPrice, string $condition): int
    {
        $triggeredCount = 0;
        
        try {
            // 根据条件获取符合触发条件的订单ID
            $orderIds = [];
            
            switch ($condition) {
                case 'gte':
                    // 获取触发价格 <= 当前价格的订单
                    $orderIds = $this->cacheRedis->zRangeByScore($redisKey, '-inf', $currentPrice);
                    break;
                    
                case 'lte':
                    // 获取触发价格 >= 当前价格的订单
                    $orderIds = $this->cacheRedis->zRangeByScore($redisKey, $currentPrice, '+inf');
                    break;
            }

            foreach ($orderIds as $orderId) {
                try {
                    // 直接调用服务的triggerOrder方法，避免额外的包装
                    $success = $this->commissionService->triggerOrder((int)$orderId, $currentPrice);
                    
                    if ($success) {
                        // 从Redis中移除已触发的订单
                        $this->cacheRedis->zRem($redisKey, $orderId);
                        
                        // 删除订单详情
                        $detailKey = CommissionRedisKey::getCommissionDetailKey((int)$orderId);
                        $this->cacheRedis->del($detailKey);
                        
                        $triggeredCount++;
                        
                        $this->logger->info('委托订单触发成功', [
                            'order_id' => $orderId,
                            'current_price' => $currentPrice,
                            'redis_key' => $redisKey
                        ]);
                    } else {
                        // 触发失败，也要从Redis中移除，避免重复尝试
                        $this->cacheRedis->zRem($redisKey, $orderId);
                        $detailKey = CommissionRedisKey::getCommissionDetailKey((int)$orderId);
                        $this->cacheRedis->del($detailKey);
                    }
                    
                } catch (\Exception $e) {
                    $this->logger->error('触发委托订单失败', [
                        'order_id' => $orderId,
                        'current_price' => $currentPrice,
                        'error' => $e->getMessage()
                    ]);
                    
                    // 异常情况下也要从Redis中移除，避免重复尝试
                    $this->cacheRedis->zRem($redisKey, $orderId);
                    $detailKey = CommissionRedisKey::getCommissionDetailKey((int)$orderId);
                    $this->cacheRedis->del($detailKey);
                }
            }
            
        } catch (\Exception $e) {
            $this->logger->error('检查委托订单失败', [
                'redis_key' => $redisKey,
                'condition' => $condition,
                'current_price' => $currentPrice,
                'error' => $e->getMessage()
            ]);
        }

        return $triggeredCount;
    }

    /**
     * 检查追踪委托订单（需要特殊处理逻辑）
     */
    protected function checkTrailingStopOrders(int $currencyId, string $currentPrice): int
    {
        // 追踪委托需要更复杂的逻辑，这里暂时返回0
        // 后续可以根据具体需求实现
        return 0;
    }

    /**
     * 定期清理过期订单
     */
    protected function cleanupExpiredOrdersIfNeeded(): void
    {
        $currentTime = time();
        
        if ($currentTime - $this->lastCleanupTime >= $this->cleanupInterval) {
            try {
                $count = $this->commissionService->cleanupExpiredCommissionOrders();
                
                if ($count > 0) {
                    $this->logger->info('清理过期委托订单', ['count' => $count]);
                }

                $this->lastCleanupTime = $currentTime;

            } catch (\Exception $e) {
                $this->logger->error('清理过期委托订单失败', [
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER', false);
    }
} 