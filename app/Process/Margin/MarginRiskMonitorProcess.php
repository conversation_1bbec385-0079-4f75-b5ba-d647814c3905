<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆价格监控进程（重构版 - 基于交易数据触发风险计算）
 */

namespace App\Process\Margin;

use App\Enum\Config\MarginConfigKey;
use App\Enum\MarketData\TradeSubscribeKey;
use App\Enum\MarketType;
use App\Enum\User\UserAssetsCacheKey;
use App\Model\Enums\Trade\Margin\MarginType;
use App\Model\Enums\User\AccountType;
use App\Process\Margin\Service\MarginNotificationService;
use App\Job\Margin\LiquidationJob;
use App\Service\RedisFactory\CacheRedis;
use App\Service\RedisFactory\MarketRedis;
use Hyperf\AsyncQueue\Driver\DriverFactory;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\AbstractProcess;
use Hyperf\Process\Annotation\Process;
use Hyperf\Context\ApplicationContext;
use Psr\Log\LoggerInterface;
use Swoole\Coroutine;

#[Process(
    name: 'margin-trade-monitor',
    nums: 1,
    redirectStdinStdout: false,
    pipeType: 2,
    enableCoroutine: true
)]
class MarginRiskMonitorProcess extends AbstractProcess
{
    #[Inject]
    protected MarketRedis $redis;

    #[Inject]
    protected CacheRedis $cacheRedis;

    protected DriverFactory $driverFactory;
    protected LoggerInterface $logger;
    protected MarginNotificationService $notificationService;

    // Swoole Channel队列
    protected \Swoole\Coroutine\Channel $tradeChannel;
    protected int $channelCapacity = 10000; // 队列容量

    // 统计信息
    protected int $receivedCount = 0;
    protected int $processedCount = 0;
    protected int $droppedCount = 0;
    protected float $lastStatsTime = 0;

    // 价格缓存（币种ID => 最新价格）- 通过订阅实时维护
    protected array $priceCache = [];

    // 价格变化阈值配置（写死）
    protected array $priceThresholds = [
        'default' => 0.005,        // 默认0.5%变化触发
        'high_volatility' => 0.01, // 高波动币种1%触发
        'stable_coins' => 0.005,   // 稳定币0.5%触发
    ];



    public function handle(): void
    {
        $container = ApplicationContext::getContainer();
        $this->driverFactory = $container->get(DriverFactory::class);
        $this->logger = $container->get(LoggerInterface::class);
        $this->notificationService = $container->get(MarginNotificationService::class);

        // 初始化Channel队列
        $this->tradeChannel = new \Swoole\Coroutine\Channel($this->channelCapacity);

        $this->logger->info('杠杆交易监控进程启动', [
            'channel_capacity' => $this->channelCapacity
        ]);

        // 启动trade数据处理协程
        Coroutine::create(function () {
            $this->processTradeData();
        });

        // 订阅现有的trade消息频道
        $this->subscribeToTradeMessages();
    }

    /**
     * 订阅现有的trade消息频道
     */
    protected function subscribeToTradeMessages(): void
    {
        // 使用现有的trade订阅频道
        $channels = [
            TradeSubscribeKey::getCryptoTradeChannelKey(MarketType::CRYPTO->value)
        ];

        $this->logger->info('开始订阅trade频道', ['channels' => $channels]);

        while (true) {
            try {
                $this->redis->subscribe($channels, function ($redis, $channel, $message) {
                    $this->receivedCount++;

                    $currentTime = microtime(true);
                    $tradeData = [
                        'channel' => $channel,
                        'message' => $message,
                        'timestamp' => $currentTime
                    ];

                    // 如果队列满了，丢弃老数据写入新数据
                    if ($this->tradeChannel->isFull()) {
                        // 尝试丢弃一些老数据
                        $discardCount = 0;
                        $maxDiscard = min(50, $this->tradeChannel->length() / 2); // 最多丢弃一半或50条

                        while ($discardCount < $maxDiscard && !$this->tradeChannel->isEmpty()) {
                            $oldData = $this->tradeChannel->pop(0.001); // 非阻塞pop，超时0.001秒
                            if ($oldData !== false) {
                                $discardCount++;
                                $this->droppedCount++;
                            } else {
                                break; // 如果pop失败，跳出循环
                            }
                        }

//                        if ($discardCount > 0) {
//                            $this->logger->warning('队列积压，丢弃老数据', [
//                                'discarded_count' => $discardCount,
//                                'queue_length' => $this->tradeChannel->length(),
//                                'total_dropped' => $this->droppedCount
//                            ]);
//                        }
                    }

                    // 写入新数据（非阻塞）
                    if (!$this->tradeChannel->push($tradeData, 0.001)) {
                        // 如果还是写入失败，记录丢弃
                        $this->droppedCount++;
                    }
                });
            } catch (\Exception $e) {
                $this->logger->error('trade订阅异常', [
                    'error' => $e->getMessage()
                ]);
                sleep(2);
            }
        }
    }

    /**
     * 处理trade数据的协程（单独的协程处理队列中的数据）
     */
    protected function processTradeData(): void
    {
        $this->logger->info('trade数据处理协程启动');

        while (true) {
            try {
                $data = $this->tradeChannel->pop(0.1);

                if ($data === false) {
                    continue;
                }

                $this->processedCount++;

                // 处理trade消息
                $this->handleTradeMessage($data['channel'], $data['message']);
            } catch (\Exception $e) {
                $this->logger->error('处理trade数据异常', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                // 出错时短暂休眠，避免错误循环
                Coroutine::sleep(0.1);
            }
        }
    }

    /**
     * 处理trade消息
     */
    protected function handleTradeMessage(string $channel, string $message): void
    {
        try {
            $tradeData = json_decode($message, true);

            if (!$tradeData || !isset($tradeData['currency_id'], $tradeData['price'])) {
                return;
            }

            $currencyId = (int)$tradeData['currency_id'];
            $marketType = (int)($tradeData['market_type'] ?? 1);
            $currentPrice = (float)$tradeData['price'];
            $quantity = (float)($tradeData['quantity'] ?? 0);
            $tradeTime = $tradeData['trade_time'] ?? time();
            $outTrade = (int)($tradeData['out_trade'] ?? 0);

            // 只处理现货市场的交易数据（现货价格用于杠杆风险计算）
            if ($marketType !== MarketType::CRYPTO->value) {
                return;
            }

            // 只处理外部交易数据
            if ($outTrade !== 1) {
                return;
            }

            // 更新价格缓存
            $this->priceCache[$currencyId] = $currentPrice;

            // 检查是否需要触发风险计算
            if ($this->shouldTriggerRiskCalculation($currencyId, $currentPrice)) {
                // 直接在当前协程中计算风险，避免创建新协程
                $this->calculateAndProcessRisk($currencyId, $currentPrice, $quantity, $tradeTime);
            }

        } catch (\Exception $e) {
            $this->logger->error('处理trade消息异常', [
                'channel' => $channel,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 判断是否需要触发风险计算
     */
    protected function shouldTriggerRiskCalculation(int $currencyId, float $currentPrice): bool
    {
        // 检查该币种是否有仓位需要监控
        $positionCount = $this->cacheRedis->hLen(
            MarginConfigKey::getCurrencyPositionsKey($currencyId)
        );

        if ($positionCount === 0) {
            return false; // 无仓位，跳过
        }

        // 获取价格变化阈值（写死配置）
        $threshold = $this->priceThresholds['default'];

        // 获取最后计算价格
        $lastPrice = $this->getLastCalculatedPrice($currencyId);

        if ($lastPrice === 0.0) {
            return true; // 首次计算
        }

        // 计算价格变化比例
        $priceChangeRatio = abs($currentPrice - $lastPrice) / $lastPrice;

        return $priceChangeRatio >= $threshold;
    }

    /**
     * 计算并处理风险（在进程中直接执行）
     */
    protected function calculateAndProcessRisk(int $currencyId, float $currentPrice, float $quantity = 0, $tradeTime = null): void
    {
        try {
            $startTime = microtime(true);

            $this->logger->info('开始风险计算', [
                'currency_id' => $currencyId,
                'current_price' => $currentPrice,
                'trade_quantity' => $quantity,
                'trade_time' => $tradeTime
            ]);

            // 1. 从Redis获取该币种的所有仓位
            $positions = $this->getCurrencyPositionsFromCache($currencyId);

            if (empty($positions)) {
                return;
            }

            // 2. 并行计算风险
            $riskResults = $this->parallelCalculateRisk($positions, $currentPrice);

            // 3. 更新风险等级索引
            $this->updateRiskIndexes($currencyId, $riskResults);

            // 4. 处理风险预警和强平
            $this->processRiskActions($riskResults);

            // 5. 更新最后计算价格
            $this->cacheRedis->hSet(
                MarginConfigKey::PRICE_LAST_CALCULATED,
                (string)$currencyId,
                (string)$currentPrice
            );

            // $endTime = microtime(true);
            // $duration = round(($endTime - $startTime) * 1000, 2);

            // $this->logger->info('风险计算完成', [
            //     'currency_id' => $currencyId,
            //     'total_positions' => count($positions),
            //     'duration_ms' => $duration,
            //     'risk_summary' => $this->getRiskSummary($riskResults)
            // ]);

        } catch (\Exception $e) {
            $this->logger->error('风险计算异常', [
                'currency_id' => $currencyId,
                'current_price' => $currentPrice,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 从Redis缓存获取币种的所有仓位数据
     */
    protected function getCurrencyPositionsFromCache(int $currencyId): array
    {
        $positionsData = $this->cacheRedis->hGetAll(
            MarginConfigKey::getCurrencyPositionsKey($currencyId)
        );

        $positions = [];
        foreach ($positionsData as $positionKey => $data) {
            $positionData = json_decode($data, true);
            if ($positionData) {
                $positionData['position_key'] = $positionKey;
                $positions[] = $positionData;
            }
        }

        return $positions;
    }

    /**
     * 获取最后计算价格
     */
    protected function getLastCalculatedPrice(int $currencyId): float
    {
        $price = $this->cacheRedis->hGet(
            MarginConfigKey::PRICE_LAST_CALCULATED,
            (string)$currencyId
        );

        return $price ? (float)$price : 0.0;
    }

    /**
     * 并行计算风险
     */
    protected function parallelCalculateRisk(array $positions, float $currentPrice): array
    {
        $results = [
            'safe' => [],
            'warning' => [],
            'danger' => [],
            'liquidation' => []
        ];

        // 分批并行处理
        $batchSize = 50; // 每批50个仓位
        $batches = array_chunk($positions, $batchSize);
        $channel = new \Swoole\Coroutine\Channel(count($batches));

        // 启动多个协程并行计算
        foreach ($batches as $batchIndex => $batch) {
            Coroutine::create(function () use ($batch, $currentPrice, $channel, $batchIndex) {
                $batchResults = [];

                foreach ($batch as $positionData) {
                    try {
                        $riskInfo = $this->calculatePositionRiskFromCache($positionData, $currentPrice);

                        // 更新仓位缓存中的风险数据
                        $this->updatePositionRiskCache($positionData['position_key'], $riskInfo, $positionData['currency_id']);

                        $batchResults[] = $riskInfo;

                    } catch (\Exception $e) {
                        $this->logger->error('单个仓位风险计算失败', [
                            'position_id' => $positionData['position_id'],
                            'error' => $e->getMessage()
                        ]);
                    }
                }

                $channel->push([
                    'batch_index' => $batchIndex,
                    'results' => $batchResults
                ]);
            });
        }

        // 收集所有批次的结果
        for ($i = 0; $i < count($batches); $i++) {
            $batchResult = $channel->pop();

            foreach ($batchResult['results'] as $riskInfo) {
                $riskLevel = $riskInfo['risk_level'];
                $results[$riskLevel][] = $riskInfo;
            }
        }

        return $results;
    }

    /**
     * 基于缓存数据计算仓位风险
     */
    protected function calculatePositionRiskFromCache(array $positionData, float $currentPrice): array
    {
        // 计算未实现盈亏
        $unrealizedPnl = $this->calculateUnrealizedPnlFromCache($positionData, $currentPrice);

        // 计算净资产（从用户资产缓存获取实时净资产）
        $netAsset = $this->calculateNetAssetFromCache($positionData, $unrealizedPnl);

        // 获取维持保证金率
        $keepRate = (float)($positionData['keep_rate'] ?? 0.05);

        // 计算维持保证金
        $quantity = (float)$positionData['quantity'];
        $positionValue = $quantity * $currentPrice;
        $maintenanceMargin = $positionValue * $keepRate;

        // 计算保证金率 = 维持保证金 / 净资产
        $marginRatio = $netAsset > 0 ? $maintenanceMargin / $netAsset : 999.0;

        // 确定风险等级（基于仓位的维持保证金率）
        $riskLevel = $this->determineRiskLevel($marginRatio, $keepRate);

        // 计算强平价格
        $liquidationPrice = $this->calculateLiquidationPrice($positionData);

        // 只在触发强平时记录详细日志
        if ($riskLevel === 'liquidation') {
            $this->logger->warning('🚨 触发强平条件', [
                'position_id' => $positionData['position_id'],
                'user_id' => $positionData['user_id'],
                'currency_id' => $positionData['currency_id'],
                'side' => $positionData['side'] == 1 ? 'LONG' : 'SHORT',
                'entry_price' => $positionData['entry_price'],
                'current_price' => $currentPrice,
                'quantity' => $quantity,
                'net_asset' => $netAsset,
                'maintenance_margin' => $maintenanceMargin,
                'margin_ratio' => $marginRatio,
                'liquidation_price' => $liquidationPrice,
                'margin_type' => $positionData['margin_type'] == MarginType::CROSS->value ? 'CROSS' : 'ISOLATED'
            ]);
        }

        return [
            'position_key' => $positionData['position_key'],
            'position_id' => $positionData['position_id'],
            'user_id' => $positionData['user_id'],
            'currency_id' => $positionData['currency_id'],
            'current_price' => $currentPrice,
            'unrealized_pnl' => $unrealizedPnl,
            'net_asset' => $netAsset,
            'margin_ratio' => $marginRatio,
            'liquidation_price' => $liquidationPrice,
            'risk_level' => $riskLevel,
            'maintenance_margin' => $maintenanceMargin,
            'keep_rate' => $keepRate,
            'should_liquidate' => $riskLevel === 'liquidation',
        ];
    }

    /**
     * 计算未实现盈亏
     */
    protected function calculateUnrealizedPnlFromCache(array $positionData, float $currentPrice): float
    {
        $entryPrice = (float)$positionData['entry_price'];
        $quantity = (float)$positionData['quantity'];
        $side = (int)$positionData['side'];

        if ($side === 1) { // 多头
            return ($currentPrice - $entryPrice) * $quantity;
        } else { // 空头
            return ($entryPrice - $currentPrice) * $quantity;
        }
    }

    /**
     * 计算净资产（从用户资产缓存获取实时净资产）
     */
    protected function calculateNetAssetFromCache(array $positionData, float $unrealizedPnl): float
    {
        $userId = (int)$positionData['user_id'];
        $currencyId = (int)$positionData['currency_id'];
        $marginType = (int)$positionData['margin_type'];

        try {
            if ($marginType === MarginType::CROSS->value) {
                // 全仓：获取用户全仓杠杆账户的所有资产净值
                $netAsset = $this->calculateCrossMarginNetAsset($userId, $currencyId);
            } else {
                // 逐仓：获取该交易对的资产净值
                $netAsset = $this->calculateIsolatedMarginNetAsset($userId, $currencyId);
            }

            $totalNetAsset = $netAsset + $unrealizedPnl;



            return $totalNetAsset;

        } catch (\Exception $e) {
            $this->logger->warning('计算净资产失败，使用降级方案', [
                'position_id' => $positionData['position_id'],
                'error' => $e->getMessage()
            ]);

            // 降级：使用保证金作为净资产
            $marginAmount = (float)$positionData['margin_amount'];
            return $marginAmount + $unrealizedPnl;
        }
    }

    /**
     * 确定风险等级（基于仓位的维持保证金率）
     */
    protected function determineRiskLevel(float $marginRatio, float $keepRate): string
    {
        // 强平条件：当前保证金率 >= 维持保证金率
        if ($marginRatio >= $keepRate) {
            return 'liquidation';  // 触发强平
        }

        // 风险等级基于距离强平的程度
        $riskRatio = $marginRatio / $keepRate;

        if ($riskRatio >= 0.9) {
            return 'danger';       // 90%以上接近强平
        } elseif ($riskRatio >= 0.7) {
            return 'warning';      // 70%以上需要警告
        } else {
            return 'safe';         // 70%以下相对安全
        }
    }

    /**
     * 计算全仓杠杆净资产
     */
    protected function calculateCrossMarginNetAsset(int $userId, int $currencyId): float
    {
        $assetsKey = UserAssetsCacheKey::getCrosstAssetsKey($userId, AccountType::MARGIN->value);
        $userAssets = $this->cacheRedis->hGetAll($assetsKey);

        if (empty($userAssets)) {
            $this->logger->warning('全仓资产缓存为空', [
                'user_id' => $userId,
                'assets_key' => $assetsKey
            ]);
            return 0.0;
        }

        $totalNetValue = 0.0;

        // 遍历所有币种，计算净资产
        foreach ($userAssets as $field => $value) {
            // 只处理币种ID字段（纯数字字段）
            if (is_numeric($field)) {
                $assetCurrencyId = (int)$field;
                $availableAmount = (float)$value;
                $frozenAmount = (float)($userAssets["{$assetCurrencyId}-frozen"] ?? 0);
                $borrowAmount = (float)($userAssets["{$assetCurrencyId}-borrow"] ?? 0);

                // 净持仓 = 可用余额 + 冻结资金 - 借贷总额
                $netAmount = $availableAmount + $frozenAmount - $borrowAmount;

                if ($netAmount > 0) {
                    // 使用实时价格计算价值
                    $currentPrice = $this->getCurrencyPrice($assetCurrencyId);
                    $currencyValue = $netAmount * $currentPrice;
                    $totalNetValue += $currencyValue;


                }
            }
        }



        return $totalNetValue;
    }

    /**
     * 计算逐仓杠杆净资产
     */
    protected function calculateIsolatedMarginNetAsset(int $userId, int $currencyId): float
    {
        $assetsKey = UserAssetsCacheKey::getIsolatedAssetsKey($userId, AccountType::ISOLATED->value, $currencyId);
        $userAssets = $this->cacheRedis->hGetAll($assetsKey);

        if (empty($userAssets)) {
            $this->logger->warning('逐仓资产缓存为空', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'assets_key' => $assetsKey
            ]);
            return 0.0;
        }

        $totalNetValue = 0.0;

        // 计算基础币净资产
        $baseCurrencyAmount = (float)($userAssets[$currencyId] ?? 0);
        $baseCurrencyFrozen = (float)($userAssets["{$currencyId}-frozen"] ?? 0);
        $baseCurrencyBorrow = (float)($userAssets["{$currencyId}-borrow"] ?? 0);

        // 基础币净持仓 = 可用余额 + 冻结资金 - 借贷总额
        $baseCurrencyNet = $baseCurrencyAmount + $baseCurrencyFrozen - $baseCurrencyBorrow;

        $baseCurrencyValue = 0.0;
        if ($baseCurrencyNet > 0) {
            $baseCurrencyPrice = $this->getCurrencyPrice($currencyId);
            $baseCurrencyValue = $baseCurrencyNet * $baseCurrencyPrice;
            $totalNetValue += $baseCurrencyValue;
        }

        // 计算计价币净资产
        $marginQuoteAmount = (float)($userAssets["{$currencyId}-margin_quote"] ?? 0);
        $marginBorrowAmount = (float)($userAssets["{$currencyId}-margin_borrow"] ?? 0);

        // 计价币净持仓 = 计价币余额 - 计价币借贷
        $quoteCurrencyNet = $marginQuoteAmount - $marginBorrowAmount;

        // 计价币价值（包含负债，负数表示欠款）
        $quoteCurrencyValue = $quoteCurrencyNet * 1.0; // USDT价格为1
        $totalNetValue += $quoteCurrencyValue;



        return $totalNetValue;
    }

    /**
     * 计算强平价格（使用真实的维持保证金率）
     */
    protected function calculateLiquidationPrice(array $positionData): float
    {
        $entryPrice = (float)$positionData['entry_price'];
        $quantity = (float)$positionData['quantity'];
        $side = (int)$positionData['side'];
        $marginAmount = (float)$positionData['margin_amount'];
        $keepRate = (float)($positionData['keep_rate'] ?? 0.05); // 维持保证金率

        // 获取当前净资产（不包含未实现盈亏）
        $userId = (int)$positionData['user_id'];
        $currencyId = (int)$positionData['currency_id'];
        $marginType = (int)$positionData['margin_type'];

        try {
            if ($marginType === MarginType::CROSS->value) {
                $netAsset = $this->calculateCrossMarginNetAsset($userId, $currencyId);
            } else {
                $netAsset = $this->calculateIsolatedMarginNetAsset($userId, $currencyId);
            }
        } catch (\Exception $e) {
            $netAsset = $marginAmount; // 降级使用保证金
        }

        // 重新获取计价币净额（需要重新计算，不能直接用netAsset）
        try {
            if ($marginType === MarginType::ISOLATED->value) {
                // 逐仓：重新获取计价币净额
                $assetsKey = UserAssetsCacheKey::getIsolatedAssetsKey($userId, AccountType::ISOLATED->value, $currencyId);
                $userAssets = $this->cacheRedis->hGetAll($assetsKey);

                $marginQuoteAmount = (float)($userAssets["{$currencyId}-margin_quote"] ?? 0);
                $marginBorrowAmount = (float)($userAssets["{$currencyId}-margin_borrow"] ?? 0);
                $quoteCurrencyNet = $marginQuoteAmount - $marginBorrowAmount;
            } else {
                // 全仓：使用当前净资产减去BTC价值
                $quoteCurrencyNet = $netAsset - ($quantity * $entryPrice);
            }
        } catch (\Exception $e) {
            // 降级：使用简化计算
            $quoteCurrencyNet = $netAsset - ($quantity * $entryPrice);
        }

        // 强平价格计算公式
        // 强平条件：BTC价值 + 计价币净额 = BTC价值 × 维持保证金率
        // (数量 × P_liq) + 计价币净额 = (数量 × P_liq) × 维持保证金率

        if ($side === 1) { // 多头
            // 数量 × P_liq × (1 - 维持保证金率) = -计价币净额
            // P_liq = -计价币净额 / (数量 × (1 - 维持保证金率))
            if ($quoteCurrencyNet < 0) {
                $liquidationPrice = -$quoteCurrencyNet / ($quantity * (1 - $keepRate));
            } else {
                $liquidationPrice = 0;
            }
        } else { // 空头
            // 数量 × P_liq × (1 + 维持保证金率) = 计价币净额
            $liquidationPrice = $quoteCurrencyNet / ($quantity * (1 + $keepRate));
        }

        $liquidationPrice = max(0, $liquidationPrice);



        return $liquidationPrice;
    }



    /**
     * 获取币种价格（直接从订阅缓存获取）
     */
    protected function getCurrencyPrice(int $currencyId): float
    {
        // 直接从订阅维护的价格缓存获取
        return $this->priceCache[$currencyId] ?? 1.0;
    }

    /**
     * 更新仓位风险缓存数据
     */
    protected function updatePositionRiskCache(string $positionKey, array $riskInfo, int $currencyId): void
    {
        // 获取原始仓位数据
        $positionData = $this->cacheRedis->hGet(
            MarginConfigKey::getCurrencyPositionsKey($currencyId),
            $positionKey
        );

        if ($positionData) {
            $data = json_decode($positionData, true);

            // 更新风险相关字段
            $data['last_calculated_price'] = $riskInfo['current_price'];
            $data['last_margin_ratio'] = $riskInfo['margin_ratio'];
            $data['last_risk_level'] = $riskInfo['risk_level'];
            $data['unrealized_pnl'] = $riskInfo['unrealized_pnl'];
            $data['net_asset'] = $riskInfo['net_asset'];
            $data['liquidation_price'] = $riskInfo['liquidation_price'];
            $data['updated_at'] = time();

            // 更新缓存
            $this->cacheRedis->hSet(
                MarginConfigKey::getCurrencyPositionsKey($currencyId),
                $positionKey,
                json_encode($data)
            );
        }
    }

    /**
     * 更新风险等级索引
     */
    protected function updateRiskIndexes(int $currencyId, array $riskResults): void
    {
        $riskLevels = ['warning', 'danger', 'liquidation'];

        // 清空旧的风险索引
        foreach ($riskLevels as $level) {
            $this->cacheRedis->del(MarginConfigKey::getRiskLevelKey($level, $currencyId));
        }

        // 重建风险索引
        foreach ($riskResults as $level => $positions) {
            if (!empty($positions) && $level !== 'safe') {
                $positionKeys = array_column($positions, 'position_key');
                if (!empty($positionKeys)) {
                    $this->cacheRedis->sAdd(
                        MarginConfigKey::getRiskLevelKey($level, $currencyId),
                        ...$positionKeys
                    );
                    $this->cacheRedis->expire(
                        MarginConfigKey::getRiskLevelKey($level, $currencyId),
                        3600
                    );
                }
            }
        }
    }

    /**
     * 处理风险预警和强平
     */
    protected function processRiskActions(array $riskResults): void
    {
        // 处理风险预警（异步协程）
        if (!empty($riskResults['warning']) || !empty($riskResults['danger'])) {
            $warningPositions = array_merge($riskResults['warning'], $riskResults['danger']);

            Coroutine::create(function () use ($warningPositions) {
                $this->processRiskWarnings($warningPositions);
            });
        }

        // 处理强平（推送到队列）
        if (!empty($riskResults['liquidation'])) {
            Coroutine::create(function () use ($riskResults) {
                $this->processLiquidations($riskResults['liquidation']);
            });
        }
    }

    /**
     * 处理风险预警
     */
    protected function processRiskWarnings(array $warningPositions): void
    {
        foreach ($warningPositions as $riskInfo) {
            try {
                // 检查预警冷却期
                $warningKey = "margin:warning:{$riskInfo['user_id']}:{$riskInfo['position_id']}:{$riskInfo['risk_level']}";

                if (!$this->cacheRedis->exists($warningKey)) {
                    $this->notificationService->sendRiskWarning(
                        $riskInfo['user_id'],
                        $riskInfo
                    );

                    // 设置冷却期（5分钟）
                    $this->cacheRedis->setex($warningKey, 300, 1);
                }

            } catch (\Exception $e) {
                $this->logger->error('发送风险预警失败', [
                    'position_id' => $riskInfo['position_id'],
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * 处理强平（只推送到队列，不在这里执行强平逻辑）
     */
    protected function processLiquidations(array $liquidationPositions): void
    {
        $liquidationQueue = $this->driverFactory->get('position_monitoring');

        foreach ($liquidationPositions as $riskInfo) {
            try {
                // 检查防重复锁
                $lockKey = "margin:liquidation:lock:{$riskInfo['position_id']}";

                if (!$this->cacheRedis->exists($lockKey)) {
                    // 推送到强平队列（队列只负责执行强平，不计算风险）
                    $liquidationQueue->push(new LiquidationJob($riskInfo));

                    // 设置防重复锁（5分钟）
                    $this->cacheRedis->setex($lockKey, 300, 1);

                    $this->logger->warning('触发强平', [
                        'position_id' => $riskInfo['position_id'],
                        'user_id' => $riskInfo['user_id'],
                        'margin_ratio' => $riskInfo['margin_ratio'],
                        'liquidation_price' => $riskInfo['liquidation_price']
                    ]);
                }

            } catch (\Exception $e) {
                $this->logger->error('推送强平任务失败', [
                    'position_id' => $riskInfo['position_id'],
                    'error' => $e->getMessage()
                ]);
            }
        }
    }


    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER',false);
    }
}