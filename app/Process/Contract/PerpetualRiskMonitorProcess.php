<?php

declare(strict_types=1);

namespace App\Process\Contract;

use App\Enum\Contract\PerpetualPositionCacheKey;
use App\Enum\CurrencyConfigKey;
use App\Enum\User\UserAssetsCacheKey;
use App\Enum\MarketData\TradeSubscribeKey;
use App\Enum\MarketData\TickerSyncKey;
use App\Enum\MarketType;
use App\Enum\AsyncExecutorKey;
use App\Model\Enums\User\AccountType;
use App\Job\Contract\PerpetualLiquidationJob;
use App\Service\RedisFactory\CacheRedis;
use App\Service\RedisFactory\MarketRedis;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\AbstractProcess;
use Hyperf\Process\Annotation\Process;
use Hyperf\Redis\Redis;
use Hyperf\Context\ApplicationContext;
use Swoole\Coroutine;
use Psr\Log\LoggerInterface;

#[Process(
    name: 'perpetual-risk-monitor',
    nums: 1,
    redirectStdinStdout: false,
    pipeType: 2,
    enableCoroutine: true
)]
class PerpetualRiskMonitorProcess extends AbstractProcess
{
    #[Inject]
    protected MarketRedis $redis;

    #[Inject]
    protected CacheRedis $cacheRedis;

    protected LoggerInterface $logger;

    // Swoole Channel队列
    protected \Swoole\Coroutine\Channel $priceChannel;
    protected int $channelCapacity = 5000;

    // 统计信息
    protected int $receivedCount = 0;
    protected int $processedCount = 0;
    protected int $droppedCount = 0;

    // 价格缓存（币种ID => 最新价格）
    protected array $priceCache = [];

    // 币种映射缓存（基础币种ID => 计价币种ID）
    protected array $currencyMappingCache = [];

    // 价格变化阈值配置
    protected array $priceThresholds = [
        'default' => 0.005,        // 默认0.5%变化触发
        'high_volatility' => 0.01, // 高波动币种1%触发
        'stable_coins' => 0.005,   // 稳定币0.5%触发
    ];

    public function handle(): void
    {
        $container = ApplicationContext::getContainer();
        $this->logger = logger('合约风险监控','perptual/perptual-monitor.log');

        // 初始化Channel队列
        $this->priceChannel = new \Swoole\Coroutine\Channel($this->channelCapacity);

        $this->logger->info('永续合约风险监控进程启动', [
            'channel_capacity' => $this->channelCapacity
        ]);

        // 启动价格数据处理协程
        Coroutine::create(function () {
            $this->processPriceData();
        });

        // 订阅价格变化消息
        $this->subscribeToPriceMessages();
    }

    /**
     * 订阅价格变化消息
     */
    protected function subscribeToPriceMessages(): void
    {
        // 订阅合约交易数据
        $channels = [
            TradeSubscribeKey::getCryptoTradeChannelKey(MarketType::MARGIN->value)
        ];

        $this->logger->info('开始订阅价格频道', ['channels' => $channels]);

        while (true) {
            try {
                $this->redis->subscribe($channels, function ($_, $channel, $message) {
                    $this->receivedCount++;

                    $currentTime = microtime(true);
                    $priceData = [
                        'channel' => $channel,
                        'message' => $message,
                        'timestamp' => $currentTime
                    ];

                    // 队列满时丢弃老数据
                    if ($this->priceChannel->isFull()) {
                        $discardCount = 0;
                        $maxDiscard = min(50, $this->priceChannel->length() / 2);

                        while ($discardCount < $maxDiscard && !$this->priceChannel->isEmpty()) {
                            $oldData = $this->priceChannel->pop(0.001);
                            if ($oldData !== false) {
                                $discardCount++;
                                $this->droppedCount++;
                            } else {
                                break;
                            }
                        }
                    }

                    // 写入新数据
                    if (!$this->priceChannel->push($priceData, 0.001)) {
                        $this->droppedCount++;
                    }
                });
            } catch (\Exception $e) {
                $this->logger->error('价格订阅异常', [
                    'error' => $e->getMessage()
                ]);
                sleep(2);
            }
        }
    }

    /**
     * 处理价格数据的协程
     */
    protected function processPriceData(): void
    {
        $this->logger->info('价格数据处理协程启动');

        while (true) {
            try {
                $data = $this->priceChannel->pop(0.1);

                if ($data === false) {
                    continue;
                }

                $this->processedCount++;

                // 处理价格消息
                $this->handlePriceMessage($data['channel'], $data['message']);
            } catch (\Exception $e) {
                $this->logger->error('处理价格数据异常', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                Coroutine::sleep(0.1);
            }
        }
    }

    /**
     * 处理价格消息
     */
    protected function handlePriceMessage(string $channel, string $message): void
    {
        try {
            $tradeData = json_decode($message, true);

            if (!$tradeData || !isset($tradeData['currency_id'], $tradeData['price'])) {
                return;
            }

            $currencyId = (int)$tradeData['currency_id'];
            $currentPrice = (float)$tradeData['price'];

            // 更新价格缓存
            $this->priceCache[$currencyId] = $currentPrice;

            // 检查是否需要触发风险计算
            if ($this->shouldTriggerRiskCalculation($currencyId, $currentPrice)) {
                $this->calculateAndProcessRisk($currencyId, $currentPrice);
            }

        } catch (\Exception $e) {
            $this->logger->error('处理价格消息异常', [
                'channel' => $channel,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 判断是否需要触发风险计算
     */
    protected function shouldTriggerRiskCalculation(int $currencyId, float $currentPrice): bool
    {
        $currencyPositionsKey = PerpetualPositionCacheKey::getCurrencyPositionsKey($currencyId);
        $positionCount = $this->cacheRedis->hLen($currencyPositionsKey);

        if ($positionCount === 0) {
            return false;
        }

        // 获取价格变化阈值
        $threshold = $this->priceThresholds['default'];

        // 获取最后计算价格
        $lastPrice = $this->getLastCalculatedPrice($currencyId);

        if ($lastPrice === 0.0) {
            return true;
        }

        // 计算价格变化幅度
        $priceChange = abs($currentPrice - $lastPrice) / $lastPrice;

        return $priceChange >= $threshold;
    }

    /**
     * 获取最后计算的价格
     */
    protected function getLastCalculatedPrice(int $currencyId): float
    {
        $key = PerpetualPositionCacheKey::getCurrencyRiskKey($currencyId);
        $lastPrice = $this->cacheRedis->hGet($key, 'last_calculated_price');

        return $lastPrice ? (float)$lastPrice : 0.0;
    }

    /**
     * 计算并处理风险
     */
    protected function calculateAndProcessRisk(int $currencyId, float $currentPrice): void
    {
        try {
            // 更新最后计算价格
            $this->updateLastCalculatedPrice($currencyId, $currentPrice);

            // 获取该币种的所有仓位
            $positionIds = $this->getCurrencyPositions($currencyId);

            if (empty($positionIds)) {
                return;
            }

            // 批量获取仓位数据
            $positionsData = $this->batchGetPositionsData($positionIds);

            if (empty($positionsData)) {
                return;
            }

            // 批量计算风险
            $riskPositions = $this->batchCalculatePositionRisk($positionsData, $currentPrice);

            // 处理需要强平的仓位
            if (!empty($riskPositions)) {
                $this->processLiquidationPositions($riskPositions);
            }

        } catch (\Exception $e) {
            $this->logger->error('计算处理风险异常', [
                'currency_id' => $currencyId,
                'current_price' => $currentPrice,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 批量获取仓位数据
     */
    protected function batchGetPositionsData(array $positionIds): array
    {
        if (empty($positionIds)) {
            return [];
        }

        try {
            // 使用pipeline批量查询仓位详情
            $pipeline = $this->cacheRedis->pipeline();

            foreach ($positionIds as $positionId) {
                $positionDetailKey = PerpetualPositionCacheKey::getPositionDetailKey((int)$positionId);
                $pipeline->hGetAll($positionDetailKey);
            }

            $results = $pipeline->exec();

            $positionsData = [];
            foreach ($results as $index => $positionData) {
                if (!empty($positionData)) {
                    $positionData['position_id'] = $positionIds[$index];
                    $positionsData[] = $positionData;
                }
            }

            $this->logger->debug('批量获取仓位数据', [
                'requested_count' => count($positionIds),
                'retrieved_count' => count($positionsData)
            ]);

            return $positionsData;

        } catch (\Exception $e) {
            $this->logger->error('批量获取仓位数据失败', [
                'position_ids' => $positionIds,
                'error' => $e->getMessage()
            ]);

            // 降级：逐个查询
            return $this->fallbackGetPositionsData($positionIds);
        }
    }

    /**
     * 降级方案：逐个获取仓位数据
     */
    protected function fallbackGetPositionsData(array $positionIds): array
    {
        $positionsData = [];

        foreach ($positionIds as $positionId) {
            try {
                $positionDetailKey = PerpetualPositionCacheKey::getPositionDetailKey((int)$positionId);
                $positionData = $this->cacheRedis->hGetAll($positionDetailKey);

                if (!empty($positionData)) {
                    $positionData['position_id'] = $positionId;
                    $positionsData[] = $positionData;
                }
            } catch (\Exception $e) {
                $this->logger->error('获取单个仓位数据失败', [
                    'position_id' => $positionId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $positionsData;
    }

    /**
     * 批量计算仓位风险
     */
    protected function batchCalculatePositionRisk(array $positionsData, float $currentPrice): array
    {
        $riskPositions = [];

        // 按保证金模式分组处理
        $crossPositions = [];  // 全仓仓位
        $isolatedPositions = []; // 逐仓仓位
        $userCurrencyPairs = [];

        // 分组仓位数据
        foreach ($positionsData as $positionData) {
            $userId = (int)$positionData['user_id'];
            $currencyId = (int)$positionData['currency_id'];
            $marginMode = (int)$positionData['margin_mode'];

            $userKey = "{$userId}_{$currencyId}_{$marginMode}";
            if (!isset($userCurrencyPairs[$userKey])) {
                $userCurrencyPairs[$userKey] = [
                    'user_id' => $userId,
                    'currency_id' => $currencyId,
                    'margin_mode' => $marginMode
                ];
            }

            if ($marginMode === 1) {
                // 全仓模式：按用户-币种分组
                $crossKey = "{$userId}_{$currencyId}";
                if (!isset($crossPositions[$crossKey])) {
                    $crossPositions[$crossKey] = [];
                }
                $crossPositions[$crossKey][] = $positionData;
            } else {
                // 逐仓模式：单独处理
                $isolatedPositions[] = $positionData;
            }
        }

        // 批量获取维持保证金率
        $maintenanceMarginRates = $this->batchGetMaintenanceMarginRates($userCurrencyPairs);

        // 处理逐仓仓位（单独计算）
        foreach ($isolatedPositions as $positionData) {
            try {
                $riskData = $this->calculateIsolatedPositionRisk($positionData, $currentPrice, $maintenanceMarginRates);

                if ($riskData && $riskData['need_liquidation']) {
                    $riskPositions[] = $riskData;
                }
            } catch (\Exception $e) {
                $this->logger->error('计算逐仓仓位风险失败', [
                    'position_id' => $positionData['position_id'] ?? 'unknown',
                    'error' => $e->getMessage()
                ]);
            }
        }

        // 处理全仓仓位（按用户-币种组合计算）
        foreach ($crossPositions as $crossKey => $userPositions) {
            try {
                $crossRiskData = $this->calculateCrossPositionRisk($userPositions, $currentPrice, $maintenanceMarginRates);

                if (!empty($crossRiskData)) {
                    $riskPositions = array_merge($riskPositions, $crossRiskData);
                }
            } catch (\Exception $e) {
                $this->logger->error('计算全仓仓位风险失败', [
                    'cross_key' => $crossKey,
                    'positions_count' => count($userPositions),
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->logger->debug('批量风险计算完成', [
            'total_positions' => count($positionsData),
            'isolated_positions' => count($isolatedPositions),
            'cross_position_groups' => count($crossPositions),
            'risk_positions' => count($riskPositions)
        ]);

        return $riskPositions;
    }



    /**
     * 获取当前标记价格
     */
    protected function getCurrentMarkPrice(int $currencyId): ?float
    {
        try {
            // 从市场数据获取最新价格作为标记价格
            $priceData = $this->cacheRedis->hGet(TickerSyncKey::getOuterTradeKey($currencyId, 1), 'price');
            
            if ($priceData && $priceData > 0) {
                return (float)$priceData;
            }

            return null;

        } catch (\Throwable $e) {
            $this->logger->error('获取标记价格失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 计算未实现盈亏
     */
    protected function calculateUnrealizedPnl(float $quantity, float $entryPrice, float $currentPrice, string $side): float
    {
        $priceDiff = bcsub((string)$currentPrice, (string)$entryPrice, 8);
        
        if ($side === 'long') {
            // 多头：(当前价格 - 开仓价格) × 数量
            return (float)bcmul((string)$quantity, $priceDiff, 8);
        } else {
            // 空头：(开仓价格 - 当前价格) × 数量
            return (float)bcmul((string)$quantity, bcmul($priceDiff, '-1', 8), 8);
        }
    }

    /**
     * 获取维持保证金率
     */
    protected function getMaintenanceMarginRate(int $userId, int $currencyId, int $marginMode): float
    {
        try {
            $userRiskKey = PerpetualPositionCacheKey::getUserRiskKey($userId);
            $fieldKey = "margin_rate_{$currencyId}_{$marginMode}";
            
            $cachedRate = $this->cacheRedis->hGet($userRiskKey, $fieldKey);
            
            if ($cachedRate !== false) {
                return (float)$cachedRate;
            }

            // 缓存不存在，返回默认维持保证金率 0.5%
            return 0.005;

        } catch (\Throwable $e) {
            $this->logger->error('获取维持保证金率失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'margin_mode' => $marginMode,
                'error' => $e->getMessage()
            ]);
            return 0.005;
        }
    }

    /**
     * 处理需要强平的仓位
     */
    protected function processLiquidationPositions(array $riskPositions): void
    {
        $this->logger->warning('发现需要强平的仓位', ['count' => count($riskPositions)]);

        foreach ($riskPositions as $riskData) {
            try {
                $this->submitLiquidationJob($riskData);
            } catch (\Throwable $e) {
                $this->logger->error('提交强平任务失败', [
                    'position_id' => $riskData['position_id'],
                    'user_id' => $riskData['user_id'],
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * 提交强平任务到异步队列
     */
    protected function submitLiquidationJob(array $riskData): void
    {
        $positionId = $riskData['position_id'];
        $userId = $riskData['user_id'];

        // 检查强平锁定，避免重复强平
        $lockKey = PerpetualPositionCacheKey::getLiquidationLockKey($positionId);
        $lockResult = $this->cacheRedis->set($lockKey, time(), ['NX', 'EX' => 30]); // 5分钟锁定

        if (!$lockResult) {
            // echo "⏸️  仓位ID: {$positionId} 已在强平处理中，跳过重复提交\n";
            $this->logger->debug('仓位已在强平处理中', ['position_id' => $positionId]);
            return;
        }

        echo "🔥 提交强平任务 - 仓位ID: {$positionId}, 用户ID: {$userId}, 保证金率: {$riskData['margin_ratio']}\n";

        $this->logger->warning('提交强平任务', [
            'position_id' => $positionId,
            'user_id' => $userId,
            'margin_ratio' => $riskData['margin_ratio'],
            'maintenance_margin_rate' => $riskData['maintenance_margin_rate']
        ]);

        try {
            // 获取仓位数据，传递给强平任务避免重复查询
            $positionData = $this->getPositionDataFromCache($positionId) ?? $this->getPositionDataFromRisk($riskData);

            // 创建强平任务并提交到异步队列
            $liquidationJob = new PerpetualLiquidationJob($userId, $positionId, $riskData, $positionData);

            // 提交到异步队列
            pushAsyncJob(AsyncExecutorKey::PERPETUAL_CONTRACT_QUEUE->value, $liquidationJob, 0);

            $this->logger->info('强平任务提交成功', [
                'position_id' => $positionId,
                'user_id' => $userId
            ]);

            // 更新风险统计
            $this->updateRiskStatistics($riskData);

        } catch (\Throwable $e) {
            // 提交失败时释放锁定
            $this->cacheRedis->del($lockKey);

            $this->logger->error('提交强平任务异常', [
                'position_id' => $positionId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }



    /**
     * 更新风险统计
     */
    protected function updateRiskStatistics(array $riskData): void
    {
        try {
            $statsKey = PerpetualPositionCacheKey::getGlobalStatsKey();

            $this->cacheRedis->hIncrBy($statsKey, 'total_liquidations', 1);
            $this->cacheRedis->hSet($statsKey, 'last_liquidation_time', time());

            $currencyStatsKey = PerpetualPositionCacheKey::getCurrencyRiskKey($riskData['currency_id']);
            $this->cacheRedis->hIncrBy($currencyStatsKey, 'liquidation_count', 1);
            $this->cacheRedis->hSet($currencyStatsKey, 'last_liquidation_time', time());

        } catch (\Throwable $e) {
            $this->logger->error('更新风险统计失败', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取计价币种ID（带缓存）
     */
    protected function getQuoteCurrencyId(int $baseCurrencyId): int
    {
        // 先检查缓存
        if (isset($this->currencyMappingCache[$baseCurrencyId])) {
            return $this->currencyMappingCache[$baseCurrencyId];
        }

        try {
            $currencyKey = CurrencyConfigKey::getCurrencyKey($baseCurrencyId); //"currency:crypto:{$baseCurrencyId}";
            $quoteCurrencyId = $this->cacheRedis->hGet($currencyKey, 'quote_assets_id');

            if (!$quoteCurrencyId) {
                // echo "  ⚠️  基础币种ID: {$baseCurrencyId} 未找到计价币种，使用默认USDT(ID:2)\n";
                $this->logger->warning('无法获取计价币种ID，使用默认USDT', [
                    'base_currency_id' => $baseCurrencyId
                ]);
                $quoteCurrencyId = 2; // 默认USDT
            } else {
                $quoteCurrencyId = (int)$quoteCurrencyId;
                // echo "  🔍 首次币种映射 - 基础币种ID: {$baseCurrencyId} -> 计价币种ID: {$quoteCurrencyId} (已缓存)\n";
            }

            // 缓存映射关系
            $this->currencyMappingCache[$baseCurrencyId] = $quoteCurrencyId;

            return $quoteCurrencyId;

        } catch (\Throwable $e) {
            echo "  ❌ 获取计价币种ID失败: " . $e->getMessage() . "\n";
            $this->logger->error('获取计价币种ID失败', [
                'base_currency_id' => $baseCurrencyId,
                'error' => $e->getMessage()
            ]);

            // 缓存默认值
            $this->currencyMappingCache[$baseCurrencyId] = 2;
            return 2; // 默认USDT
        }
    }

    /**
     * 获取用户账户余额
     */
    protected function getUserAccountBalance(int $userId, int $baseCurrencyId): array
    {
        try {
            // 获取计价币种ID
            $quoteCurrencyId = $this->getQuoteCurrencyId($baseCurrencyId);

            $assetsKey = UserAssetsCacheKey::getFuturesAssetsKey($userId, AccountType::FUTURES->value);

            $available = $this->cacheRedis->hGet($assetsKey, (string)$quoteCurrencyId) ?: '0';
            $frozen = $this->cacheRedis->hGet($assetsKey, "{$quoteCurrencyId}-frozen") ?: '0';

            return [
                'available' => (float)$available,
                'frozen' => (float)$frozen,
                'total' => (float)bcadd($available, $frozen, 8),
                'quote_currency_id' => $quoteCurrencyId
            ];

        } catch (\Throwable $e) {
            $this->logger->error('获取用户账户余额失败', [
                'user_id' => $userId,
                'base_currency_id' => $baseCurrencyId,
                'error' => $e->getMessage()
            ]);

            return [
                'available' => 0.0,
                'frozen' => 0.0,
                'total' => 0.0,
                'quote_currency_id' => 2
            ];
        }
    }

    /**
     * 更新最后计算价格
     */
    protected function updateLastCalculatedPrice(int $currencyId, float $price): void
    {
        $key = PerpetualPositionCacheKey::getCurrencyRiskKey($currencyId);
        $this->cacheRedis->hMSet($key, [
            'last_calculated_price' => (string)$price,
            'last_calculated_time' => (string)time()
        ]);
    }

    /**
     * 获取币种的所有仓位ID
     */
    protected function getCurrencyPositions(int $currencyId): array
    {
        $currencyPositionsKey = PerpetualPositionCacheKey::getCurrencyPositionsKey($currencyId);
        return $this->cacheRedis->hVals($currencyPositionsKey);
    }

    /**
     * 批量获取维持保证金率
     */
    protected function batchGetMaintenanceMarginRates(array $userCurrencyPairs): array
    {
        $maintenanceMarginRates = [];

        try {
            // 使用pipeline批量查询维持保证金率
            $pipeline = $this->cacheRedis->pipeline();
            $queryKeys = [];

            foreach ($userCurrencyPairs as $key => $pair) {
                $userRiskKey = PerpetualPositionCacheKey::getUserRiskKey($pair['user_id']);
                $fieldKey = "margin_rate_{$pair['currency_id']}_{$pair['margin_mode']}";

                $pipeline->hGet($userRiskKey, $fieldKey);
                $queryKeys[] = $key;
            }

            $results = $pipeline->exec();

            foreach ($results as $index => $rate) {
                $key = $queryKeys[$index];
                if ($rate !== false && $rate !== null) {
                    $maintenanceMarginRates[$key] = (float)$rate;
                } else {
                    // 使用默认维持保证金率
                    $maintenanceMarginRates[$key] = 0.005;
                }
            }

        } catch (\Exception $e) {
            $this->logger->error('批量获取维持保证金率失败', [
                'error' => $e->getMessage()
            ]);

            // 降级：使用默认值
            foreach ($userCurrencyPairs as $key => $pair) {
                $maintenanceMarginRates[$key] = 0.005;
            }
        }

        return $maintenanceMarginRates;
    }

    /**
     * 计算逐仓仓位风险
     * 逐仓模式：只使用该仓位冻结的保证金计算风险率
     */
    protected function calculateIsolatedPositionRisk(array $positionData, float $currentPrice, array $maintenanceMarginRates): ?array
    {
        $userId = (int)$positionData['user_id'];
        $currencyId = (int)$positionData['currency_id'];
        $marginMode = (int)$positionData['margin_mode'];
        $positionId = (int)$positionData['position_id'];
        $quantity = (float)$positionData['quantity'];
        $entryPrice = (float)$positionData['entry_price'];
        $marginAmount = (float)$positionData['margin_amount']; // 仓位冻结的保证金
        $side = $positionData['side'];

        // 获取维持保证金率
        $userKey = "{$userId}_{$currencyId}_{$marginMode}";
        $maintenanceMarginRate = $maintenanceMarginRates[$userKey] ?? 0.005;

        // 计算未实现盈亏
        $unrealizedPnl = $this->calculateUnrealizedPnl($quantity, $entryPrice, $currentPrice, $side);

        // 计算名义价值
        $notionalValue = bcmul((string)$quantity, (string)$currentPrice, 8);

        // 计算维持保证金要求
        $maintenanceMargin = bcmul($notionalValue, (string)$maintenanceMarginRate, 8);

        // 逐仓保证金余额 = 仓位冻结保证金 + 未实现盈亏
        $marginBalance = bcadd((string)$marginAmount, (string)$unrealizedPnl, 8);

        // 计算保证金率 = 保证金余额 / 名义价值
        $marginRatio = bcdiv($marginBalance, $notionalValue, 8);

        // 判断是否需要强平：保证金率 <= 维持保证金率
        $needLiquidation = bccomp($marginRatio, (string)$maintenanceMarginRate, 8) <= 0;

        // 输出逐仓计算数据
        // echo "\n� 逐仓风险计算 - 仓位ID: {$positionId}\n";
        // echo "  用户ID: {$userId}, 币种ID: {$currencyId}, 方向: " . ($side == 1 ? '多头' : '空头') . "\n";
        // echo "  开仓价: {$entryPrice}, 当前价: {$currentPrice}, 数量: {$quantity}\n";
        // echo "  保证金: {$marginAmount}, 未实现盈亏: {$unrealizedPnl}\n";
        // echo "  名义价值: {$notionalValue}, 维持保证金要求: {$maintenanceMargin}\n";
        // echo "  保证金余额: {$marginBalance}, 保证金率: {$marginRatio}\n";
        // echo "  维持保证金率: {$maintenanceMarginRate}, 强平价格: {$liquidationPrice}\n";

        if ($needLiquidation) {
            // 计算强平价格
            $liquidationPrice = $this->calculateIsolatedLiquidationPrice($quantity, $entryPrice, $marginAmount, $maintenanceMarginRate, (int)$side);
            echo "  🚨 逐仓强平触发! 仓位ID: {$positionId}, 保证金率: {$marginRatio}, 强平价格: {$liquidationPrice}\n";
        }
        // else {
        //     echo "  ✅ 安全 - 保证金率({$marginRatio}) > 维持保证金率({$maintenanceMarginRate})\n";
        // }

        return [
            'position_id' => $positionId,
            'user_id' => $userId,
            'currency_id' => $currencyId,
            'margin_mode' => $marginMode,
            'margin_type' => 'isolated',
            'current_price' => $currentPrice,
            'unrealized_pnl' => (float)$unrealizedPnl,
            'margin_balance' => $marginBalance,
            'margin_ratio' => (float)$marginRatio,
            'maintenance_margin_rate' => $maintenanceMarginRate,
            'maintenance_margin' => (float)$maintenanceMargin,
            'isolated_margin_amount' => (float)$marginAmount,
            'need_liquidation' => $needLiquidation,
        ];
    }

    /**
     * 计算全仓仓位风险
     * 全仓模式：使用账户所有余额作为保证金计算风险率
     */
    protected function calculateCrossPositionRisk(array $userPositions, float $currentPrice, array $maintenanceMarginRates): array
    {
        if (empty($userPositions)) {
            return [];
        }

        $riskPositions = [];
        $firstPosition = $userPositions[0];
        $userId = (int)$firstPosition['user_id'];
        $currencyId = (int)$firstPosition['currency_id'];
        $marginMode = 1; // 全仓模式

        try {
            // 1. 获取用户账户所有余额（全仓模式使用账户所有余额作为保证金）
            $accountBalance = $this->getUserAccountBalance($userId, $currencyId);
            $totalAccountBalance = bcadd((string)$accountBalance['available'], (string)$accountBalance['frozen'], 8);
            $quoteCurrencyId = $accountBalance['quote_currency_id'];

            // echo "\n🔍 全仓风险计算 - 用户ID: {$userId}\n";
            // echo "  基础币种ID: {$currencyId}, 计价币种ID: {$quoteCurrencyId}\n";
            // echo "  计价币种可用余额: {$accountBalance['available']}\n";
            // echo "  计价币种冻结余额: {$accountBalance['frozen']}\n";
            // echo "  计价币种总余额: {$totalAccountBalance}\n";

            // 2. 计算用户在该币种的总体风险指标（全仓按净敞口计算）
            $totalUnrealizedPnl = 0;
            $longQuantity = 0;  // 多头总数量
            $shortQuantity = 0; // 空头总数量

            // 获取维持保证金率
            $userKey = "{$userId}_{$currencyId}_{$marginMode}";
            $maintenanceMarginRate = $maintenanceMarginRates[$userKey] ?? 0.005;
            // echo "  维持保证金率: {$maintenanceMarginRate}\n";

            // 计算所有仓位的汇总数据
            // echo "  仓位详情:\n";
            foreach ($userPositions as $positionData) {
                $positionId = (int)$positionData['position_id'];
                $quantity = (float)$positionData['quantity'];
                $entryPrice = (float)$positionData['entry_price'];
                $side = $positionData['side'];

                // 按方向累计数量
                if ($side == 1) {
                    $longQuantity = bcadd((string)$longQuantity, (string)$quantity, 8);
                } else {
                    $shortQuantity = bcadd((string)$shortQuantity, (string)$quantity, 8);
                }

                // echo "    仓位ID: {$positionId}, 方向: " . ($side == 1 ? '多头' : '空头') . ", 数量: {$quantity}\n";
                // echo "    开仓价: {$entryPrice}, 当前价: {$currentPrice}\n";
            }

            // 提前检测净敞口
            $netDifference = bcsub((string)$longQuantity, (string)$shortQuantity, 8);
            $netExposure = bccomp($netDifference, '0', 8) >= 0 ? $netDifference : bcmul($netDifference, '-1', 8);

            // echo "  净敞口计算:\n";
            // echo "    多头总数量: {$longQuantity}\n";
            // echo "    空头总数量: {$shortQuantity}\n";
            // echo "    净敞口数量: {$netExposure}\n";

            // 如果净敞口为0，直接跳过
            if (bccomp($netExposure, '0', 8) == 0) {
                // echo "  ✅ 全仓安全 - 净敞口为0，完全对冲\n";
                return [];
            }

            // 有净敞口，继续计算未实现盈亏和其他指标
            // echo "  继续计算未实现盈亏和风险指标...\n";
            foreach ($userPositions as $positionData) {
                $quantity = (float)$positionData['quantity'];
                $entryPrice = (float)$positionData['entry_price'];
                $side = $positionData['side'];

                // 计算未实现盈亏
                $unrealizedPnl = $this->calculateUnrealizedPnl($quantity, $entryPrice, $currentPrice, $side);
                $totalUnrealizedPnl = bcadd((string)$totalUnrealizedPnl, (string)$unrealizedPnl, 8);
            }

            $totalNotionalValue = bcmul($netExposure, (string)$currentPrice, 8);
            $totalMaintenanceMargin = bcmul($totalNotionalValue, (string)$maintenanceMarginRate, 8);

            // 3. 全仓保证金余额 = 账户所有余额 + 未实现盈亏
            $totalMarginBalance = bcadd($totalAccountBalance, (string)$totalUnrealizedPnl, 8);

            // echo "  汇总数据:\n";
            // echo "    总未实现盈亏: {$totalUnrealizedPnl}\n";
            // echo "    净敞口名义价值: {$totalNotionalValue}\n";
            // echo "    净敞口维持保证金要求: {$totalMaintenanceMargin}\n";
            // echo "    总保证金余额: {$totalMarginBalance}\n";

            // 4. 计算保证金率和强平判断（此时净敞口必定大于0）
            $totalMarginRatio = bcdiv($totalMarginBalance, (string)$totalNotionalValue, 8);
            // echo "    总保证金率: {$totalMarginRatio}\n";

            // 判断是否需要强平
            $needLiquidation = bccomp($totalMarginRatio, (string)$maintenanceMarginRate, 8) <= 0;

            if ($needLiquidation) {
                echo "  🚨 全仓强平触发! 用户ID: {$userId}, 总保证金率: {$totalMarginRatio}, 维持保证金率: {$maintenanceMarginRate}\n";
            }
            // else {
            //     echo "  ✅ 全仓安全 - 总保证金率({$totalMarginRatio}) > 维持保证金率({$maintenanceMarginRate})\n";
            // }

            // 4. 如果需要强平，返回所有仓位（全仓模式下需要全部强平）
            if ($needLiquidation) {
                foreach ($userPositions as $positionData) {
                    $positionId = (int)$positionData['position_id'];
                    $quantity = (float)$positionData['quantity'];
                    $entryPrice = (float)$positionData['entry_price'];
                    $marginAmount = (float)$positionData['margin_amount'];
                    $side = $positionData['side'];

                    // 计算单个仓位的未实现盈亏
                    $unrealizedPnl = $this->calculateUnrealizedPnl($quantity, $entryPrice, $currentPrice, $side);
                    $marginBalance = bcadd((string)$marginAmount, (string)$unrealizedPnl, 8);

                    $riskPositions[] = [
                        'position_id' => $positionId,
                        'user_id' => $userId,
                        'currency_id' => $currencyId,
                        'margin_mode' => $marginMode,
                        'current_price' => $currentPrice,
                        'unrealized_pnl' => (float)$unrealizedPnl,
                        'margin_balance' => $marginBalance,
                        'margin_ratio' => (float)$totalMarginRatio, // 使用总体保证金率
                        'maintenance_margin_rate' => $maintenanceMarginRate,
                        'maintenance_margin' => (float)$totalMaintenanceMargin,
                        'need_liquidation' => true,
                        'cross_margin_info' => [
                            'total_account_balance' => (float)$totalAccountBalance,
                            'total_unrealized_pnl' => (float)$totalUnrealizedPnl,
                            'total_notional_value' => (float)$totalNotionalValue,
                            'total_positions_count' => count($userPositions),
                            'need_cancel_orders' => true // 全仓强平需要撤销所有挂单
                        ]
                    ];
                }

                $this->logger->warning('全仓仓位触发强平', [
                    'user_id' => $userId,
                    'currency_id' => $currencyId,
                    'total_margin_ratio' => (float)$totalMarginRatio,
                    'maintenance_margin_rate' => $maintenanceMarginRate,
                    'positions_count' => count($userPositions),
                    'total_notional_value' => (float)$totalNotionalValue
                ]);
            }

        } catch (\Exception $e) {
            $this->logger->error('计算全仓风险异常', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'positions_count' => count($userPositions),
                'error' => $e->getMessage()
            ]);
        }

        return $riskPositions;
    }

    /**
     * 检查进程健康状态
     */
    protected function checkProcessHealth(): void
    {
        $healthKey = 'perpetual:risk_monitor:health';
        $this->cacheRedis->setEx($healthKey, 60, json_encode([
            'last_check_time' => time(),
            'process_id' => getmypid(),
            'memory_usage' => memory_get_usage(true),
            'received_count' => $this->receivedCount,
            'processed_count' => $this->processedCount,
            'dropped_count' => $this->droppedCount,
            'price_cache_size' => count($this->priceCache),
            'status' => 'running'
        ]));
    }

    /**
     * 从缓存获取仓位数据
     */
    protected function getPositionDataFromCache(int $positionId): ?array
    {
        try {
            $positionKey = PerpetualPositionCacheKey::getPositionDetailKey($positionId);
            $positionData = $this->cacheRedis->hGetAll($positionKey);

            return !empty($positionData) ? $positionData : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 计算逐仓强平价格
     */
    protected function calculateIsolatedLiquidationPrice(float $quantity, float $entryPrice, float $marginAmount, float $maintenanceMarginRate, int $side): string
    {
        try {
            // 逐仓强平价格公式：
            // 强平时：保证金余额 = 名义价值 × 维持保证金率
            // 即：保证金 + 未实现盈亏 = 强平价格 × 数量 × 维持保证金率

            if ($side == 1) {
                // 多头：未实现盈亏 = (强平价格 - 开仓价) × 数量
                // 保证金 + (强平价格 - 开仓价) × 数量 = 强平价格 × 数量 × 维持保证金率
                // 强平价格 = (保证金 - 开仓价 × 数量) / (数量 × (维持保证金率 - 1))

                $marginStr = (string)$marginAmount;
                $entryPriceStr = (string)$entryPrice;
                $quantityStr = (string)$quantity;
                $maintenanceMarginRateStr = (string)$maintenanceMarginRate;

                $numerator = bcsub($marginStr, bcmul($entryPriceStr, $quantityStr, 8), 8);
                $denominator = bcmul($quantityStr, bcsub($maintenanceMarginRateStr, '1', 8), 8);

                $liquidationPrice = bcdiv($numerator, $denominator, 8);
            } else {
                // 空头：未实现盈亏 = (开仓价 - 强平价格) × 数量
                // 保证金 + (开仓价 - 强平价格) × 数量 = 强平价格 × 数量 × 维持保证金率
                // 强平价格 = (保证金 + 开仓价 × 数量) / (数量 × (维持保证金率 + 1))

                $marginStr = (string)$marginAmount;
                $entryPriceStr = (string)$entryPrice;
                $quantityStr = (string)$quantity;
                $maintenanceMarginRateStr = (string)$maintenanceMarginRate;

                $numerator = bcadd($marginStr, bcmul($entryPriceStr, $quantityStr, 8), 8);
                $denominator = bcmul($quantityStr, bcadd($maintenanceMarginRateStr, '1', 8), 8);

                $liquidationPrice = bcdiv($numerator, $denominator, 8);
            }

            return $liquidationPrice;

        } catch (\Exception $e) {
            return '0';
        }
    }

    /**
     * 从风险数据中提取仓位信息
     */
    protected function getPositionDataFromRisk(array $riskData): ?array
    {
        // 从风险计算数据中提取仓位基本信息
        return [
            'id' => $riskData['position_id'] ?? 0,
            'user_id' => $riskData['user_id'] ?? 0,
            'currency_id' => $riskData['currency_id'] ?? 0,
            'margin_mode' => $riskData['margin_mode'] ?? 0,
            'side' => $riskData['side'] ?? 0,
            'quantity' => $riskData['quantity'] ?? 0,
            'available_quantity' => $riskData['available_quantity'] ?? 0,
            'entry_price' => $riskData['entry_price'] ?? 0,
            'margin_amount' => $riskData['margin_amount'] ?? 0,
            'status' => 1, // 持仓状态
        ];
    }
}
