<?php

declare(strict_types=1);
/**
 * 永续合约委托单监控进程
 */

namespace App\Process\Contract;

use App\Process\BaseProcess;
use App\Model\Trade\TradePerpetualConditionalOrder;
use App\Model\Enums\Trade\Perpetual\ConditionalOrderStatus;
use App\Model\Enums\Trade\Perpetual\TriggerCondition;
use App\Enum\Contract\PerpetualConditionalRedisKey;
use App\Enum\MarketData\TradeSubscribeKey;
use App\Enum\MarketType;
use App\Job\Contract\PerpetualConditionalOrderExecutionJob;
use App\Enum\AsyncExecutorKey;
use App\Service\RedisFactory\CacheRedis;
use App\Service\RedisFactory\MarketRedis;
use Hyperf\Process\Annotation\Process;
use Hyperf\Di\Annotation\Inject;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;
use Hyperf\Logger\LoggerFactory;
use Swoole\Coroutine;
use Swoole\Coroutine\Channel;

#[Process(name: 'PerpetualConditionalOrderMonitorProcess', enableCoroutine: true)]
class PerpetualConditionalOrderMonitorProcess extends BaseProcess
{
    #[Inject]
    public MarketRedis $redis;

    #[Inject]
    protected CacheRedis $cacheRedis;

    /**
     * 条件订单监控日志
     */
    protected LoggerInterface $conditionalLogger;

    /**
     * 价格数据处理队列
     */
    protected Channel $priceDataChannel;

    /**
     * 队列容量
     */
    protected int $channelCapacity = 10000;

    /**
     * 清理间隔时间（秒）
     */
    protected int $cleanupInterval = 3600; // 1小时

    protected int $lastCleanupTime = 0;

    public function __construct(ContainerInterface $container, LoggerFactory $loggerFactory)
    {
        parent::__construct($container);
        $this->conditionalLogger = logger('合约委托','perpetual-logs.log');//$loggerFactory->get('perpetual-conditional-order-monitor', 'perpetual-logs');

        // 初始化Channel队列
        $this->priceDataChannel = new Channel($this->channelCapacity);
    }

    public function handle(): void
    {
        $this->conditionalLogger->info('永续合约委托单监控进程启动');

        try {
            // 1. 启动价格数据处理协程
            $this->startPriceDataProcessor();

            // 2. 启动定期清理协程
            $this->startPeriodicCleanup();

            // 3. 启动价格数据订阅（主协程）
            $this->subscribeToPriceData();

            while (true) {
                Coroutine::sleep(1);
            }

        } catch (\Throwable $e) {
            $this->conditionalLogger->error('永续合约委托单监控进程启动失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER', true);
    }

    /**
     * 启动价格数据处理协程
     */
    protected function startPriceDataProcessor(): void
    {
        Coroutine::create(function () {
            $this->conditionalLogger->info('价格数据处理协程启动');

            while (true) {
                try {
                    // 从Channel队列中获取价格数据
                    $priceData = $this->priceDataChannel->pop(0.1); // 0.1秒超时

                    if ($priceData === false) {
                        // 超时，继续下一次循环
                        continue;
                    }

                    // 处理价格数据
                    $this->processPriceData($priceData);

                } catch (\Exception $e) {
                    $this->conditionalLogger->error('价格数据处理协程异常', [
                        'error' => $e->getMessage()
                    ]);

                    // 异常后短暂休眠
                    Coroutine::sleep(0.1);
                }
            }
        });
    }

    /**
     * 启动定期清理协程
     */
    protected function startPeriodicCleanup(): void
    {
        Coroutine::create(function () {
            $this->conditionalLogger->info('定期清理协程启动');

            while (true) {
                try {
                    // 每小时清理一次过期订单
                    Coroutine::sleep(3600);
                    $this->cleanupFailedOrders();

                    $this->conditionalLogger->info('定期清理失效委托订单完成');

                } catch (\Exception $e) {
                    $this->conditionalLogger->error('定期清理协程异常', [
                        'error' => $e->getMessage()
                    ]);
                }
            }
        });
    }

    /**
     * 订阅价格数据
     */
    protected function subscribeToPriceData(): void
    {
        Coroutine::create(function () {
            while (true) {
                try {
                    // 获取永续合约市场的成交数据订阅频道
                    $channel = TradeSubscribeKey::getCryptoTradeChannelKey(MarketType::MARGIN->value);

                    $this->conditionalLogger->info('开始订阅永续合约成交数据', ['channel' => $channel]);

                    // 订阅成交数据频道
                    $this->redis->subscribe([$channel], function ($redis, $channel, $message) {
                        $this->handlePriceMessage($channel, $message);
                    });

                } catch (\Exception $e) {
                    $this->conditionalLogger->error('订阅永续合约成交数据失败', [
                        'error' => $e->getMessage()
                    ]);
                }
                Coroutine::sleep(2);
            }
        });
    }

    /**
     * 处理价格数据消息（快速入队）
     */
    protected function handlePriceMessage(string $channel, string $message): void
    {
        try {
            $data = json_decode($message, true);
            if (!$data || !$this->validatePriceData($data)) {
                return;
            }

            $currencyId = (int)$data['currency_id'];
            $price = (string)$data['price'];
            $marketType = (int)$data['market_type'];

            // 快速将数据放入Channel队列
            $priceData = [
                'currency_id' => $currencyId,
                'price' => $price,
                'market_type' => $marketType,
                'timestamp' => microtime(true)
            ];

            // 非阻塞推送到队列
            if (!$this->priceDataChannel->push($priceData, 0.001)) {
                // 队列满了，移除最旧的数据，然后加入新数据
                $this->handleFullQueue($priceData, $currencyId, $price);
            }

        } catch (\Exception $e) {
            $this->conditionalLogger->error('处理价格数据消息失败', [
                'channel' => $channel,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 处理队列满的情况
     * 移除旧数据，加入新数据
     */
    protected function handleFullQueue(array $newPriceData, int $currencyId, string $price): void
    {
        try {
            // 策略1: 先尝试简单的移除一个旧数据
            $oldData = $this->priceDataChannel->pop(0.001); // 1毫秒超时

            if ($oldData !== false) {
                // 成功移除了一个旧数据，尝试加入新数据
                if ($this->priceDataChannel->push($newPriceData, 0.001)) {
                    return;
                }

                // 如果加入失败，尝试将旧数据放回去
                $this->priceDataChannel->push($oldData, 0.001);
            }

            // 策略2: 批量移除旧数据（当简单替换失败时）
            $removedData = [];
            $maxRemoveAttempts = min(20, $this->channelCapacity / 10); // 最多移除队列容量的10%

            for ($i = 0; $i < $maxRemoveAttempts; $i++) {
                $oldData = $this->priceDataChannel->pop(0.001);
                if ($oldData === false) {
                    break; // 队列为空
                }
                $removedData[] = $oldData;

                // 每移除一个数据就尝试加入新数据
                if ($this->priceDataChannel->push($newPriceData, 0.001)) {
                    // 尝试将部分重要的旧数据放回队列（保留最新的几个）
                    $keepCount = min(3, count($removedData));
                    for ($j = count($removedData) - $keepCount; $j < count($removedData); $j++) {
                        if (!$this->priceDataChannel->push($removedData[$j], 0.001)) {
                            break; // 队列又满了，停止放回
                        }
                    }
                    return;
                }
            }

            // 策略3: 如果仍然失败，尝试将移除的数据放回去
            foreach (array_reverse($removedData) as $data) {
                if (!$this->priceDataChannel->push($data, 0.001)) {
                    break; // 队列满了，停止放回
                }
            }

        } catch (\Exception $e) {
            $this->conditionalLogger->error('处理队列满异常', [
                'currency_id' => $currencyId,
                'price' => $price,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 验证价格数据格式
     */
    protected function validatePriceData(array $data): bool
    {
        $requiredFields = ['currency_id', 'market_type', 'price', 'quantity', 'trade_time'];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                $this->conditionalLogger->warning('价格数据缺少必要字段', [
                    'missing_field' => $field,
                    'data' => $data
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * 处理价格数据（异步处理）
     */
    protected function processPriceData(array $priceData): void
    {
        try {
            $currencyId = $priceData['currency_id'];
            $price = $priceData['price'];
            // 检查并触发符合条件的委托订单
            $this->checkAndTriggerOrdersByPrice($currencyId, $price);

        } catch (\Exception $e) {
            $this->conditionalLogger->error('处理价格数据失败', [
                'price_data' => $priceData,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 根据价格检查并触发委托订单
     * 监控进程只负责从Redis获取触发的订单ID，然后提交到异步队列
     * 不进行任何数据库操作，保证监控进程的高性能
     */
    protected function checkAndTriggerOrdersByPrice(int $currencyId, string $currentPrice): void
    {
        $triggeredCount = 0;

        try {
            // 防止并发处理同一币种
            $lockKey = PerpetualConditionalRedisKey::getMonitorLockKey($currencyId);
            $lockResult = $this->cacheRedis->set($lockKey, 1, ['NX', 'EX' => 2]); // 2秒锁

            if (!$lockResult) {
                return; // 获取锁失败，跳过这次处理
            }

            try {
                // 检查大于等于触发条件的订单 (价格上涨触发)
                $triggeredCount += $this->checkOrdersByCondition(
                    PerpetualConditionalRedisKey::getConditionalOrdersZsetKey($currencyId, TriggerCondition::GREATER_THAN_OR_EQUAL->value),
                    $currentPrice,
                    'gte'
                );

                // 检查小于等于触发条件的订单 (价格下跌触发)
                $triggeredCount += $this->checkOrdersByCondition(
                    PerpetualConditionalRedisKey::getConditionalOrdersZsetKey($currencyId, TriggerCondition::LESS_THAN_OR_EQUAL->value),
                    $currentPrice,
                    'lte'
                );

                if ($triggeredCount > 0) {
                    $this->conditionalLogger->info('价格触发永续合约委托订单', [
                        'currency_id' => $currencyId,
                        'current_price' => $currentPrice,
                        'triggered_count' => $triggeredCount
                    ]);
                }

            } finally {
                // 释放锁
                $this->cacheRedis->del($lockKey);
            }

        } catch (\Exception $e) {
            $this->conditionalLogger->error('检查永续合约委托订单失败', [
                'currency_id' => $currencyId,
                'current_price' => $currentPrice,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 检查指定条件的订单
     */
    protected function checkOrdersByCondition(string $redisKey, string $currentPrice, string $condition): int
    {
        $triggeredCount = 0;

        try {
            // 将当前价格转换为score格式，与Redis中存储的格式一致
            $currentPriceScore = PerpetualConditionalRedisKey::priceToScore($currentPrice);

            // 根据条件获取符合触发条件的订单ID
            $orderIds = [];

            switch ($condition) {
                case 'gte':
                    // 获取触发价格 <= 当前价格的订单
                    $orderIds = $this->cacheRedis->zRangeByScore($redisKey, '-inf', (string)$currentPriceScore);
                    break;

                case 'lte':
                    // 获取触发价格 >= 当前价格的订单
                    $orderIds = $this->cacheRedis->zRangeByScore($redisKey, (string)$currentPriceScore, '+inf');
                    break;
            }

            foreach ($orderIds as $orderId) {
                try {
                    // 为每个委托单加锁，防止重复处理
                    $orderLockKey = "conditional_order:lock:{$orderId}";
                    $lockResult = $this->cacheRedis->set($orderLockKey, time(), ['NX', 'EX' => 10]); // 10秒锁定

                    if (!$lockResult) {
                        // 该委托单已在处理中，跳过
                        continue;
                    }

                    try {
                        // 先从Redis中移除，避免重复触发
                        $removeResult = $this->cacheRedis->zRem($redisKey, $orderId);

                        if ($removeResult > 0) {
                            // 成功移除后再提交到异步队列
                            $success = $this->submitToAsyncQueue((int)$orderId, $currentPrice);

                            if ($success) {
                                $triggeredCount++;
                            } else {
                                // 提交失败，记录日志但不重新加入Redis（避免死循环）
                                $this->conditionalLogger->warning('委托单提交队列失败', [
                                    'order_id' => $orderId,
                                    'current_price' => $currentPrice
                                ]);
                            }
                        }

                    } finally {
                        // 释放委托单锁
                        $this->cacheRedis->del($orderLockKey);
                    }

                } catch (\Exception $e) {
                    $this->conditionalLogger->error('处理委托单异常', [
                        'order_id' => $orderId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

        } catch (\Exception $e) {
            $this->conditionalLogger->error('检查永续合约委托订单失败', [
                'redis_key' => $redisKey,
                'condition' => $condition,
                'current_price' => $currentPrice,
                'error' => $e->getMessage()
            ]);
        }

        return $triggeredCount;
    }

    /**
     * 提交委托订单到异步队列执行
     * 监控进程只负责收集触发的委托订单，不进行任何数据库查询
     * 所有的验证、状态检查、业务逻辑都在异步队列中处理
     */
    protected function submitToAsyncQueue(int $orderId, string $currentPrice): bool
    {
        try {
            // 创建异步任务，所有的验证和数据库操作都在队列中处理
            $job = new PerpetualConditionalOrderExecutionJob($orderId, [
                'execution_type' => 'price_trigger',
                'trigger_price' => $currentPrice,
                'current_price' => $currentPrice,
                'triggered_at' => date('Y-m-d H:i:s')
            ]);

            // 提交到异步队列
            pushAsyncJob(AsyncExecutorKey::PERPETUAL_CONTRACT_QUEUE->value, $job);

            return true;

        } catch (\Exception $e) {
            $this->conditionalLogger->error('提交委托订单到队列失败', [
                'conditional_order_id' => $orderId,
                'current_price' => $currentPrice,
                'error' => $e->getMessage()
            ]);
            return false;

        } catch (\Throwable $e) {
            $this->conditionalLogger->error('提交委托订单到队列异常', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 清理失效的委托订单
     */
    protected function cleanupFailedOrders(): void
    {
        try {
            // 清理数据库中失败的委托订单记录（可选）
            $cleanedCount = TradePerpetualConditionalOrder::where('status', ConditionalOrderStatus::FAILED->value)
                ->where('created_at', '<', \Carbon\Carbon::now()->subDays(7)) // 7天前的失败订单
                ->delete();

            if ($cleanedCount > 0) {
                $this->conditionalLogger->info('清理失效永续合约委托订单', ['count' => $cleanedCount]);
            }

        } catch (\Exception $e) {
            $this->conditionalLogger->error('清理失效永续合约委托订单失败', [
                'error' => $e->getMessage()
            ]);
        }
    }
}
