<?php

/**
 * UserAssetsSyncProcess.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/15
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Process\UserAssets;

use App\Enum\UserAssets\UserAssetsFlowsCacheKey;
use App\Service\RedisFactory\CacheRedis;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\Context\ApplicationContext;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\AbstractProcess;
use Hyperf\Process\Annotation\Process;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

#[Process(name: 'user-assets-sync-process')]
class UserAssetsSyncProcess extends AbstractProcess
{
    public int $nums = 1;

    #[Inject]
    protected CacheRedis $redis;

    protected LoggerInterface $logger;

    #[Inject]
    protected UserAccountsAssetService $service;

    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER',false);
    }

    public function handle(): void
    {
        $this->logger = logger('用户资金同步','userAccounts/sync-log.log');

        while (true){
            $data = null;
            try {
                $data = $this->redis->zPopMin(UserAssetsFlowsCacheKey::ASSETS_SYNC_QUEUE, 1);
                if(empty($data)){
                    usleep(500000);
                    continue;
                }
                $this->syncAssets($data);
            }catch (\RedisException $e){
                $this->logger->error("Redis连接异常", ['error' => $e->getMessage()]);
                sleep(3);
                $this->redis = ApplicationContext::getContainer()->get(CacheRedis::class);
            } catch (\Throwable $t){
                $this->logger->error("用户资金处理同步失败", [
                    'data' => $data,
                    'error' => $t->getMessage(),
                    'trace' => $t->getTraceAsString()
                ]);
                sleep(1);
            }
        }
    }

    protected function syncAssets(array $data): void
    {
        foreach ($data as $member => $score) {
            try {
                // 解析member格式: 可能是JSON数据
                $syncData = json_decode($member, true);
                if (!$syncData || !isset($syncData['callback'])) {
                    $this->logger->warning('同步数据格式错误', ['member' => $member]);
                    continue;
                }

                $callback = $syncData['callback'];
                unset($syncData['callback']);

                // 调用对应的服务方法
                $this->service->{$callback}(...$syncData);

                $this->logger->info('资金同步成功', [
                    'callback' => $callback,
                    'data' => $syncData
                ]);

            } catch (\Exception $e) {
                $this->logger->error('单条资金同步失败', [
                    'member' => $member,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}