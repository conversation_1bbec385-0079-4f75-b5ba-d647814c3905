<?php

declare(strict_types=1);

namespace App\Process\UserAssets;

use App\Enum\UserAssets\UserAssetsFlowsCacheKey;
use App\Model\User\UserAccountsFlow;
use App\Service\RedisFactory\CacheRedis;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\AbstractProcess;
use Hyperf\Process\Annotation\Process;
use Psr\Log\LoggerInterface;

#[Process(name: "user_assets_flows_process")]
class UserAssetsFlowsProcess extends AbstractProcess
{
    // 批量处理数量（写死）
    private const BATCH_SIZE = 50;

    // 处理间隔（秒，写死）
    private const PROCESS_INTERVAL = 1;

    #[Inject]
    private CacheRedis $redis;

    private LoggerInterface $logger;

    public function handle(): void
    {
        $this->logger = logger('资金流水记录','userAccounts/flows.log');
        $this->logger->info('用户资产流水处理进程启动');

        while (true) {
            try {
                $res = $this->processFlowsBatch();
                if($res){
                    usleep(100000); //阻塞0.1秒 理论1秒处理500条数据
                }else{
                    usleep(500000);
                }
            } catch (\Throwable $e) {
                $this->logger->error('流水处理进程异常', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                // 异常时休眠更长时间
                sleep(5);
            }
        }
    }

    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER',false);
    }

    /**
     * 批量处理流水数据
     */
    private function processFlowsBatch(): bool
    {
        $queueKey = UserAssetsFlowsCacheKey::getFlowsQueueKey();

        // 批量获取最旧的流水记录
        $flows = $this->redis->zPopMin($queueKey, self::BATCH_SIZE);

        if (empty($flows)) {
            return false;
        }

        $this->logger->info('获取到流水记录', ['count' => count($flows)]);

        // 解析流水数据
        $flowsData = [];
        foreach ($flows as $member => $score) {
            try {
                // 解析member格式: flowId|jsonData
                $parts = explode('|', $member, 2);
                if (count($parts) !== 2) {
                    $this->logger->warning('流水数据格式错误', ['member' => $member]);
                    continue;
                }

                $flowData = json_decode($parts[1], true);
                if (!$flowData) {
                    $this->logger->warning('流水数据JSON解析失败', ['member' => $member]);
                    continue;
                }

                // 添加时间戳用于排序
                $flowData['_timestamp'] = $score;
                $flowsData[] = $flowData;

            } catch (\Exception $e) {
                $this->logger->error('解析流水数据失败', [
                    'member' => $member,
                    'error' => $e->getMessage()
                ]);
            }
        }

        if (empty($flowsData)) {
            return false;
        }

        // 按时间戳排序
        usort($flowsData, function ($a, $b) {
            return $a['_timestamp'] <=> $b['_timestamp'];
        });

        // 移除排序用的时间戳字段
        foreach ($flowsData as &$flowData) {
            unset($flowData['_timestamp']);
            // 兼容老数据，补充 account_type 字段
            if (!isset($flowData['account_type'])) {
                $flowData['account_type'] = 0;
            }
        }

        // 批量写入数据库
        $this->batchInsertFlows($flowsData);

        return true;
    }

    /**
     * 批量插入流水数据到数据库
     */
    private function batchInsertFlows(array $flowsData): void
    {
        try {
            $startTime = microtime(true);

            // 使用事务批量插入
            Db::transaction(function () use ($flowsData) {
                UserAccountsFlow::query()->insert($flowsData);
            });

            $endTime = microtime(true);
            $duration = ($endTime - $startTime) * 1000; // 转换为毫秒

            $this->logger->info('批量写入流水成功', [
                'count' => count($flowsData),
                'duration_ms' => number_format($duration, 2)
            ]);

        } catch (\Exception $e) {
            $this->logger->error('批量写入流水失败，尝试逐条插入', [
                'count' => count($flowsData),
                'error' => $e->getMessage()
            ]);

            // 批量插入失败时，逐条重试
            $this->fallbackInsertFlows($flowsData);
        }
    }

    /**
     * 逐条插入流水数据（批量插入失败时的降级方案）
     */
    private function fallbackInsertFlows(array $flowsData): void
    {
        $successCount = 0;
        $failCount = 0;

        foreach ($flowsData as $flowData) {
            try {
                // 兼容老数据，补充 account_type 字段
                if (!isset($flowData['account_type'])) {
                    $flowData['account_type'] = 0;
                }
                $flow = new UserAccountsFlow();
                $flow->fill($flowData);
                $flow->save();
                $successCount++;

            } catch (\Exception $e) {
                $failCount++;
                $this->logger->error('单条流水插入失败', [
                    'flow_data' => $flowData,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->logger->info('逐条插入流水完成', [
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'total_count' => count($flowsData)
        ]);
    }
}
