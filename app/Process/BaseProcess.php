<?php

/**
 * BaseProcess.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/23
 * Website:algoquant.org
 */

namespace App\Process;

use App\Logger\LoggerFactory;
use App\Service\RedisFactory\MarketRedis;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\AbstractProcess;
use Psr\Container\ContainerInterface;
use Swoole\Coroutine;

/**
 * 封装进程间的运行执行命令启动
 */

class BaseProcess extends AbstractProcess
{
    public int $nums = 1;

    public bool $enableCoroutine = true;

    public string $cmd_key = '';

    #[Inject]
    public MarketRedis $redis;

    public LoggerFactory $logger;

    /**
     * 封装进程通信
     * @param ContainerInterface $container
     */
    public function __construct(ContainerInterface $container)
    {
        parent::__construct($container);
    }

    public function subscribeCmd(): void
    {
        try {
            $this->redis->subscribe([$this->cmd_key], function ($redis, $channel, $message) {
                try {
                    $message = json_decode($message, true);
                    if ($message === null) {
                        return;
                    }
                    $this->parse_cmd($message);
                } catch (\Throwable $e) {
                    $this->logger->get('process')->error("Error processing message: " . $e->getMessage());
                }
            });
        } catch (\Throwable $e) {
            $this->logger->get('process')->error("Redis subscribe error: " . $e->getMessage());
            Coroutine::sleep(3);
            go(function (){
                $this->subscribeCmd();
            });
        }
    }

    public function handle(): void
    {
        $this->logger = $this->container->get(LoggerFactory::class);
        $this->logger->get('process')->info("{$this->name} process started");
        
        if (!empty($this->cmd_key)) {
            go(function () {
                $this->subscribeCmd();
            });
        }
        $this->runBusinessLogic();
    }

    /**
     * 执行业务逻辑（子类可以重写此方法）
     * @return void
     */
    protected function runBusinessLogic(): void{}

    /**
     * 解析redis 中推送到进程的命令
     * @param array $message
     * @return void
     */
    public function parse_cmd(array $message): void{}
}