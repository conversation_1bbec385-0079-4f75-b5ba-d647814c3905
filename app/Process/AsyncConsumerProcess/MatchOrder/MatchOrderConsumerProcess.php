<?php

/**
 * MatchOrderConsumerProcess.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/30
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Process\AsyncConsumerProcess\MatchOrder;

use Hyperf\AsyncQueue\Process\ConsumerProcess;
use Hyperf\Process\Annotation\Process;

#[Process(name: 'match-order-consumer')]
class MatchOrderConsumerProcess extends ConsumerProcess
{
    public bool $enableCoroutine = true;

    protected string $queue = 'match-order';

    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER',false);
    }
}