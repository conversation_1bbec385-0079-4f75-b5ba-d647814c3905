<?php

declare(strict_types=1);
/**
 * 永续合约异步任务消费进程
 */

namespace App\Process\AsyncConsumerProcess;

use Hyperf\AsyncQueue\Process\ConsumerProcess;
use Hyperf\Process\Annotation\Process;

#[Process(name: 'perpetual-contract-async-process')]
class PerpetualContractAsyncProcess extends ConsumerProcess
{
    public string $queue = 'perpetual-contract';

    public bool $enableCoroutine = true;

    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER', false);
    }
}
