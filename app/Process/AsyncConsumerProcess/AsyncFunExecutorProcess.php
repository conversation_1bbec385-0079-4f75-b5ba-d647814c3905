<?php

/**
 * AsyncFunExecutorJob.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/24
 * Website:algoquant.org
 */

namespace App\Process\AsyncConsumerProcess;

use Hyperf\AsyncQueue\Process\ConsumerProcess;
use Hyperf\Process\Annotation\Process;

/**
 * 异步任务处理进程
 */
#[Process(name: 'async-consumer-process')]
class AsyncFunExecutorProcess extends ConsumerProcess
{
    protected string $queue = 'async-func-executor';

    public bool $enableCoroutine = true;

    /**
     * 全部启动，双端处理
     * @param $server
     * @return bool
     */
    public function isEnable($server): bool
    {
        return true;
    }
}