<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Api\Service\User;

use ApiElf\HyperfEncryption\Crypt;
use App\Exception\BusinessException;
use App\Exception\JwtInBlackException;
use App\Http\Api\Event\UserLoginEvent;
use App\Http\Api\Event\UserRegisteredEvent;
use App\Http\Api\Service\Agent\AgentService;
use App\Http\Api\Service\BaseService;
use App\Http\Api\Service\Sms\EmailService;
use App\Http\Api\Service\Sms\SmsService;
use App\Http\Common\ResultCode;
use App\Model\Agent\AgentClient;
use App\Model\Agent\AgentInviteCode;
use App\Model\User\Enums\PasswordType;
use App\Model\User\Enums\RegisterType;
use App\Model\User\Enums\Status;
use App\Model\User\User;
use App\Model\User\UserLoginLog;
use App\Model\User\UserPasskey;
use App\Model\User\UserPasswordHistory;
use App\Model\User\UserRelation;
use App\Model\User\UserVipLevel;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Firebase\JWT\JWT;
use Firebase\JWT\JWK;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Lcobucci\JWT\Token\RegisteredClaims;
use Lcobucci\JWT\UnencryptedToken;
use Mine\Jwt\Factory;
use Mine\Jwt\JwtInterface;
use Mine\JwtAuth\Interfaces\CheckTokenInterface;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\SimpleCache\CacheInterface;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Redis\Redis;
use Lcobucci\JWT\Token;
use Psr\Http\Message\ServerRequestInterface;
use Hyperf\Collection\Arr;
use Hyperf\Stringable\Str;
use PragmaRX\Google2FA\Google2FA;

final class UserService extends BaseService implements CheckTokenInterface
{
    /**
     * @var string jwt场景
     */
    private string $jwt = 'api';

    public const AUTH_CACHE_KEY = "api:user:auth:uid-%s-%d"; // 用户身份信息缓存 user_id-device => token 使用普通的key 作为缓存利润ttl
    public const USER_CACHE_KEY = "api:user:info:uid-%d"; // 用户身份信息缓存 user_id => json info

    #[Inject]
    private readonly Factory $jwtFactory;

    #[Inject]
    private readonly EventDispatcherInterface $dispatcher;

    #[Inject]
    protected CacheInterface $cache;

    #[Inject]
    protected EmailService $emailService;

    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected SmsService $smsService;

    #[Inject]
    protected DeviceService $deviceService;

    #[Inject]
    protected AgentService $agentService;

    /**
     * 发送邮箱验证码
     */
    public function sendEmailCode(string $email, string $type = 'register'): void
    {
        // 检查发送频率限制 (1分钟内只能发送一次)
        $rateLimitKey = 'email_code_rate_limit:' . $email;
        if ($this->cache->has($rateLimitKey)) {
            throw new BusinessException(ResultCode::FORBIDDEN, '发送过于频繁，请稍后再试');
        }

        if ($type === 'register' || $type === 'bind_email') {
            if (User::query()->where('email', $email)->exists()) {
                throw new BusinessException(ResultCode::FORBIDDEN, '邮箱已注册');
            }
        }

        if ($type === 'reset_password' || $type === 'login' || $type === 'set_fund_password') {
            if (!User::query()->where('email', $email)->exists()) {
                throw new BusinessException(ResultCode::FORBIDDEN, '邮箱不存在');
            }
        }

        // 生成6位随机验证码
        $code = str_pad((string)random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        // 存储验证码到缓存,10分钟有效
        $this->cache->set($type . '_email_code_' . $email, $code, 600);

        // 设置发送频率限制 (1分钟)
        $this->cache->set($rateLimitKey, 1, 60);

        // 记录IP地址，防止恶意请求
        $ip = $this->deviceService->getClientIp();
        $ipLimitKey = 'email_code_ip_limit:' . $ip;
        $ipCount = (int)$this->cache->get($ipLimitKey, 0);
        if ($ipCount > 10) { // 同一IP 1小时内最多发送10次
            throw new BusinessException(ResultCode::FORBIDDEN, '发送次数超限，请稍后再试');
        }
        $this->cache->set($ipLimitKey, $ipCount + 1, 3600);

        $this->emailService->sendVerificationCode($email, $code);
    }

    /**
     * 用户注册(邮箱注册)
     */
    public function registerByEmail(array $data): array
    {
        // 验证邮箱验证码
        $codeKey = 'register_email_code_' . $data['email'];
        $code = $this->cache->get($codeKey);

        if (!$code || $code !== $data['email_code']) {
            throw new BusinessException(ResultCode::FORBIDDEN, '验证码错误或已过期');
        }

        // 查找邀请人
        $parentId = null;
        $inviterInfo = false;
        if (!empty($data['invite_code'])) {
            $inviterInfo = $this->getInviterInfo($data['invite_code']);
            $parent = $inviterInfo['parent'];
            $parentId = $parent->id;
        }

        // 生成唯一邀请码
        $inviteCode = $this->generateInviteCode();

        // 事务
        Db::beginTransaction();
        try {
            // 创建用户
            $username = 'CPX-' . strtoupper(substr(md5(uniqid() . microtime(true)), 0, 7));
            $user = new User();
            $user->account = substr(strval(microtime(true) * 10000 + random_int(1000, 9999)) , -11);
            $user->username = $username;
            $user->email = $data['email'];
            $user->password = $data['password']; // 模型会自动加密
            $user->display_name = '@' . $username;
            $user->invite_code = $inviteCode;
            $user->parent_id = $parentId;
            $user->register_type = RegisterType::EMAIL;
            $user->save();

            if ($inviterInfo && $parentId && $parent->agent_id) {
                // 如果父级是代理商，则当前用户为代理商直客
                $this->agentClientCreate($user, $parent->agent_id, $inviterInfo['agentInviteCode']);
            }

            if ($parentId) {
                // 维护 cpx_user_relation
                $plink = UserRelation::query()->where(UserRelation::FIELD_USER_ID, $parentId)->first();
                if ($plink) {
                    $link = $plink->link;
                    array_unshift($link, $parentId);
                } else {
                    $link = [$parentId];
                }
                UserRelation::query()->create([
                    UserRelation::FIELD_USER_ID => $user->id,
                    UserRelation::FIELD_PARENT_ID => $parentId,
                    UserRelation::FIELD_LINK => $link,
                ]);
            }

            Db::commit();
        } catch (\Throwable $th) {
            Db::rollBack();
            throw new BusinessException(ResultCode::FAIL, '注册失败，' . $th->getMessage());
        }

        $this->dispatcher->dispatch(new UserRegisteredEvent($user, $this->request));

        // 注册成功后自动登录
        return $this->login($username, $data['password'], true);
    }

    /**
     * 用户注册(手机号注册)
     */
    public function registerByPhone(array $data): array
    {
        $this->smsService->validate($data['phone'], $data['code'], $data['sms_code'], 'register');

        // 查找邀请人
        $parentId = null;
        if (!empty($data['invite_code'])) {
            $inviterInfo = $this->getInviterInfo($data['invite_code']);
            $parent = $inviterInfo['parent'];
            $parentId = $parent->id;
        }

        // 生成唯一邀请码
        $inviteCode = $this->generateInviteCode();

        // 事务
        Db::beginTransaction();
        try {
            // 创建用户
            $username = 'CPX-' . strtoupper(substr(md5(uniqid() . microtime(true)), 0, 7));
            $user = new User();
            $user->account = substr((string) microtime(true) * 10000 + random_int(1000, 9999), -11);
            $user->username = $username;
            $user->phone = $data['phone'];
            $user->phone_country_code = $data['code'];
            $user->password = $data['password']; // 模型会自动加密
            $user->display_name = '@' . $username;
            $user->invite_code = $inviteCode;
            $user->parent_id = $parentId;
            $user->register_type = RegisterType::PHONE;
            $user->save();

            if ($inviterInfo && $parentId && $parent->agent_id) {
                // 如果父级是代理商，则当前用户为代理商直客
                $this->agentClientCreate($user, $parent->agent_id, $inviterInfo['agentInviteCode']);
            }

            if ($parentId) {
                // 维护 cpx_user_relation
                $plink = UserRelation::query()->where(UserRelation::FIELD_USER_ID, $parentId)->first();
                if ($plink) {
                    $link = $plink->link;
                    array_unshift($link, $parentId);
                } else {
                    $link = [$parentId];
                }
                UserRelation::query()->create([
                    UserRelation::FIELD_USER_ID => $user->id,
                    UserRelation::FIELD_PARENT_ID => $parentId,
                    UserRelation::FIELD_LINK => $link,
                ]);
            }

            Db::commit();
        } catch (\Throwable $th) {
            Db::rollBack();
            throw new BusinessException(ResultCode::FAIL, '注册失败，' . $th->getMessage());
        }

        $this->dispatcher->dispatch(new UserRegisteredEvent($user, $this->request));

        // 注册成功后自动登录
        return $this->login($username, $data['password'], true);
    }

    /**
     * 用户登录
     * @param string $username 用户名
     * @param string $password 密码
     * @param bool $ignoreVerification 是否忽略设备验证
     */
    public function login(string $username, string $password, bool $ignoreVerification = false): array
    {
        $user = User::query()->where('username', $username)->orWhere('email', $username)->orWhere('phone', $username)->first();

        if (!$user) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户不存在');
        }

        if ($user->status == \App\Model\User\Enums\Status::DISABLED) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户已禁用');
        }

        if ($user->status == \App\Model\User\Enums\Status::DELETED) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户已注销');
        }

        if (!password_verify($password, $user->password)) {
            throw new BusinessException(ResultCode::UNPROCESSABLE_ENTITY, trans('auth.password_error'));
        }

        // if (!$ignoreVerification) {
        //     // 更换登录设备时或 IP 归属地发生变化时需要邮箱验证码、短信验证码
        //     $loginLog = UserLoginLog::query()->where(UserLoginLog::FIELD_USER_ID, $user->id)->orderBy(UserLoginLog::FIELD_CREATED_AT, 'desc')->first();

        //     // 当前登录设备id
        //     $currentDeviceId = $this->deviceService->getDeviceId();

        //     // IP 归属地发生变化时需要短信验证码
        //     // 通过 IP 归属地接口由前端上报并保存到 Redis 中
        //     $ip_location = $this->redis->get('ip_location_' . $this->deviceService->getClientIp());

        //     if ($loginLog->device_id !== $currentDeviceId || empty($ip_location) || $loginLog->location_province !== $ip_location['province']) {
        //         // 需要进一步验证，将引导用户通过邮箱、手机号验证码登录
        //         $verify_info = [];
        //         if (!empty($user->email)) {
        //             // 绑定了邮箱需要邮箱验证码
        //             $verify_info['email'] = $user->email;
        //         }
        //         if (!empty($user->phone)) {
        //             // 绑定了手机号需要短信验证码
        //             $verify_info['phone'] = $user->phone;
        //             $verify_info['code'] = $user->phone_country_code;
        //         }
        //         if (!empty($user->google2fa_secret)) {
        //             // 绑定了谷歌验证器需要谷歌验证码
        //             $verify_info['username'] = $user->username; // 用户名（验证登录接口需要）
        //             $verify_info['google2fa'] = env('APP_NAME'); // 谷歌验证器名称
        //         }
        //         return [
        //             'need_verify' => true,
        //             'verify_info' => $verify_info,
        //         ];
        //     }
        // }

        $jwt = $this->getJwt();

        $access_token = $jwt->builderAccessToken((string) $user->id)->toString();

        $this->setUserCache($user, $access_token);

        // 记录登录设备信息、登录日志
        $this->dispatcher->dispatch(new UserLoginEvent($user, $access_token, $this->request));

        return [
            'access_token' => $access_token,
            'refresh_token' => $jwt->builderRefreshToken((string) $user->id)->toString(),
            'expire_at' => time() + (int) $jwt->getConfig('ttl', 0),
            'user' => $user,
        ];
    }

    /**
     * 验证码登录
     */
    public function verifyCodeLogin(array $data): array
    {
        $user = null;
        if (!empty($data['phone'])) {
            $user = User::query()->where('phone', $data['phone'])->where('phone_country_code', $data['code'])->first();
        }

        if (!empty($data['email']) && !$user) {
            $user = User::query()->where('email', $data['email'])->first();
        }

        if (!empty($data['username']) && !$user) {
            $user = User::query()->where('username', $data['username'])->first();
        }

        if (!$user) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户不存在');
        }

        if ($user->status == \App\Model\User\Enums\Status::DISABLED) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户已禁用');
        }

        if ($user->status == \App\Model\User\Enums\Status::DELETED) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户已注销');
        }

        if (!empty($user->email)) {
            // 验证邮箱验证码
            $codeKey = 'login_email_code_' . $data['email'];
            $vcode = $this->cache->get($codeKey);

            if (!$vcode || $vcode !== $data['email_code']) {
                throw new BusinessException(ResultCode::FORBIDDEN, '验证码错误或已过期');
            }
        }

        if (!empty($user->phone)) {
            // 验证短信验证码
            $this->smsService->validate($data['phone'], $data['code'], $data['sms_code'], 'login');
        }

        // 谷歌验证码
        if (!empty($user->google2fa_secret)) {
            $google2fa_secret = Crypt::decrypt($user->google2fa_secret);
            $google2fa = new Google2FA();
            if (!$google2fa->verifyKey($google2fa_secret, $data['google2fa_code'])) {
                throw new BusinessException(ResultCode::FORBIDDEN, '谷歌验证码错误');
            }
        }

        $jwt = $this->getJwt();

        $access_token = $jwt->builderAccessToken((string) $user->id)->toString();

        $this->setUserCache($user, $access_token);

        // 记录登录设备信息、登录日志
        $this->dispatcher->dispatch(new UserLoginEvent($user, $access_token, $this->request));

        return [
            'access_token' => $access_token,
            'refresh_token' => $jwt->builderRefreshToken((string) $user->id)->toString(),
            'expire_at' => time() + (int) $jwt->getConfig('ttl', 0),
            'user' => $user,
        ];
    }

    public function setUserCache(User $user, string $token = ''): void
    {
        $user_id = (int) $user->id;
        $expire = (int) $this->getJwt()->getConfig('ttl', 0);
        $user = $user->except(['password', 'fund_password', 'google2fa_secret', 'third_party_auth']);
        
        //缓存用户交易费率数据
        $feeRates = UserVipLevel::getUserFeeRates($user_id);
        $user = array_merge($user, ['vip_level_id' => $feeRates['vip_level_id']]);
        foreach ($user as $key => $value) {
            if(is_object($value) &&  enum_exists(get_class($value))){
                $user[$key] = $value->value;
            }
            if($value instanceof Carbon) {
                $user[$key] = $value->format('Y-m-d H:i:s');
            }
        }

        $this->redis->hMSet(
            sprintf(self::USER_CACHE_KEY, $user_id),
            $user
        );
        $this->redis->expire(sprintf(self::USER_CACHE_KEY, $user_id), $expire);

        // 修改为运行多端登录
        if ($token) {
            $device = $this->request->getHeaderLine('x-device-info');
            $this->redis->setex(sprintf(self::AUTH_CACHE_KEY, $this->deviceName($device), $user_id), $expire, $token);
        }
    }

    public function getUserCache(int $user_id): ?User
    {
        $user = $this->redis->hGetAll(sprintf(self::USER_CACHE_KEY, $user_id));
        if ($user) {
            return (new User())->fill($user);
        }
        return null;
    }

    public function getTokenCache(int $user_id, string $device = ''): ?string
    {
        if (!$device) {
            $device = $this->request->getHeaderLine('x-device-info');
        }
        return $this->redis->get(sprintf(self::AUTH_CACHE_KEY, $this->deviceName($device), $user_id));
    }

    public function getUserInfo(): User
    {
        $user = User::query()
            ->with('kycVerification:id,user_id,first_name,middle_name,last_name,status')
            ->find($this->request->getAttribute('user_id'));
        if (!$user) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户不存在');
        }

        // 更新用户缓存
        $this->setUserCache($user);

        return $user;
    }

    /**
     * 用户退出登录
     */
    public function logout(UnencryptedToken $token): bool
    {
        $userId = $this->request->getAttribute('user_id');
        $this->redis->del(sprintf(self::AUTH_CACHE_KEY, $this->deviceName($this->request->getHeaderLine('x-device-info')), $userId));
        $this->redis->del(sprintf(self::USER_CACHE_KEY, $userId));
        $this->getJwt()->addBlackList($token);
        return true;
    }

    /**
     * 刷新令牌
     */
    public function refreshToken(UnencryptedToken $token): array
    {
        $jwt = $this->getJwt();

        $access_token = $jwt->builderAccessToken($token->claims()->get(RegisteredClaims::ID))->toString();
        $device = $this->request->getHeaderLine('x-device-info');
        $expire = (int) $jwt->getConfig('ttl', 0);
        $this->redis->setex(sprintf(self::AUTH_CACHE_KEY, $this->deviceName($device), $token->claims()->get(RegisteredClaims::ID)), $expire, $access_token);

        $jwt->addBlackList($token);
        return [
            'access_token' => $access_token,
            'refresh_token' => $jwt->builderRefreshToken($token->claims()->get(RegisteredClaims::ID))->toString(),
            'expire_at' => time() + (int) $jwt->getConfig('ttl', 0),
        ];
    }

    /**
     * 重置密码
     */
    public function resetPassword(array $data): array
    {
        $user = null;
        if (!empty($data['phone'])) {
            $user = User::query()->where('phone', $data['phone'])->where('phone_country_code', $data['code'])->first();
        }

        if (!empty($data['email'])) {
            $user = User::query()->where('email', $data['email'])->first();
        }

        if (!$user) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户不存在');
        }

        if (!empty($user->email)) {
            // 验证码校验
            $cacheKey = 'reset_password_email_code_' . $data['email'];
            $cachedCode = $this->cache->get($cacheKey);
            if (!$cachedCode || $cachedCode !== $data['email_code']) {
                throw new BusinessException(ResultCode::FORBIDDEN, '验证码错误或已过期');
            }
        }

        if (!empty($user->phone)) {
            // 验证短信验证码
            $this->smsService->validate($data['phone'], $data['code'], $data['sms_code'], 'reset_password');
        }

        // 谷歌验证码
        if (!empty($user->google2fa_secret)) {
            $google2fa_secret = Crypt::decrypt($user->google2fa_secret);
            $google2fa = new Google2FA();
            if (!$google2fa->verifyKey($google2fa_secret, $data['google2fa_code'])) {
                throw new BusinessException(ResultCode::FORBIDDEN, '谷歌验证码错误');
            }
        }

        // 检查密码是否与历史密码重复
        $historyPasswords = UserPasswordHistory::query()
            ->where(UserPasswordHistory::FIELD_USER_ID, $user->id)
            ->where(UserPasswordHistory::FIELD_PASSWORD_TYPE, PasswordType::LOGIN)
            ->get();
        /** @var UserPasswordHistory $historyPassword */
        foreach ($historyPasswords as $historyPassword) {
            if (password_verify($data['password'], $historyPassword->password_hash)) {
                throw new BusinessException(ResultCode::FORBIDDEN, '密码不能与历史密码重复');
            }
        }

        $user->password = $data['password'];
        $user->save();

        // 记录密码历史
        $userPasswordHistory = new UserPasswordHistory();
        $userPasswordHistory->user_id = $user->id;
        $userPasswordHistory->password_hash = $user->password;
        $userPasswordHistory->password_type = PasswordType::LOGIN;
        $userPasswordHistory->change_ip = $this->deviceService->getClientIp();
        $userPasswordHistory->save();

        // 如果是登录状态下，则退出登录（主要是将旧密码对应的 token 加入黑名单）
        try {
            /** @var \Lcobucci\JWT\UnencryptedToken $token */
            $token = $this->parserToken($this->request);
            $userId = (int) $token->claims()->get(RegisteredClaims::ID);
            $this->request->withAttribute('user_id', $userId);
            $this->logout($token);
        } catch (\Throwable $th) {
            // 忽略
        }

        return $this->login($user->username, $data['password']);
    }

    protected function parserToken(ServerRequestInterface $request): Token
    {
        return $this->getJwt()->parserAccessToken($this->getToken($request));
    }

    protected function getToken(ServerRequestInterface $request): string
    {
        if ($request->hasHeader('Authorization')) {
            return Str::replace('Bearer ', '', $request->getHeaderLine('Authorization'));
        }
        if ($request->hasHeader('token')) {
            return $request->getHeaderLine('token');
        }
        if (Arr::has($request->getQueryParams(), 'token')) {
            return $request->getQueryParams()['token'];
        }
        return '';
    }

    /**
     * 检查JWT令牌
     */
    public function checkJwt(UnencryptedToken $token): void
    {
        $expireAt = $token->claims()->get(RegisteredClaims::EXPIRATION_TIME)->getTimestamp();
        if ($expireAt < time()) {
            $this->getJwt()->addBlackList($token);
            throw new BusinessException(ResultCode::UNAUTHORIZED, '登录状态已过期，请重新登录');
        }

        $this->getJwt()->hasBlackList($token) && throw new JwtInBlackException();
    }

    /**
     * 获取JWT实例
     */
    public function getJwt(): JwtInterface
    {
        return $this->jwtFactory->get($this->jwt);
    }

    /**
     * 第三方登录
     */
    public function oauthLogin(array $data): array
    {
        $provider = $data['provider'];
        $token = $data['token'];
        $displayName = !empty($data['display_name']) ? $data['display_name'] : null;
        $avatar = !empty($data['avatar']) ? $data['avatar'] : null;

        // 验证第三方token并获取用户信息
        $userInfo = $this->verifyOAuthToken($provider, $token);

        if (!empty($userInfo['name'])) {
            $displayName = $userInfo['name'];
        }

        if (!empty($userInfo['picture'])) {
            $avatar = $userInfo['picture'];
        }

        if (!$userInfo || empty($userInfo['sub'])) {
            throw new BusinessException(ResultCode::FORBIDDEN, '第三方授权验证失败');
        }

        $oauthId = $userInfo['sub']; // 第三方用户唯一标识
        $email = !empty($userInfo['email']) ? $userInfo['email'] : null;

        // 根据第三方ID查找用户
        $user = null;
        if ($provider === 'google') {
            $user = User::query()->where('third_party_auth->google->sub', $oauthId)->first();
        } else if ($provider === 'apple') {
            $user = User::query()->where('third_party_auth->apple->sub', $oauthId)->first();
        }

        if (!$user && !empty($email)) {
            $user = User::query()->where('email', $email)->first();
            if ($user) {
                $user->third_party_auth[$provider]['sub'] = $oauthId;
                $user->save();
            }
        }

        // 如果用户不存在，则创建新用户
        if (!$user) {
            // 查找邀请人
            $parentId = null;
            if (!empty($data['invite_code'])) {
                $inviterInfo = $this->getInviterInfo($data['invite_code']);
                $parent = $inviterInfo['parent'];
                $parentId = $parent->id;
            }

            // 生成唯一邀请码
            $inviteCode = $this->generateInviteCode();

            // 事务
            Db::beginTransaction();
            try {
                // 创建用户
                $username = 'CPX-' . strtoupper(substr(md5(uniqid() . microtime(true)), 0, 7));
                $user = new User();
                $user->account = substr((string) microtime(true) * 10000 + random_int(1000, 9999), -11);
                $user->username = $username;
                $user->email = $email;
                $user->password = $user->account; // 第三方登录，密码默认为账号
                $user->display_name = !empty($displayName) ? $displayName : '@' . $username;
                $user->invite_code = $inviteCode;
                $user->parent_id = $parentId;
                $user->register_type = $provider === 'google' ? RegisterType::GOOGLE : RegisterType::APPLE;
                $user->third_party_auth = [
                    $provider => [
                        'iss' => $userInfo['iss'] ?? '',
                        'aud' => $userInfo['aud'] ?? '',
                        'sub' => $oauthId
                    ]
                ];
                $user->avatar = $avatar;
                $user->save();

                if ($inviterInfo && $parentId && $parent->agent_id) {
                    // 如果父级是代理商，则当前用户为代理商直客
                    $this->agentClientCreate($user, $parent->agent_id, $inviterInfo['agentInviteCode']);
                }

                if ($parentId) {
                    // 维护 cpx_user_relation
                    $plink = UserRelation::query()->where(UserRelation::FIELD_USER_ID, $parentId)->first();
                    if ($plink) {
                        $link = $plink->link;
                        array_unshift($link, $parentId);
                    } else {
                        $link = [$parentId];
                    }
                    UserRelation::query()->create([
                        UserRelation::FIELD_USER_ID => $user->id,
                        UserRelation::FIELD_PARENT_ID => $parentId,
                        UserRelation::FIELD_LINK => $link,
                    ]);
                }

                Db::commit();
            } catch (\Throwable $th) {
                Db::rollBack();
                throw new BusinessException(ResultCode::FAIL, '注册失败，' . $th->getMessage());
            }

            $this->dispatcher->dispatch(new UserRegisteredEvent($user, $this->request));
        }

        // 登录
        if ($user->status == \App\Model\User\Enums\Status::DISABLED) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户已禁用');
        }

        if ($user->status == \App\Model\User\Enums\Status::DELETED) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户已注销');
        }

        $jwt = $this->getJwt();

        $access_token = $jwt->builderAccessToken((string) $user->id)->toString();

        $this->setUserCache($user, $access_token);

        // 记录登录设备信息、登录日志
        $this->dispatcher->dispatch(new UserLoginEvent($user, $access_token, $this->request));

        return [
            'access_token' => $access_token,
            'refresh_token' => $jwt->builderRefreshToken((string) $user->id)->toString(),
            'expire_at' => time() + (int) $jwt->getConfig('ttl', 0),
            'user' => $user,
        ];
    }

    /**
     * 验证第三方OAuth令牌
     */
    public function verifyOAuthToken(string $provider, string $token): ?array
    {
        $client = new Client();

        $deviceName = container()->get(RequestInterface::class)->getHeaderLine('x-device-info', 'unknow');
        $deviceType = $this->deviceService->getDeviceType($deviceName);

        if ($provider === 'google') {
            try {
                // 获取Google的公钥
                $response = $client->get('https://www.googleapis.com/oauth2/v3/certs');
                $keys = json_decode($response->getBody()->getContents(), true);

                // 验证ID令牌
                $payload = JWT::decode($token, JWK::parseKeySet($keys));

                // 验证aud (audience)

                match (strtolower($deviceType)) {
                    'android' => $googleClientId = env('GOOGLE_CLIENT_ID_ANDROID'),
                    'ios' => $googleClientId = env('GOOGLE_CLIENT_ID_IOS'),
                    'macos' => $googleClientId = env('GOOGLE_CLIENT_ID_MACOS'),
                    'web' => $googleClientId = env('GOOGLE_CLIENT_ID_WEB'),
                    'windows' => $googleClientId = env('GOOGLE_CLIENT_ID_WINDOWS'),
                };
                if ($googleClientId && $payload->aud !== $googleClientId) {
                    return null;
                }

                // 验证令牌是否过期
                if ($payload->exp < time()) {
                    return null;
                }

                return (array)$payload;
            } catch (\Throwable $e) {
                // 记录错误日志
                // $this->logger->error('Google OAuth验证失败', ['error' => $e->getMessage()]);
                return null;
            }
        } elseif ($provider === 'apple') {
            try {
                // 获取Apple的公钥
                $response = $client->get('https://appleid.apple.com/auth/keys');
                $keys = json_decode($response->getBody()->getContents(), true);

                // 验证ID令牌
                $payload = JWT::decode($token, JWK::parseKeySet($keys));
                // 验证iss (issuer)
                if ($payload->iss !== 'https://appleid.apple.com') {
                    return null;
                }

                // 验证aud (audience)
                match (strtolower($deviceType)) {
                    'ios' => $appleClientId = env('APPLE_CLIENT_ID'),
                    'macos' => $appleClientId = env('APPLE_CLIENT_ID_MACOS')
                };
                if ($appleClientId && $payload->aud !== $appleClientId) {
                    return null;
                }

                // 验证令牌是否过期
                if ($payload->exp < time()) {
                    return null;
                }

                return (array)$payload;
            } catch (\Throwable $e) {
                // 记录错误日志
                // $this->logger->error('Apple OAuth验证失败', ['error' => $e->getMessage()]);
                return null;
            }
        }

        return null;
    }

    /**
     * 获取邀请人
     */
    public function getInviterInfo(string $inviteCode)
    {
        // 查询 cpx_agent_invite_code 表
        $agentInviteCode = AgentInviteCode::query()->where(AgentInviteCode::FIELD_INVITE_CODE, $inviteCode)->first();

        if ($agentInviteCode) {
            $parent = User::query()->where(User::FIELD_ID, $agentInviteCode->user_id)->first();
        } else {
            $parent = User::query()->where(User::FIELD_INVITE_CODE, $inviteCode)->first();
        }

        if (!$parent) {
            throw new BusinessException(ResultCode::FORBIDDEN, '邀请码不存在');
        }

        return [
            'parent' => $parent,
            'agentInviteCode' => $agentInviteCode,
        ];
    }

    /**
     * 代理商直客记录，直客手续费返佣设置
     */
    public function agentClientCreate(User $user, int $agentId, AgentInviteCode $agentInviteCode)
    {
        if (!$agentInviteCode) {
            return false;
        }

        $agentClient = new AgentClient();
        $agentClient->agent_id = $agentId;
        $agentClient->invite_code_id = $agentInviteCode->id;
        $agentClient->user_id = $user->id;
        $agentClient->spot_commission_rate = $agentInviteCode->spot_commission_rate;
        $agentClient->contract_commission_rate = $agentInviteCode->contract_commission_rate;
        $agentClient->trader_spot_commission_rate = $agentInviteCode->trader_spot_commission_rate;
        $agentClient->trader_contract_commission_rate = $agentInviteCode->trader_contract_commission_rate;
        $agentClient->save();

        // 代理商直客ID
        $user->agent_client_id = $agentClient->id;
        $user->save();

        // 维护代理商直客注册人数
        $this->agentService->registerIncrement($agentId);

        return true;
    }

    public function bindPhone(array $data): User
    {
        $phone = $data['phone'];
        $code = $data['code'];
        $smsCode = $data['sms_code'];

        // 验证手机号验证码
        $this->smsService->validate($phone, $code, $smsCode, 'bind_phone');

        // 验证手机号是否已绑定
        $user = $this->getUser();
        if ($user && $user->phone) {
            throw new BusinessException(ResultCode::FORBIDDEN, '手机号已绑定');
        }

        $user = $this->getUser();

        $user->phone = $phone;
        $user->phone_country_code = $code;
        $user->save();

        return $user;
    }

    public function bindEmail(array $data): User
    {
        $email = $data['email'];
        $emailCode = $data['email_code'];

        // 验证邮箱验证码
        $codeKey = 'bind_email_email_code_' . $email;
        $code = $this->cache->get($codeKey);

        if (!$code || $code !== $emailCode) {
            throw new BusinessException(ResultCode::FORBIDDEN, '验证码错误或已过期');
        }

        $user = $this->getUser();

        $user->email = $email;
        $user->save();

        return $user;
    }

    /**
     * 获取谷歌验证器密钥及二维码
     */
    public function google2fa(array $data): array
    {
        $password = $data['password'];

        $user = $this->getUser();

        // 验证密码
        if (!password_verify($password, $user->password)) {
            throw new BusinessException(ResultCode::UNPROCESSABLE_ENTITY, trans('auth.password_error'));
        }

        if ($user->email) {
            // 验证邮箱验证码
            $codeKey = 'bind_google2fa_email_code_' . $user->email;
            $code = $this->cache->get($codeKey);

            if (!$code || $code !== $data['email_code']) {
                throw new BusinessException(ResultCode::FORBIDDEN, '验证码错误或已过期');
            }
        }

        if ($user->phone) {
            // 验证手机号验证码
            $this->smsService->validate($user->phone, $user->phone_country_code, $data['sms_code'], 'bind_google2fa');
        }

        // 生成谷歌验证器密钥
        $google2fa = new Google2FA();
        $google2faSecret = $google2fa->generateSecretKey();

        // 生成谷歌验证器二维码
        $holder = !empty($user->email) ? $user->email : $user->username;
        $qrCodeUrl = $google2fa->getQRCodeUrl(
            env('APP_NAME'),
            $holder,
            $google2faSecret
        );

        return [
            'google2fa_secret' => $google2faSecret,
            'qr_code_url' => $qrCodeUrl,
        ];
    }

    /**
     * 绑定谷歌验证器
     */
    public function bindGoogle2fa(array $data): User
    {
        $user = $this->getUser();

        $google2fa = new Google2FA();
        if (!$google2fa->verifyKey($data['google2fa_secret'], $data['google2fa_code'])) {
            throw new BusinessException(ResultCode::FORBIDDEN, '谷歌验证码错误');
        }

        $user->google2fa_secret = Crypt::encrypt($data['google2fa_secret']);
        $user->save();

        return $user;
    }

    /**
     * 设置资金密码
     */
    public function setFundPassword(array $data)
    {
        $user = $this->getUser();

        if ($user->email) {
            // 验证邮箱验证码
            $codeKey = 'set_fund_password_email_code_' . $user->email;
            $code = $this->cache->get($codeKey);

            if (!$code || $code !== $data['email_code']) {
                throw new BusinessException(ResultCode::FORBIDDEN, '验证码错误或已过期');
            }
        }

        if ($user->phone) {
            // 验证手机号验证码
            $this->smsService->validate($user->phone, $user->phone_country_code, $data['sms_code'], 'set_fund_password');
        }

        // 谷歌验证码
        if (!empty($user->google2fa_secret)) {
            $google2fa_secret = Crypt::decrypt($user->google2fa_secret);
            $google2fa = new Google2FA();
            if (!$google2fa->verifyKey($google2fa_secret, $data['google2fa_code'])) {
                throw new BusinessException(ResultCode::FORBIDDEN, '谷歌验证码错误');
            }
        }

        $user->fund_password = $data['fund_password'];
        $user->save();

        return $user;
    }

    /**
     * 获取进一步登录验证项
     */
    public function getLoginOptions(array $data): array
    {
        $user = User::query()
            ->where('username', $data['username'])
            ->orWhere('email', $data['username'])
            ->orWhere('phone', $data['username'])
            ->first();

        if (!$user) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户不存在');
        }

        $options = [
            'username' => $user->username,
        ];

        $option = [];
        if ($user->email) {
            $option['email'] = $user->email;
        }

        if ($user->phone) {
            $option['phone'] = $user->phone;
        }

        if ($user->google2fa_secret) {
            $option['google2fa'] = env('APP_NAME');
        }

        if ($option) {
            $options['groups'][] = $option;
        }

        // 通行密钥
        $passkeys = UserPasskey::query()
            ->select('id', 'credential_id', 'device_info', 'created_at', 'updated_at')
            ->where('user_id', $user->id)
            ->get();
        if ($passkeys) {
            $options['groups'][] = ['passkeys' => $passkeys];
        }

        return $options;
    }

    /**
     * 单一密码验证接口
     */
    public function verifyPassword(array $data): void
    {
        $user = $this->getUser();

        if (!password_verify($data['password'], $user->password)) {
            throw new BusinessException(ResultCode::FORBIDDEN, '密码错误');
        }

        return;
    }

    /**
     * IP 归属地上报（由客户端上报，省去后端请求付费接口）
     */
    public function reportIpLocation(array $data): void
    {
        $ip = $this->deviceService->getClientIp();

        // 保存到 Redis 中
        $this->redis->set('ip_location_' . $ip, $data, 3600 * 6);

        return;
    }

    /**
     * 注销账号
     */
    public function delete(): void
    {
        $user = $this->getUser();
        // 注销不实际删除账号，且需要考虑用户再次使用相同的邮箱、手机号注册新账号的情况
        $user->account = '#' . $user->account . '_' . $user->id;
        $user->username = '#' . $user->username . '_' . $user->id;
        $user->display_name = '#' . $user->display_name . '_' . $user->id;
        $user->invite_code = '#' . $user->invite_code . '_' . $user->id;
        $user->email = '#' . $user->email . '_' . $user->id;
        $user->phone = '#' . $user->phone . '_' . $user->id;
        $user->status = Status::DELETED;
        $user->save();

        // 软删除
        $user->delete();
        return;
    }

    /**
     * 生成唯一邀请码
     */
    private function generateInviteCode(): string
    {
        return 'Y' . strtoupper(substr(md5(uniqid() . microtime(true)), 0, 6));
    }

    public function deviceName(string $deviceInfo = ''): string
    {
        if (!$deviceInfo) {
            $deviceInfo = $this->request->getHeaderLine('x-device-info');
        }
        $deviceName = preg_replace('/[^a-zA-Z0-9]/', '', $deviceInfo);
        return $deviceName;
    }
}
