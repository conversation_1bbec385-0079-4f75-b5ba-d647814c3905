<?php

declare(strict_types=1);
/**
 * ArticleService
 * Author:<PERSON>.Mr
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-01
 * Website:xxx
 */

namespace App\Http\Api\Service\Article;

use App\Http\Api\Request\Article\ArticleRequest;
use App\Model\Article\Article;
use App\Model\Article\DynamicsCurrency;
use App\QueryBuilder\QueryBuilder;
use App\Repository\Article\ArticleRepository;
use Hyperf\Di\Annotation\Inject;

class ArticleService
{

    #[Inject]
    protected ArticleRepository $repository;
    /**
     * 通过分类查询资讯列表
     * Summary of list
     * @return array
     */
    public function list(ArticleRequest $request)
    {
        return QueryBuilder::for(Article::class, $request)
                ->where('category_id', $request->input('category_id'))
                ->orderByDesc('id')
                ->allowedSorts(['id', 'created_at'])
                ->pagex(function($item){
                    if($item->currency){
                        $item->currencyList = DynamicsCurrency::query()->whereIn('symbol_id',$item->currency)->get();
                    }else{
                        $item->currencyList = [];
                    }
                    return $item;
                });
    }

    /**
     * 获取详情
     * Summary of detail
     * @param mixed $id
     */
    public function detail($id) {
        return $this->repository->findById($id);
    }
}
