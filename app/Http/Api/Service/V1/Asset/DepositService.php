<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Asset;

use App\Enum\CurrencyConfigKey;
use App\Exception\BusinessException;
use App\Http\Api\Service\BaseService;
use App\Http\Common\ResultCode;
use App\Model\Currency\CurrencyTransfer;
use App\Service\Wallet\DepositAddressService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

class DepositService extends BaseService
{
    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected DepositAddressService $depositAddressService;

    /**
     * 获取支持充值的币种
     */
    public function getSupportedCurrencies(): array
    {
        try {
            // 使用JOIN查询避免在循环中读取Redis币种信息
            $currencies = CurrencyTransfer::query()
                ->select([
                    'currency_transfer.currency_id',
                    'currency_transfer.chains',
                    'currency.base_asset',
                    'currency_mate.logo'
                ])
                ->leftJoin('currency', 'currency_transfer.currency_id', '=', 'currency.id')
                ->leftJoin('currency_mate', 'currency_transfer.currency_id', '=', 'currency_mate.currency_id')
                ->where('currency_transfer.transfer', 1)
                ->where('currency.status',1)
                ->get();

            $result = [];
            foreach ($currencies as $currency) {
                $result[] = [
                    'currency_id' => $currency['currency_id'],
                    'base_asset' => $currency['base_asset'],
                    'logo' => $currency['logo'],
                    'chains' => $currency['chains'], // 模型自动转换为数组
                ];
            }

            return $result;

        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '获取支持充值币种失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取或生成充值地址
     */
    public function getOrCreateDepositAddress(int $currencyId, string $chainId): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        try {
            // 验证币种和链的支持情况
            $this->validateCurrencyAndChain($currencyId, $chainId);

            // 获取或生成充值地址
            $addressData = $this->depositAddressService->getOrCreateDepositAddress($userId, $chainId);

            // 获取链配置信息
            $chainConfig = $this->getChainConfig($currencyId, $chainId);

            return [
                'address' => $addressData['address'],
                'memo' => $addressData['memo'] ?? '',
                'chain_id' => $chainId,
                'chain_name' => $chainConfig['chain'] ?? '',
                'min_deposit' => $chainConfig['minDepositAmount'] ?? '0',
                'confirmations' => $chainConfig['depositConfirm'] ?? 0,
                'contract_address' => $chainConfig['contractAddress'] ?? null,
            ];

        } catch (\Exception $e) {
            if ($e instanceof BusinessException) {
                throw $e;
            }
            throw new BusinessException(ResultCode::FAIL, '获取充值地址失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证币种和链的支持情况
     */
    private function validateCurrencyAndChain(int $currencyId, string $chainId): void
    {
        $currencyTransfer = CurrencyTransfer::where('currency_id', $currencyId)
            ->where('transfer', 1)
            ->first();

        if (!$currencyTransfer) {
            throw new BusinessException(ResultCode::FAIL, '该币种不支持充值');
        }

        // 检查链是否支持
        $chains = $currencyTransfer->chains;
        $supportedChains = array_column($chains, 'chain');

        if (!in_array($chainId, $supportedChains)) {
            throw new BusinessException(ResultCode::FAIL, '该币种不支持此链');
        }
    }

    /**
     * 获取链配置信息
     */
    private function getChainConfig(int $currencyId, string $chainId): array
    {
        $currencyTransfer = CurrencyTransfer::where('currency_id', $currencyId)
            ->where('transfer', 1)
            ->first();

        if (!$currencyTransfer) {
            return [];
        }

        $chains = $currencyTransfer->chains;
        foreach ($chains as $chain) {
            if ($chain['chain'] === $chainId) {
                return $chain;
            }
        }

        return [];
    }

    /**
     * 获取币种信息
     */
    private function getCurrencyInfo(int $currencyId): array
    {
        try {
            $currencyKey = CurrencyConfigKey::getCurrencyKey($currencyId);
            $symbol = $this->redis->hGet($currencyKey, 'symbol') ?: '';
            $logo = $this->redis->hGet($currencyKey, 'logo') ?: '';

            return [
                'symbol' => $symbol,
                'logo' => $logo,
            ];
        } catch (\Exception $e) {
            return [
                'symbol' => '',
                'logo' => '',
            ];
        }
    }

    /**
     * 获取用户充值地址列表
     */
    public function getUserDepositAddresses(): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        try {
            return $this->depositAddressService->getUserAllAddresses($userId);
        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '获取充值地址列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证地址格式
     */
    public function validateAddress(string $address, string $chainId): bool
    {
        try {
            return $this->depositAddressService->validateAddress($address, $chainId);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取支持的链配置
     */
    public function getSupportedChains(): array
    {
        // 返回支持的链列表，可以从配置或数据库获取
        return [
            'btc' => ['name' => 'Bitcoin', 'symbol' => 'BTC'],
            'eth' => ['name' => 'Ethereum', 'symbol' => 'ETH'],
            'bsc' => ['name' => 'BSC', 'symbol' => 'BNB'],
            'tron' => ['name' => 'Tron', 'symbol' => 'TRX'],
            'polygon' => ['name' => 'Polygon', 'symbol' => 'MATIC'],
            'arbitrum' => ['name' => 'Arbitrum', 'symbol' => 'ETH'],
            'optimism' => ['name' => 'Optimism', 'symbol' => 'ETH'],
        ];
    }
}
