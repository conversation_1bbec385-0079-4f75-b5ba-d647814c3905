<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Asset;

use App\Exception\BusinessException;
use App\Http\Api\Service\BaseService;
use App\Http\Common\ResultCode;
use App\Model\Currency\Currency;
use App\Model\Enums\Asset\TransferStatus;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\User\UserAccountsAsset;
use App\Model\User\UserTransferRecord;
use App\Service\UserAccounts\UserAccountsAssetService;
use App\Enum\CurrencyConfigKey;
use Hyperf\Contract\TranslatorInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

class TransferService extends BaseService
{
    #[Inject]
    protected UserAccountsAssetService $assetService;

    #[Inject]
    protected TranslatorInterface $translator;

    #[Inject]
    protected Redis $redis;

    /**
     * 获取指定账户类型有余额的币种列表
     */
    public function getAccountCurrencies(int $accountType): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        try {
            // 使用JOIN查询避免在循环中读取Redis币种信息
            $selectFields = [
                'user_accounts_assets.currency_id',
                'user_accounts_assets.available',
                'currency.base_asset as symbol',
                'currency_mate.logo'
            ];

            // 如果是逐仓账户，需要额外获取margin_quote字段
            if ($accountType === AccountType::ISOLATED->value) {
                $selectFields[] = 'user_accounts_assets.margin_quote';
            }

            $assets = UserAccountsAsset::query()
                ->select($selectFields)
                ->leftJoin('currency', 'user_accounts_assets.currency_id', '=', 'currency.id')
                ->leftJoin('currency_mate', 'user_accounts_assets.currency_id', '=', 'currency_mate.currency_id')
                ->where('user_accounts_assets.user_id', $userId)
                ->where('user_accounts_assets.account_type', $accountType)
                ->where('user_accounts_assets.available', '>', 0)
                ->where('user_accounts_assets.status', 1)
                ->where('currency.status', 1)
                ->get();

            $result = [];
            foreach ($assets as $asset) {
                $currencyData = [
                    'currency_id' => $asset['currency_id'],
                    'symbol' => $asset['symbol'],
                    'logo' => $asset['logo'],
                    'available' => number_format($asset['available'], 8, '.', ''),
                ];

                // 如果是逐仓账户，添加margin_quote字段
                if ($accountType === AccountType::ISOLATED->value) {
                    $currencyData['margin_quote'] = number_format($asset['margin_quote'] ?? 0, 8, '.', '');
                }

                $result[] = $currencyData;
            }

            return $result;

        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '获取账户币种列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 执行资金划转
     */
    public function transfer(array $data): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        try {
            // 验证划转参数
            $this->validateTransferData($data);

            // 检查余额是否充足
            $this->checkBalance($userId, $data);

            // 创建划转记录
            $transferRecord = $this->createTransferRecord($userId, $data);

            // 执行资金划转
            $this->processTransfer($userId, $data, $transferRecord->getId());

            // 更新记录状态为成功
            $transferRecord->setStatus(TransferStatus::SUCCESS->value);
            $transferRecord->save();

            return [
                'transfer_id' => $transferRecord->getId(),
                'from_account_type' => $transferRecord->getFromAccountType(),
                'to_account_type' => $transferRecord->getToAccountType(),
                'currency_id' => $transferRecord->getCurrencyId(),
                'amount' => number_format($transferRecord->getAmount(), 8, '.', ''),
                'status' => $transferRecord->getStatus(),
                'created_at' => $transferRecord->getCreatedAt()->format('Y-m-d H:i:s'),
            ];

        } catch (\Exception $e) {
            if ($e instanceof BusinessException) {
                throw $e;
            }
            throw new BusinessException(ResultCode::FAIL, '资金划转失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取划转记录
     */
    public function getTransferRecords(array $filters = []): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        try {
            // 使用JOIN查询获取币种信息
            $query = UserTransferRecord::query()
                ->select([
                    'user_transfer_records.id',
                    'user_transfer_records.currency_id',
                    'user_transfer_records.from_account_type',
                    'user_transfer_records.to_account_type',
                    'user_transfer_records.amount',
                    'user_transfer_records.status',
                    'user_transfer_records.remark',
                    'user_transfer_records.isolated',
                    'user_transfer_records.created_at',
                    'currency.base_asset as symbol'
                ])
                ->join('currency', 'user_transfer_records.currency_id', '=', 'currency.id')
                ->where('user_transfer_records.user_id', $userId);

            // 应用筛选条件
            if (!empty($filters['currency_id'])) {
                $query->where('user_transfer_records.currency_id', $filters['currency_id']);
            }

            if (!empty($filters['from_account_type'])) {
                $query->where('user_transfer_records.from_account_type', $filters['from_account_type']);
            }

            if (!empty($filters['to_account_type'])) {
                $query->where('user_transfer_records.to_account_type', $filters['to_account_type']);
            }

            if (!empty($filters['status'])) {
                $query->where('user_transfer_records.status', $filters['status']);
            }

            if (!empty($filters['start_time'])) {
                $query->where('user_transfer_records.created_at', '>=', date('Y-m-d H:i:s', $filters['start_time']));
            }

            if (!empty($filters['end_time'])) {
                $query->where('user_transfer_records.created_at', '<=', date('Y-m-d H:i:s', $filters['end_time']));
            }

            // 分页参数
            $page = $filters['page'] ?? 1;
            $limit = min($filters['limit'] ?? 20, 100);
            $offset = ($page - 1) * $limit;

            // 获取总数
            $total = $query->count();

            // 获取数据
            $records = $query->orderBy('user_transfer_records.created_at', 'desc')
                ->offset($offset)
                ->limit($limit)
                ->get();

            $data = [];
            foreach ($records as $record) {
                $recordData = [
                    'id' => $record['id'],
                    'currency_id' => $record['currency_id'],
                    'symbol' => $record['symbol'],
                    'from_account_type' => $record['from_account_type'],
                    'from_account_name' => $this->getAccountTypeName($record['from_account_type']),
                    'to_account_type' => $record['to_account_type'],
                    'to_account_name' => $this->getAccountTypeName($record['to_account_type']),
                    'amount' => number_format($record['amount'], 8, '.', ''),
                    'status' => $record['status'],
                    'status_name' => TransferStatus::from($record['status'])->getName(),
                    'remark' => $record['remark'] ?? '',
                    'created_at' => $record['created_at']->format('Y-m-d H:i:s'),
                ];

                // 添加逐仓资产类型信息
                if ($record['isolated'] !== null) {
                    $recordData['isolated'] = $record['isolated'];
                    $recordData['isolated_asset_type'] = $record['isolated'] === 1 ? 'quote' : 'base';
                    $recordData['isolated_asset_name'] = $record['isolated'] === 1 ? '计价币' : '基础币';
                }

                $data[] = $recordData;
            }

            return [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'last_page' => ceil($total / $limit),
                'data' => $data,
            ];

        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '获取划转记录失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取资金账户类型列表
     */
    public function getAccountTypes(string $lang = 'zh-CN'): array
    {
        try {
            $accountTypes = [];

            foreach (AccountType::cases() as $accountType) {
                $accountTypes[$accountType->value] = $this->getAccountTypeNameByLang($accountType->value, $lang);
            }

            return $accountTypes;

        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '获取账户类型列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证划转数据
     */
    private function validateTransferData(array $data): void
    {
        if (empty($data['currency_id']) || !is_numeric($data['currency_id'])) {
            throw new BusinessException(ResultCode::FAIL, '币种ID不能为空');
        }

        if (!isset($data['from_account_type']) || !is_numeric($data['from_account_type'])) {
            throw new BusinessException(ResultCode::FAIL, '转出账户类型不能为空');
        }

        if (!isset($data['to_account_type']) || !is_numeric($data['to_account_type'])) {
            throw new BusinessException(ResultCode::FAIL, '转入账户类型不能为空');
        }

        if ($data['from_account_type'] === $data['to_account_type']) {
            throw new BusinessException(ResultCode::FAIL, '转出和转入账户类型不能相同');
        }

        if (empty($data['amount']) || !is_numeric($data['amount']) || $data['amount'] <= 0) {
            throw new BusinessException(ResultCode::FAIL, '划转金额必须大于0');
        }

        // 验证账户类型是否有效
        $validAccountTypes = array_map(fn($case) => $case->value, AccountType::cases());

        if (!in_array($data['from_account_type'], $validAccountTypes)) {
            throw new BusinessException(ResultCode::FAIL, '转出账户类型无效');
        }

        if (!in_array($data['to_account_type'], $validAccountTypes)) {
            throw new BusinessException(ResultCode::FAIL, '转入账户类型无效');
        }

        // 验证逐仓划转参数
        $this->validateIsolatedTransfer($data);
    }

    /**
     * 检查余额是否充足
     */
    private function checkBalance(int $userId, array $data): void
    {
        // 确定要检查的币种ID
        $checkCurrencyId = $data['currency_id'];

        // 如果是向逐仓转入计价币，currency_id就是计价币ID，直接检查
        // 其他情况都使用currency_id检查

        $asset = UserAccountsAsset::where(UserAccountsAsset::FIELD_USER_ID, $userId)
            ->where(UserAccountsAsset::FIELD_ACCOUNT_TYPE, $data['from_account_type'])
            ->where(UserAccountsAsset::FIELD_CURRENCY_ID, $checkCurrencyId)
            ->where(UserAccountsAsset::FIELD_STATUS, 1)
            ->first();

        if (!$asset) {
            throw new BusinessException(ResultCode::FAIL, '转出账户不存在或未激活');
        }

        // 检查余额
        if ($data['from_account_type'] === AccountType::ISOLATED->value && !empty($data['isolated_asset_type'])) {
            // 逐仓账户特殊处理
            if ($data['isolated_asset_type'] === 'quote') {
                // 检查计价币余额(margin_quote字段)
                if (bccomp((string)$asset->getMarginQuote(), (string)$data['amount'], 8) < 0) {
                    throw new BusinessException(ResultCode::FAIL, '转出账户计价币余额不足');
                }
            } else {
                // 检查基础币余额(available字段)
                if (bccomp((string)$asset->getAvailable(), (string)$data['amount'], 8) < 0) {
                    throw new BusinessException(ResultCode::FAIL, '转出账户基础币余额不足');
                }
            }
        } else {
            // 普通账户检查available余额
            if (bccomp((string)$asset->getAvailable(), (string)$data['amount'], 8) < 0) {
                throw new BusinessException(ResultCode::FAIL, '转出账户余额不足');
            }
        }
    }

    /**
     * 创建划转记录
     */
    private function createTransferRecord(int $userId, array $data): UserTransferRecord
    {
        $record = new UserTransferRecord();
        $record->setUserId($userId);
        $record->setCurrencyId($data['currency_id']);
        $record->setFromAccountType($data['from_account_type']);
        $record->setToAccountType($data['to_account_type']);
        $record->setAmount($data['amount']);
        $record->setStatus(TransferStatus::PROCESSING->value);
        $record->setRemark($data['remark'] ?? '');

        // 设置逐仓资产类型
        $this->setIsolatedType($record, $data);

        if (!$record->save()) {
            throw new BusinessException(ResultCode::FAIL, '创建划转记录失败');
        }

        return $record;
    }

    /**
     * 执行资金划转
     */
    private function processTransfer(int $userId, array $data, int $transferId): void
    {
        try {
            // 检查是否涉及逐仓账户的特殊处理
            $isFromIsolated = $data['from_account_type'] === AccountType::ISOLATED->value && !empty($data['isolated_asset_type']);
            $isToIsolated = $data['to_account_type'] === AccountType::ISOLATED->value && !empty($data['isolated_asset_type']);

            if ($isFromIsolated || $isToIsolated) {
                // 逐仓账户特殊处理
                $this->processIsolatedTransfer($userId, $data, $transferId);
            } else {
                // 普通账户划转
                $this->processNormalTransfer($userId, $data, $transferId);
            }

        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '资金划转处理失败: ' . $e->getMessage());
        }
    }

    /**
     * 处理普通账户划转
     */
    private function processNormalTransfer(int $userId, array $data, int $transferId): void
    {
        // 1. 从转出账户扣除资金
        $this->assetService->deductAvailableAsset(
            $userId,
            $data['from_account_type'],
            $data['currency_id'],
            $data['amount'],
            FlowsType::TRANSFER_OUT->value,
            $transferId
        );

        // 2. 向转入账户增加资金
        $this->assetService->addAvailableAsset(
            $userId,
            $data['to_account_type'],
            $data['currency_id'],
            $data['amount'],
            FlowsType::TRANSFER_IN->value,
            $transferId
        );
    }

    /**
     * 处理逐仓账户划转
     */
    private function processIsolatedTransfer(int $userId, array $data, int $transferId): void
    {
        $isFromIsolated = $data['from_account_type'] === AccountType::ISOLATED->value;
        $isToIsolated = $data['to_account_type'] === AccountType::ISOLATED->value;
        $assetType = $data['isolated_asset_type'] ?? 'base';

        if ($isFromIsolated && $assetType === 'quote') {
            // 从逐仓转出计价币
            $this->processIsolatedQuoteOut($userId, $data, $transferId);
        } elseif ($isToIsolated && $assetType === 'quote') {
            // 向逐仓转入计价币
            $this->processIsolatedQuoteIn($userId, $data, $transferId);
        } else {
            // 逐仓基础币划转，使用普通流程
            $this->processNormalTransfer($userId, $data, $transferId);
        }
    }

    /**
     * 获取账户类型名称
     */
    private function getAccountTypeName(int $accountType): string
    {
        return $this->getAccountTypeNameByLang($accountType, 'zh-CN');
    }

    /**
     * 根据语言获取账户类型名称
     */
    private function getAccountTypeNameByLang(int $accountType, string $lang): string
    {
        // 转换语言代码格式
        $locale = $lang === 'en-US' ? 'en' : 'zh_CN';

        // 获取账户类型对应的翻译键
        $translationKey = match ($accountType) {
            AccountType::WALLET->value => 'account_type.wallet',
            AccountType::SPOT->value => 'account_type.spot',
            AccountType::FUTURES->value => 'account_type.futures',
            AccountType::MARGIN->value => 'account_type.margin',
            AccountType::ISOLATED->value => 'account_type.isolated',
            AccountType::CHAIN->value => 'account_type.chain',
            AccountType::COPY->value => 'account_type.copy',
            AccountType::FUNDING->value => 'account_type.funding',
            default => 'account_type.unknown',
        };

        return $this->translator->trans($translationKey, [], $locale);
    }

    /**
     * 验证逐仓划转参数
     */
    private function validateIsolatedTransfer(array $data): void
    {
        $isFromIsolated = $data['from_account_type'] === AccountType::ISOLATED->value;
        $isToIsolated = $data['to_account_type'] === AccountType::ISOLATED->value;

        // 如果涉及逐仓账户，必须指定资产类型
        if (($isFromIsolated || $isToIsolated) && empty($data['isolated_asset_type'])) {
            throw new BusinessException(ResultCode::FAIL, '逐仓账户划转必须指定资产类型(base/quote)');
        }

        // 验证资产类型值
        if (!empty($data['isolated_asset_type']) && !in_array($data['isolated_asset_type'], ['base', 'quote'])) {
            throw new BusinessException(ResultCode::FAIL, '逐仓资产类型只能是base或quote');
        }

        // 如果是向逐仓转入计价币，必须指定目标基础币种ID
        if ($isToIsolated && !empty($data['isolated_asset_type']) && $data['isolated_asset_type'] === 'quote') {
            if (empty($data['isolated_target_currency_id'])) {
                throw new BusinessException(ResultCode::FAIL, '向逐仓转入计价币时必须指定目标基础币种ID');
            }
        }

        // 如果是从逐仓转出基础币，currency_id就是基础币ID
        // 如果是从逐仓转出计价币，currency_id也是基础币ID，通过它获取计价币ID
        // 如果是向逐仓转入基础币，currency_id就是基础币ID
        // 如果是向逐仓转入计价币，isolated_target_currency_id是基础币ID，currency_id是计价币ID
    }

    /**
     * 处理从逐仓转出计价币
     */
    private function processIsolatedQuoteOut(int $userId, array $data, int $transferId): void
    {
        // 1. 从逐仓账户的margin_quote字段扣除资金
        $this->assetService->deductAvailableAsset(
            $userId,
            AccountType::ISOLATED->value,
            (int)$data['currency_id'],
            $data['amount'],
            FlowsType::TRANSFER_OUT->value,
            $transferId,
            'margin_quote'
        );

        // 2. 获取基础币对应的计价币ID
        $quoteCurrencyId = $this->getQuoteCurrencyId($data['currency_id']);

        // 3. 向转入账户增加计价币资金
        $this->assetService->addAvailableAsset(
            $userId,
            $data['to_account_type'],
            $quoteCurrencyId,
            $data['amount'],
            FlowsType::TRANSFER_IN->value,
            $transferId
        );
    }

    /**
     * 处理向逐仓转入计价币
     */
    private function processIsolatedQuoteIn(int $userId, array $data, int $transferId): void
    {
        // 1. 从转出账户扣除计价币资金（currency_id就是计价币ID）
        $this->assetService->deductAvailableAsset(
            $userId,
            $data['from_account_type'],
            $data['currency_id'],
            $data['amount'],
            FlowsType::TRANSFER_OUT->value,
            $transferId
        );

        // 2. 向逐仓账户的margin_quote字段增加资金（使用目标基础币ID）
        $this->assetService->addAvailableAsset(
            $userId,
            AccountType::ISOLATED->value,
            (int)$data['isolated_target_currency_id'],
            $data['amount'],
            FlowsType::TRANSFER_IN->value,
            $transferId,
            'margin_quote'
        );
    }

    /**
     * 获取基础币对应的计价币ID
     */
    private function getQuoteCurrencyId(int $baseCurrencyId): int
    {
        try {
            // 从Redis获取币种配置
            $currencyKey = CurrencyConfigKey::getCurrencyKey($baseCurrencyId);
            $quoteCurrencyId = $this->redis->hGet($currencyKey, Currency::FIELD_QUOTE_ASSETS_ID);

            if ($quoteCurrencyId === false || $quoteCurrencyId === null) {
                throw new BusinessException(ResultCode::FAIL, "未找到币种 {$baseCurrencyId} 的计价币种配置");
            }

            return (int)$quoteCurrencyId;

        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, "获取计价币种ID失败: " . $e->getMessage());
        }
    }

    /**
     * 设置逐仓资产类型
     */
    private function setIsolatedType(UserTransferRecord $record, array $data): void
    {
        $isFromIsolated = $data['from_account_type'] === AccountType::ISOLATED->value;
        $isToIsolated = $data['to_account_type'] === AccountType::ISOLATED->value;

        // 只有涉及逐仓账户时才设置isolated字段
        if ($isFromIsolated || $isToIsolated) {
            if (!empty($data['isolated_asset_type'])) {
                $isolatedValue = $data['isolated_asset_type'] === 'quote' ? 1 : 0;
                $record->setIsolated($isolatedValue);
            }
        }
        // 非逐仓划转不设置isolated字段（保持null）
    }
}
