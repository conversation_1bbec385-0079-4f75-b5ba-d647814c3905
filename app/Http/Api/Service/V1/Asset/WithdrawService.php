<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Asset;

use App\Enum\CurrencyConfigKey;
use App\Exception\BusinessException;
use App\Http\Api\Service\BaseService;
use App\Http\Common\ResultCode;
use App\Model\Currency\CurrencyTransfer;
use App\Model\Enums\Asset\WithdrawStatus;
use App\Model\Enums\Asset\WithdrawType;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\User\User;
use App\Model\User\UserAccountsAsset;
use App\Model\User\UserCustomWithdrawAddress;
use App\Model\User\UserWithdrawRecord;
use App\Service\UserAccounts\UserAccountsAssetService;
use Carbon\Carbon;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

class WithdrawService extends BaseService
{
    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected UserAccountsAssetService $assetService;

    /**
     * 获取可提币种
     */
    public function getAvailableCurrencies(): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        try {
            // 使用JOIN查询避免在循环中读取Redis币种信息
            $assets = UserAccountsAsset::query()
                ->select([
                    'user_accounts_assets.currency_id',
                    'user_accounts_assets.available',
                    'user_accounts_assets.frozen',
                    'currency.base_asset',
                    'currency_mate.logo'
                ])
                ->leftJoin('currency', 'user_accounts_assets.currency_id', '=', 'currency.id')
                ->leftJoin('currency_mate', 'user_accounts_assets.currency_id', '=', 'currency_mate.currency_id')
                ->where('user_accounts_assets.user_id', $userId)
                ->where('user_accounts_assets.account_type', AccountType::WALLET->value)
                ->where('user_accounts_assets.available', '>', 0)
                ->where('user_accounts_assets.status', 1)
                ->get();

            $result = [];
            foreach ($assets as $asset) {
                $result[] = [
                    'currency_id' => $asset['currency_id'],
                    'base_asset' => $asset['base_asset'],
                    'logo' => $asset['logo'],
                    'available_balance' => number_format($asset['available'], 8, '.', ''),
                    'frozen_balance' => number_format($asset['frozen'], 8, '.', ''),
                    'total_balance' => number_format($asset['available'] + $asset['frozen'], 8, '.', ''),
                ];
            }

            return $result;

        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '获取可提币种失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取提币配置
     */
    public function getWithdrawConfig(int $currencyId): array
    {
        try {
            $currencyTransfer = CurrencyTransfer::where('currency_id', $currencyId)
                ->first();

            if (!$currencyTransfer) {
                throw new BusinessException(ResultCode::FAIL, '币种配置不存在');
            }

            $currencyInfo = $this->getCurrencyInfo($currencyId);

            return [
                'currency_id' => $currencyId,
                'base_asset' => $currencyTransfer->base_asset,
                'logo' => $currencyInfo['logo'],
                'chains' => $currencyTransfer->chains ?? [],
            ];

        } catch (\Exception $e) {
            if ($e instanceof BusinessException) {
                throw $e;
            }
            throw new BusinessException(ResultCode::FAIL, '获取提币配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 提交提币申请
     */
    public function submitWithdraw(array $data): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        try {
            // 验证提币请求
            $this->validateWithdrawRequest($userId, $data);

            // 计算手续费
            $fee = $this->calculateWithdrawFee($data);

            // 检查余额是否充足
            $this->checkBalance($userId, $data['currency_id'], $data['amount'], $fee);

            // 创建提币记录
            $withdrawRecord = $this->createWithdrawRecord($userId, $data, $fee);

            // 处理资金变动
            $withdrawType = WithdrawType::from($data['withdraw_type']);
            if ($withdrawType->isInternal()) {
                // 内部转账：直接转移资金
                $this->processInternalTransfer($withdrawRecord, $data);
            } else {
                // 链上提币：冻结资金
                $this->freezeWithdrawAmount($userId, $data['currency_id'], $data['amount'], $withdrawRecord->getId());
            }

            return [
                'withdraw_id' => $withdrawRecord->getId(),
                'currency_id' => $withdrawRecord->getCurrencyId(),
                'base_asset' => $this->getCurrencyInfo($withdrawRecord->getCurrencyId())['symbol'],
                'withdraw_type' => $withdrawRecord->getWithdrawType(),
                'withdraw_type_name' => WithdrawType::from($withdrawRecord->getWithdrawType())->getName(),
                'amount' => number_format($withdrawRecord->getAmount(), 8, '.', ''),
                'fee' => number_format($withdrawRecord->getFee(), 8, '.', ''),
                'actual_amount' => number_format($withdrawRecord->getActualAmount(), 8, '.', ''),
                'status' => $withdrawRecord->getStatus(),
                'status_name' => WithdrawStatus::from($withdrawRecord->getStatus())->getName(),
                'created_at' => $withdrawRecord->getCreatedAt()->format('Y-m-d H:i:s'),
            ];

        } catch (\Exception $e) {
            if ($e instanceof BusinessException) {
                throw $e;
            }
            throw new BusinessException(ResultCode::FAIL, '提交提币申请失败: ' . $e->getMessage());
        }
    }

    /**
     * 查询提币记录
     */
    public function getWithdrawRecords(array $filters = []): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        try {
            $query = UserWithdrawRecord::where(UserWithdrawRecord::FIELD_USER_ID, $userId);

            // 应用筛选条件
            if (!empty($filters['currency_id'])) {
                $query->where(UserWithdrawRecord::FIELD_CURRENCY_ID, $filters['currency_id']);
            }

            if (!empty($filters['withdraw_type'])) {
                $query->where(UserWithdrawRecord::FIELD_WITHDRAW_TYPE, $filters['withdraw_type']);
            }

            if (!empty($filters['status'])) {
                $query->where(UserWithdrawRecord::FIELD_STATUS, $filters['status']);
            }

            if (!empty($filters['start_time'])) {
                $query->where(UserWithdrawRecord::FIELD_CREATED_AT, '>=', date('Y-m-d H:i:s', $filters['start_time']));
            }

            if (!empty($filters['end_time'])) {
                $query->where(UserWithdrawRecord::FIELD_CREATED_AT, '<=', date('Y-m-d H:i:s', $filters['end_time']));
            }

            // 分页参数
            $page = $filters['page'] ?? 1;
            $limit = min($filters['limit'] ?? 20, 100);
            $offset = ($page - 1) * $limit;

            // 获取总数
            $total = $query->count();

            // 获取数据
            $records = $query->orderBy(UserWithdrawRecord::FIELD_CREATED_AT, 'desc')
                ->offset($offset)
                ->limit($limit)
                ->get();

            $data = [];
            foreach ($records as $record) {
                $currencyInfo = $this->getCurrencyInfo($record->getCurrencyId());

                $item = [
                    'id' => $record->getId(),
                    'currency_id' => $record->getCurrencyId(),
                    'base_asset' => $currencyInfo['symbol'],
                    'logo' => $currencyInfo['logo'],
                    'withdraw_type' => $record->getWithdrawType(),
                    'withdraw_type_name' => WithdrawType::from($record->getWithdrawType())->getName(),
                    'amount' => number_format($record->getAmount(), 8, '.', ''),
                    'fee' => number_format($record->getFee(), 8, '.', ''),
                    'actual_amount' => number_format($record->getActualAmount(), 8, '.', ''),
                    'status' => $record->getStatus(),
                    'status_name' => WithdrawStatus::from($record->getStatus())->getName(),
                    'created_at' => $record->getCreatedAt()->format('Y-m-d H:i:s'),
                    'completed_at' => $record->getCompletedAt() ? $record->getCompletedAt()->format('Y-m-d H:i:s') : null,
                ];

                // 根据提币类型添加特定字段
                if ($record->getWithdrawType() === WithdrawType::CHAIN->value) {
                    $item['chain_id'] = $record->getChainId();
                    $item['to_address'] = $record->getToAddress();
                    $item['memo'] = $record->getMemo();
                    $item['tx_hash'] = $record->getTxHash();
                    $item['confirmations'] = $record->getConfirmations();
                } else {
                    $item['to_email'] = $record->getToEmail();
                    $item['to_phone'] = $record->getToPhone();
                    $item['to_uid'] = $record->getToUid();
                }

                $data[] = $item;
            }

            return [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'last_page' => ceil($total / $limit),
                'data' => $data,
            ];

        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '查询提币记录失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证提币请求
     */
    private function validateWithdrawRequest(int $userId, array $data): void
    {
        $withdrawType = WithdrawType::from($data['withdraw_type']);

        // 验证币种
        $currencyTransfer = CurrencyTransfer::where('currency_id', $data['currency_id'])->first();
        if (!$currencyTransfer) {
            throw new BusinessException(ResultCode::FAIL, '币种不存在');
        }

        // 验证提币类型特定参数
        if ($withdrawType === WithdrawType::CHAIN) {
            $this->validateChainWithdraw($data);
        } else {
            $this->validateInternalTransfer($withdrawType, $data);
        }
    }

    /**
     * 验证链上提币
     */
    private function validateChainWithdraw(array $data): void
    {
        if (empty($data['chain_id']) || empty($data['to_address'])) {
            throw new BusinessException(ResultCode::FAIL, '链上提币参数不完整');
        }

        // 验证地址格式
        // TODO: 根据链ID验证地址格式
    }

    /**
     * 验证内部转账
     */
    private function validateInternalTransfer(WithdrawType $withdrawType, array $data): void
    {
        $receiverUserId = $this->validateInternalReceiver($withdrawType, $data);
        if (!$receiverUserId) {
            throw new BusinessException(ResultCode::FAIL, '接收方用户不存在');
        }
    }

    /**
     * 验证内部转账接收方
     */
    private function validateInternalReceiver(WithdrawType $withdrawType, array $data): ?int
    {
        return match ($withdrawType) {
            WithdrawType::INTERNAL_EMAIL => $this->findUserByEmail($data['to_email']),
            WithdrawType::INTERNAL_PHONE => $this->findUserByPhone($data['to_phone']),
            WithdrawType::INTERNAL_UID => $this->findUserByUid($data['to_uid']),
            default => null,
        };
    }

    /**
     * 根据邮箱查找用户
     */
    private function findUserByEmail(string $email): ?int
    {
        $user = User::where('email', $email)->first();
        return $user ? $user->id : null;
    }

    /**
     * 根据手机号查找用户
     */
    private function findUserByPhone(string $phone): ?int
    {
        $user = User::where('phone', $phone)->first();
        return $user ? $user->id : null;
    }

    /**
     * 根据UID查找用户
     */
    private function findUserByUid(string $uid): ?int
    {
        $user = User::where('uid', $uid)->first();
        return $user ? $user->id : null;
    }

    /**
     * 计算提币手续费
     */
    private function calculateWithdrawFee(array $data): float
    {
        $withdrawType = WithdrawType::from($data['withdraw_type']);

        // 内部转账无手续费
        if ($withdrawType->isInternal()) {
            return 0.0;
        }

        // 链上提币手续费
        if ($withdrawType === WithdrawType::CHAIN) {
            return $this->getChainWithdrawFee($data['currency_id'], $data['chain_id']);
        }

        return 0.0;
    }

    /**
     * 获取链上提币手续费
     */
    private function getChainWithdrawFee(int $currencyId, string $chainId): float
    {
        try {
            $currencyTransfer = CurrencyTransfer::where('currency_id', $currencyId)->first();
            if (!$currencyTransfer) {
                return 0.0;
            }

            $chains = $currencyTransfer->chains ?? [];
            foreach ($chains as $chain) {
                if ($chain['chain_id'] === $chainId) {
                    return (float)($chain['withdraw_fee'] ?? 0);
                }
            }

            return 0.0;
        } catch (\Exception $e) {
            return 0.0;
        }
    }

    /**
     * 检查余额是否充足
     */
    private function checkBalance(int $userId, int $currencyId, float $amount, float $fee): void
    {
        $asset = UserAccountsAsset::where(UserAccountsAsset::FIELD_USER_ID, $userId)
            ->where(UserAccountsAsset::FIELD_ACCOUNT_TYPE, AccountType::WALLET->value)
            ->where(UserAccountsAsset::FIELD_CURRENCY_ID, $currencyId)
            ->first();

        if (!$asset) {
            throw new BusinessException(ResultCode::FAIL, '账户不存在');
        }

        $totalNeed = $amount + $fee;
        if ($asset->getAvailable() < $totalNeed) {
            throw new BusinessException(ResultCode::FAIL, '余额不足');
        }
    }

    /**
     * 创建提币记录
     */
    private function createWithdrawRecord(int $userId, array $data, float $fee): UserWithdrawRecord
    {
        $withdrawType = WithdrawType::from($data['withdraw_type']);
        $actualAmount = $data['amount'] - $fee;

        $record = new UserWithdrawRecord();
        $record->setUserId($userId);
        $record->setCurrencyId($data['currency_id']);
        $record->setWithdrawType($data['withdraw_type']);
        $record->setAmount($data['amount']);
        $record->setFee($fee);
        $record->setActualAmount($actualAmount);
        $record->setStatus(WithdrawStatus::PENDING->value);

        // 根据提币类型设置特定字段
        if ($withdrawType === WithdrawType::CHAIN) {
            $record->setChainId($data['chain_id']);
            $record->setToAddress($data['to_address']);
            $record->setMemo($data['memo'] ?? null);
        } else {
            $receiverUserId = $this->validateInternalReceiver($withdrawType, $data);
            $record->setToUserId($receiverUserId);

            if ($withdrawType === WithdrawType::INTERNAL_EMAIL) {
                $record->setToEmail($data['to_email']);
            } elseif ($withdrawType === WithdrawType::INTERNAL_PHONE) {
                $record->setToPhone($data['to_phone']);
                $record->setToPhoneCountryCode($data['to_phone_country_code'] ?? null);
            } elseif ($withdrawType === WithdrawType::INTERNAL_UID) {
                $record->setToUid($data['to_uid']);
            }
        }

        if (!$record->save()) {
            throw new BusinessException(ResultCode::FAIL, '创建提币记录失败');
        }

        return $record;
    }

    /**
     * 处理内部转账
     */
    private function processInternalTransfer(UserWithdrawRecord $record, array $data): void
    {
        $withdrawType = WithdrawType::from($record->getWithdrawType());
        $receiverUserId = $this->validateInternalReceiver($withdrawType, $data);

        if (!$receiverUserId) {
            throw new BusinessException(ResultCode::FAIL, '接收方用户不存在');
        }

        try {
            // 1. 从发送方扣除资金
            $this->assetService->deductAvailableAsset(
                $record->getUserId(),
                AccountType::WALLET->value,
                $record->getCurrencyId(),
                $record->getAmount(),
                FlowsType::INTERNAL_TRANSFER_OUT->value,
                $record->getId()
            );

            // 2. 向接收方增加资金
            $this->assetService->addAvailableAsset(
                $receiverUserId,
                AccountType::WALLET->value,
                $record->getCurrencyId(),
                $record->getActualAmount(),
                FlowsType::INTERNAL_TRANSFER_IN->value,
                $record->getId()
            );

            // 3. 更新记录状态为完成
            $record->setStatus(WithdrawStatus::COMPLETED->value);
            $record->setCompletedAt(Carbon::now());
            $record->save();

        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '内部转账处理失败: ' . $e->getMessage());
        }
    }

    /**
     * 冻结提币资金
     */
    private function freezeWithdrawAmount(int $userId, int $currencyId, float $amount, int $withdrawId): void
    {
        try {
            // 将可用资金转移到冻结中
            $this->assetService->freezeAsset(
                $userId,
                AccountType::WALLET->value,
                $currencyId,
                $amount,
                FlowsType::WITHDRAW->value,
                $withdrawId
            );
        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '冻结提币资金失败: ' . $e->getMessage());
        }
    }

    /**
     * 添加快速提币地址
     */
    public function addWithdrawAddress(array $data): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        try {
            // 验证参数
            $this->validateAddressData($data);

            // 检查地址是否已存在
            $existingAddress = UserCustomWithdrawAddress::where(UserCustomWithdrawAddress::FIELD_USER_ID, $userId)
                ->where(UserCustomWithdrawAddress::FIELD_CURRENCY_ID, $data['currency_id'])
                ->where(UserCustomWithdrawAddress::FIELD_CHAIN_ID, $data['chain_id'])
                ->where(UserCustomWithdrawAddress::FIELD_ADDRESS, $data['address'])
                ->first();

            if ($existingAddress) {
                throw new BusinessException(ResultCode::FAIL, '该地址已存在');
            }

            // 创建新地址记录
            $withdrawAddress = new UserCustomWithdrawAddress();
            $withdrawAddress->setUserId($userId);
            $withdrawAddress->setCurrencyId($data['currency_id']);
            $withdrawAddress->setChainId($data['chain_id']);
            $withdrawAddress->setAddress($data['address']);
            $withdrawAddress->setRemark($data['remark'] ?? '');

            if (!$withdrawAddress->save()) {
                throw new BusinessException(ResultCode::FAIL, '保存提币地址失败');
            }

            return [
                'id' => $withdrawAddress->getId(),
                'currency_id' => $withdrawAddress->getCurrencyId(),
                'chain_id' => $withdrawAddress->getChainId(),
                'address' => $withdrawAddress->getAddress(),
                'remark' => $withdrawAddress->getRemark(),
                'created_at' => $withdrawAddress->getCreatedAt()->format('Y-m-d H:i:s'),
            ];

        } catch (\Exception $e) {
            if ($e instanceof BusinessException) {
                throw $e;
            }
            throw new BusinessException(ResultCode::FAIL, '添加提币地址失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取用户快速提币地址列表
     */
    public function getWithdrawAddresses(int $currencyId = null): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        try {
            $query = UserCustomWithdrawAddress::where(UserCustomWithdrawAddress::FIELD_USER_ID, $userId);

            if ($currencyId) {
                $query->where(UserCustomWithdrawAddress::FIELD_CURRENCY_ID, $currencyId);
            }

            $addresses = $query->orderBy(UserCustomWithdrawAddress::FIELD_CREATED_AT, 'desc')->get();

            $result = [];
            foreach ($addresses as $address) {
                $result[] = [
                    'id' => $address->getId(),
                    'currency_id' => $address->getCurrencyId(),
                    'chain_id' => $address->getChainId(),
                    'address' => $address->getAddress(),
                    'remark' => $address->getRemark(),
                    'created_at' => $address->getCreatedAt()->format('Y-m-d H:i:s'),
                ];
            }

            return $result;

        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '获取提币地址列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除快速提币地址
     */
    public function deleteWithdrawAddress(int $addressId): bool
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        try {
            $address = UserCustomWithdrawAddress::where(UserCustomWithdrawAddress::FIELD_ID, $addressId)
                ->where(UserCustomWithdrawAddress::FIELD_USER_ID, $userId)
                ->first();

            if (!$address) {
                throw new BusinessException(ResultCode::FAIL, '提币地址不存在');
            }

            return $address->delete();

        } catch (\Exception $e) {
            if ($e instanceof BusinessException) {
                throw $e;
            }
            throw new BusinessException(ResultCode::FAIL, '删除提币地址失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证地址数据
     */
    private function validateAddressData(array $data): void
    {
        if (empty($data['currency_id']) || !is_numeric($data['currency_id'])) {
            throw new BusinessException(ResultCode::FAIL, '币种ID不能为空');
        }

        if (empty($data['chain_id'])) {
            throw new BusinessException(ResultCode::FAIL, '链ID不能为空');
        }

        if (empty($data['address'])) {
            throw new BusinessException(ResultCode::FAIL, '提币地址不能为空');
        }

        // 验证币种是否存在
        $currencyTransfer = CurrencyTransfer::where('currency_id', $data['currency_id'])->first();
        if (!$currencyTransfer) {
            throw new BusinessException(ResultCode::FAIL, '币种不存在');
        }

        // 验证地址格式
        if (!$this->validateAddressFormat($data['address'], $data['chain_id'])) {
            throw new BusinessException(ResultCode::FAIL, '地址格式不正确');
        }
    }

    /**
     * 验证地址格式
     */
    private function validateAddressFormat(string $address, string $chainId): bool
    {
        // 基本长度和格式检查
        switch (strtolower($chainId)) {
            case 'btc':
            case 'bitcoin':
                return preg_match('/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/', $address) === 1;
            case 'eth':
            case 'ethereum':
            case 'bsc':
            case 'polygon':
            case 'arbitrum':
            case 'optimism':
                return preg_match('/^0x[a-fA-F0-9]{40}$/', $address) === 1;
            case 'tron':
            case 'trx':
                return preg_match('/^T[A-Za-z1-9]{33}$/', $address) === 1;
            default:
                return strlen($address) > 10; // 基本长度检查
        }
    }

    /**
     * 获取币种信息
     */
    private function getCurrencyInfo(int $currencyId): array
    {
        try {
            $currencyKey = CurrencyConfigKey::getCurrencyKey($currencyId);
            $symbol = $this->redis->hGet($currencyKey, 'symbol') ?: '';
            $logo = $this->redis->hGet($currencyKey, 'logo') ?: '';

            return [
                'symbol' => $symbol,
                'logo' => $logo,
            ];
        } catch (\Exception $e) {
            return [
                'symbol' => '',
                'logo' => '',
            ];
        }
    }
}
