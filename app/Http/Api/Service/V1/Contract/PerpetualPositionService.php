<?php

declare(strict_types=1);
/**
 * 永续合约仓位服务
 */

namespace App\Http\Api\Service\V1\Contract;

use App\Model\Trade\TradePerpetualPosition;
use App\Model\Trade\TradePerpetualLiquidation;
use App\Model\Trade\TradePerpetualAdl;
use App\Model\Trade\TradeMarginLevel;
use App\Model\Currency\Currency;
use App\Model\Enums\Trade\Perpetual\PositionStatus;
use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Service\UserAccounts\UserAccountsAssetService;
use App\Model\Enums\User\FlowsType;
use App\Model\Enums\User\AccountType;
use App\Http\Api\Event\Perpetual\PerpetualMarginAdjusted;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Hyperf\DbConnection\Db;
use Hyperf\Context\ApplicationContext;
use Psr\EventDispatcher\EventDispatcherInterface;

class PerpetualPositionService
{
    #[Inject]
    protected TradePerpetualPosition $tradePerpetualPosition;

    #[Inject]
    protected TradePerpetualLiquidation $tradePerpetualLiquidation;

    #[Inject]
    protected TradePerpetualAdl $tradePerpetualAdl;

    #[Inject]
    protected TradeMarginLevel $tradeMarginLevel;

    #[Inject]
    protected Currency $currency;

    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected UserAccountsAssetService $userAccountsAssetService;

    /**
     * 获取用户仓位列表
     */
    public function getPositionList(int $userId, ?int $currencyId = null, ?int $marginMode = null, ?int $side = null,$status = 1, int $perPage = 20, int $page = 1): array
    {
        // 构建查询条件
        $query = $this->tradePerpetualPosition->query()
            ->select([
                'trade_perpetual_position.*',
                'currency.symbol'
            ])
            ->leftJoin('currency', 'trade_perpetual_position.currency_id', '=', 'currency.id')
            ->where('trade_perpetual_position.user_id', $userId); 

            if($status != PositionStatus::HOLDING->value) {
                $query->where('trade_perpetual_position.status','!=', PositionStatus::HOLDING->value);
            }else{
                $query->where('trade_perpetual_position.status', $status);
            }
            $query->orderBy('trade_perpetual_position.updated_at', 'desc');

        // 添加筛选条件
        if ($currencyId) {
            $query->where('trade_perpetual_position.currency_id', $currencyId);
        }

        if ($marginMode) {
            $query->where('trade_perpetual_position.margin_mode', $marginMode);
        }

        if ($side) {
            $query->where('trade_perpetual_position.side', $side);
        }

        // 获取总数
        $total = $query->count();

        // 分页查询
        $positions = $query->offset(($page - 1) * $perPage)
            ->limit($perPage)
            ->get();

        // 格式化数据
        $data = $positions->map(function ($position) {
            return $this->formatPositionData($position);
        })->toArray();

        return [
            'data' => $data,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage)
        ];
    }

    /**
     * 格式化仓位数据
     */
    private function formatPositionData($position): array
    {
        return [
            'position_id' => $position->id,
            'symbol' => $position->symbol ?? 'UNKNOWN',
            'currency_id' => $position->currency_id,
            'margin_mode' => $position->margin_mode,
            'side' => $position->side,
            'quantity' => $position->quantity,
            'available_quantity' => $position->available_quantity,
            'frozen_quantity' => $position->frozen_quantity,
            'entry_price' => $position->entry_price,
            'margin_amount' => $position->margin_amount,
            'initial_margin' => $position->initial_margin,
            'maintenance_margin' => $position->maintenance_margin,
            'realized_pnl' => $position->realized_pnl,
            'leverage' => $position->leverage,
            'auto_add_margin' => $position->auto_add_margin,
            'status' => $position->status,
            'created_at' => $position->created_at,
            'updated_at' => $position->updated_at
        ];
    }

    /**
     * 获取仓位详情
     */
    public function getPositionDetail(int $userId, int $currencyId, int $marginMode): array
    {
        // TODO: 实现仓位详情查询逻辑
        return [
            'symbol' => 'BTCUSDT',
            'position_amt' => '0',
            'entry_price' => '0',
            'mark_price' => '0',
            'unreal_pnl' => '0',
            'liquidation_price' => '0',
            'leverage' => '20',
            'max_notional_value' => '0',
            'margin_type' => $marginMode == 1 ? 'cross' : 'isolated',
            'isolated_margin' => '0',
            'is_auto_add_margin' => false,
            'position_side' => 'BOTH',
            'notional' => '0',
            'isolated_wallet' => '0',
            'update_time' => time() * 1000,
        ];
    }

    /**
     * 调整保证金
     */
    public function adjustMargin(int $userId, int $currencyId, int $marginMode, float $amount, int $type): array
    {
        return Db::transaction(function () use ($userId, $currencyId, $marginMode, $amount, $type) {
            // 1. 查找仓位
            $position = $this->findPosition($userId, $currencyId, $marginMode);
            if (!$position) {
                throw new BusinessException(ResultCode::FAIL, '仓位不存在');
            }

            // 2. 记录原始保证金
            $originalMargin = $position->margin_amount;

            // 3. 根据类型调整保证金
            if ($type === 1) {
                // 增加保证金
                $this->userAccountsAssetService->freezeAsset(
                    $userId,
                    AccountType::FUTURES->value,
                    $currencyId,
                    $amount,
                    FlowsType::PERPETUAL_MARGIN_FREEZE->value
                );
                $position->margin_amount = bcadd((string)$position->margin_amount, (string)$amount, 8);
            } else {
                // 减少保证金
                $this->userAccountsAssetService->unfreezeAsset(
                    $userId,
                    AccountType::FUTURES->value,
                    $currencyId,
                    $amount,
                    FlowsType::PERPETUAL_MARGIN_UNFREEZE->value
                );
                $position->margin_amount = bcsub((string)$position->margin_amount, (string)$amount, 8);
            }

            // 4. 保存仓位
            $position->save();

            // 5. 触发保证金调整事件
            $this->triggerMarginAdjustedEvent($position, $amount, $type === 1 ? 'increase' : 'decrease', $originalMargin);

            return [
                'success' => true,
                'amount' => $amount,
                'type' => $type,
                'original_margin' => $originalMargin,
                'new_margin' => $position->margin_amount,
            ];
        });
    }

    /**
     * 调整杠杆倍数
     */
    public function adjustLeverage(int $userId, int $currencyId, int $marginMode, float $leverage): array
    {
        Db::beginTransaction();
        try {
            // 1. 验证杠杆合理性
            $this->validateLeverageAdjustment($userId, $currencyId, $marginMode, $leverage);

            // 2. 查找现有仓位
            $position = $this->findPosition($userId, $currencyId, $marginMode);

            if ($position) {
                // 3. 计算新的保证金需求
                $oldMargin = $position->margin_amount;
                $newMargin = $this->calculateRequiredMargin($position, $leverage);
                $marginDiff = bcsub((string)$newMargin, (string)$oldMargin, 8);

                // 4. 调整账户资金
                if (bccomp($marginDiff, '0', 8) > 0) {
                    // 需要增加保证金
                    $this->userAccountsAssetService->freezeAsset(
                        $userId,
                        AccountType::FUTURES->value,
                        $currencyId,
                        (float)$marginDiff,
                        FlowsType::PERPETUAL_MARGIN_FREEZE->value
                    );
                } else {
                    // 释放多余保证金
                    $this->userAccountsAssetService->unfreezeAsset(
                        $userId,
                        AccountType::FUTURES->value,
                        $currencyId,
                        abs((float)$marginDiff),
                        FlowsType::PERPETUAL_MARGIN_UNFREEZE->value
                    );
                }

                // 5. 更新仓位信息
                $position->leverage = $leverage;
                $position->margin_amount = $newMargin;
                $position->initial_margin = $newMargin;
                $position->save();

                Db::commit();

                return [
                    'success' => true,
                    'leverage' => $leverage,
                    'margin_amount' => $newMargin,
                    'margin_change' => (float)$marginDiff
                ];
            } else {
                // 没有仓位时，只需要验证杠杆合理性即可
                Db::commit();

                return [
                    'success' => true,
                    'leverage' => $leverage,
                    'margin_amount' => 0,
                    'margin_change' => 0
                ];
            }

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 验证杠杆调整的合理性
     */
    private function validateLeverageAdjustment(int $userId, int $currencyId, int $marginMode, float $leverage): void
    {
        // 1. 检查杠杆范围
        $maxLeverage = $this->getMaxLeverage($currencyId);
        if ($leverage > $maxLeverage || $leverage < 1) {
            throw new BusinessException(ResultCode::FAIL, "杠杆倍数必须在1-{$maxLeverage}之间");
        }

        // 2. 检查是否有挂单
        if ($this->hasOpenOrders($userId, $currencyId, $marginMode)) {
            throw new BusinessException(ResultCode::FAIL, '存在挂单时不能调整杠杆');
        }
    }

    /**
     * 查找仓位
     */
    private function findPosition(int $userId, int $currencyId, int $marginMode)
    {
        return $this->tradePerpetualPosition->query()
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_mode', $marginMode)
            ->where('status', PositionStatus::HOLDING->value)
            ->first();
    }

    /**
     * 计算所需保证金
     */
    private function calculateRequiredMargin($position, float $leverage): float
    {
        $positionValue = bcmul((string)$position->quantity, (string)$position->entry_price, 8);
        return (float)bcdiv($positionValue, (string)$leverage, 8);
    }

    /**
     * 获取最大杠杆
     */
    private function getMaxLeverage(int $currencyId): int
    {
        $marginLevel = $this->tradeMarginLevel->query()
            ->where('currency_id', $currencyId)
            ->orderBy('level')
            ->first();

        return $marginLevel ? $marginLevel->leverage_max : 20;
    }

    /**
     * 检查是否有挂单
     */
    private function hasOpenOrders(int $userId, int $currencyId, int $marginMode): bool
    {
        // TODO: 实现检查挂单逻辑
        // 需要查询trade_perpetual_order表中是否有未完成的订单
        return false;
    }

    /**
     * 切换保证金模式
     */
    public function switchMarginMode(int $userId, int $currencyId, int $marginMode): array
    {
        // TODO: 实现保证金模式切换逻辑
        // 1. 检查当前是否有持仓
        // 2. 检查挂单情况
        // 3. 切换保证金模式
        // 4. 调整账户配置
        
        return [
            'code' => 200,
            'msg' => 'success',
            'dual_side_position' => false,
        ];
    }

    /**
     * 获取仓位风险
     */
    public function getPositionRisk(int $userId, ?int $currencyId = null, ?int $marginMode = null): array
    {
        // TODO: 实现仓位风险计算逻辑
        return [
            'data' => [],
        ];
    }

    /**
     * 获取强平历史
     */
    public function getLiquidationHistory(int $userId, ?int $currencyId = null, int $perPage = 20, int $page = 1): array
    {
        // TODO: 实现强平历史查询逻辑
        return [
            'data' => [],
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => 0,
            'last_page' => 1,
        ];
    }

    /**
     * 获取ADL历史
     */
    public function getAdlHistory(int $userId, ?int $currencyId = null, int $perPage = 20, int $page = 1): array
    {
        // TODO: 实现ADL历史查询逻辑
        return [
            'data' => [],
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => 0,
            'last_page' => 1,
        ];
    }

    /**
     * 计算仓位未实现盈亏
     */
    private function calculateUnrealizedPnl(array $position, float $markPrice): float
    {
        // TODO: 实现未实现盈亏计算逻辑
        return 0.0;
    }

    /**
     * 计算强平价格
     */
    private function calculateLiquidationPrice(array $position): float
    {
        // TODO: 实现强平价格计算逻辑
        return 0.0;
    }

    /**
     * 验证保证金调整合理性
     */
    private function validateMarginAdjustment(array $position, float $amount, int $type): bool
    {
        // TODO: 实现保证金调整验证逻辑
        return true;
    }

    /**
     * 触发保证金调整事件
     */
    protected function triggerMarginAdjustedEvent(TradePerpetualPosition $position, float $adjustmentAmount, string $adjustmentType, float $originalMargin): void
    {
        try {
            $container = ApplicationContext::getContainer();
            $eventDispatcher = $container->get(EventDispatcherInterface::class);

            $event = new PerpetualMarginAdjusted(
                $position,
                $adjustmentAmount,
                $adjustmentType,
                $originalMargin
            );

            $eventDispatcher->dispatch($event);

        } catch (\Exception $e) {
            // 记录错误但不影响主流程
            logger('perpetual-position')->error('触发保证金调整事件失败', [
                'position_id' => $position->id,
                'adjustment_amount' => $adjustmentAmount,
                'adjustment_type' => $adjustmentType,
                'error' => $e->getMessage()
            ]);
        }
    }
}