<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Contract;

use App\Model\Enums\Trade\Perpetual\PositionStatus;
use App\Service\UserAccounts\UserAccountsAssetService;
use App\Enum\CurrencyConfigKey;
use App\Enum\AsyncExecutorKey;
use App\Job\AsyncFunExecutorJob;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\Trade\TradePerpetualFundingFee;
use App\Model\Trade\TradePerpetualPosition;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

class PerpetualFundingFeeCollectionService
{
    #[Inject]
    protected LoggerInterface $logger;

    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected UserAccountsAssetService $userAccountsAssetService;

    #[Inject]
    protected PerpetualFundingRateCalculationService $fundingRateService;

    /**
     * 收取指定币种的资金费用（分批异步处理）
     */
    public function collectCurrencyFundingFees(int $currencyId, array $rateData): bool
    {
        try {
            $this->logger->info('开始分批收取资金费用', ['currency_id' => $currencyId]);

            // 1. 分批获取仓位并提交到异步队列
            $batchSize = 100; // 每批处理100个仓位
            $offset = 0;
            $totalSubmitted = 0;

            while (true) {
                // 分批获取活跃仓位
                $positions = $this->getActivePositionsBatch($currencyId, $batchSize, $offset);

                if (empty($positions)) {
                    break; // 没有更多仓位了
                }

                // 2. 按保证金模式分组
                $crossPositions = [];
                $isolatedPositions = [];

                foreach ($positions as $position) {
                    if ($position['margin_mode'] == 1) {
                        $crossPositions[] = $position;
                    } else {
                        $isolatedPositions[] = $position;
                    }
                }

                // 3. 提交全仓仓位到异步队列
                if (!empty($crossPositions)) {
                    $this->submitCrossMarginFeesToQueue($crossPositions, $rateData);
                }

                // 4. 提交逐仓仓位到异步队列
                if (!empty($isolatedPositions)) {
                    $this->submitIsolatedMarginFeesToQueue($isolatedPositions, $rateData);
                }

                $totalSubmitted += count($positions);
                $offset += $batchSize;

                $this->logger->info('提交批次到异步队列', [
                    'currency_id' => $currencyId,
                    'batch_size' => count($positions),
                    'total_submitted' => $totalSubmitted
                ]);
            }

            return true;

        } catch (\Exception $e) {
            $this->logger->error('收取资金费用失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 提交全仓仓位到异步队列
     */
    protected function submitCrossMarginFeesToQueue(array $positions, array $rateData): void
    {
        // 按用户分组
        $userPositions = [];
        foreach ($positions as $position) {
            $userPositions[$position['user_id']][] = $position;
        }

        foreach ($userPositions as $userId => $userPositionList) {
            // 提交到异步队列
            $job = new AsyncFunExecutorJob(
                'App\Http\Api\Service\V1\Contract\PerpetualFundingFeeCollectionService',
                'processCrossUserFeesAsync',
                [$userId, $userPositionList, $rateData]
            );
            pushAsyncJob(AsyncExecutorKey::PERPETUAL_CONTRACT_QUEUE->value, $job);
        }
    }

    /**
     * 提交逐仓仓位到异步队列
     */
    protected function submitIsolatedMarginFeesToQueue(array $positions, array $rateData): void
    {
        foreach ($positions as $position) {
            // 提交到异步队列
            $job = new AsyncFunExecutorJob(
                'App\Http\Api\Service\V1\Contract\PerpetualFundingFeeCollectionService',
                'processIsolatedPositionFeeAsync',
                [$position, $rateData]
            );
            pushAsyncJob(AsyncExecutorKey::PERPETUAL_CONTRACT_QUEUE->value, $job);
        }
    }

    /**
     * 异步处理单个用户的全仓资金费用
     */
    public function processCrossUserFeesAsync(int $userId, array $positions, array $rateData): void
    {
        $this->processCrossUserFees($userId, $positions, $rateData);
    }

    /**
     * 异步处理单个逐仓仓位的资金费用
     */
    public function processIsolatedPositionFeeAsync(array $position, array $rateData): void
    {
        $this->processIsolatedPositionFee($position, $rateData);
    }

    /**
     * 处理单个用户的全仓资金费用
     */
    protected function processCrossUserFees(int $userId, array $positions, array $rateData): void
    {
        try {
            $totalFundingFee = 0;
            $feeRecords = [];

            // 计算每个仓位的费用
            foreach ($positions as $position) {
                $positionFee = $this->calculatePositionFee($position, $rateData);
                $totalFundingFee = bcadd((string)$totalFundingFee, (string)$positionFee, 8);

                // 创建费用记录
                $feeRecords[] = $this->createFeeRecord($position, $rateData, $positionFee);

                // 更新仓位的累计资金费用
                $this->updatePositionTotalFunding($position['id'], $positionFee);
            }

            // 从账户余额中扣除/增加总费用
            if (bccomp((string)$totalFundingFee, '0', 8) != 0) {
                $quoteCurrencyId = $this->getQuoteCurrencyId($rateData['currency_id']);
                $success = $this->updateUserBalanceWithFallback($userId, $quoteCurrencyId, $totalFundingFee);

                // 更新费用记录状态
                $status = $success ? 2 : 3; // 2-已收取，3-收取失败
                $this->updateFeeRecordsStatus($feeRecords, $status);
            }

        } catch (\Exception $e) {
            $this->logger->error('处理全仓用户资金费用失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }



    /**
     * 处理单个逐仓仓位的资金费用
     */
    protected function processIsolatedPositionFee(array $position, array $rateData): void
    {
        try {
            $fundingFee = $this->calculatePositionFee($position, $rateData);
            
            // 创建费用记录
            $feeRecord = $this->createFeeRecord($position, $rateData, $fundingFee);

            if (bccomp((string)$fundingFee, '0', 8) != 0) {
                // 逐仓模式：优先从可用余额扣除，不足时从仓位保证金扣除
                $success = $this->updateIsolatedPositionFeeWithFallback($position, $fundingFee);

                // 更新费用记录状态
                $status = $success ? 2 : 3; // 2-已收取，3-收取失败
                $this->updateFeeRecordsStatus([$feeRecord], $status);
            }

        } catch (\Exception $e) {
            $this->logger->error('处理逐仓仓位资金费用失败', [
                'position_id' => $position['id'],
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 计算仓位的资金费用
     */
    protected function calculatePositionFee(array $position, array $rateData): float
    {
        // 仓位价值 = 仓位数量 × 标记价格
        $positionValue = bcmul((string)$position['quantity'], (string)$rateData['mark_price'], 8);

        // 资金费用 = 仓位价值 × 资金费率
        $fundingFee = bcmul($positionValue, (string)$rateData['funding_rate'], 8);

        // 多头支付资金费用（正费率时扣减，负费率时增加）
        // 空头收取资金费用（正费率时增加，负费率时扣减）
        if ($position['side'] == 2) {
            // 空头：收取资金费用，符号相反
            $fundingFee = bcmul($fundingFee, '-1', 8);
        }

        return (float)$fundingFee;
    }

    /**
     * 创建资金费用记录
     */
    protected function createFeeRecord(array $position, array $rateData, float $fundingFee): TradePerpetualFundingFee
    {
        $positionValue = bcmul((string)$position['quantity'], (string)$rateData['mark_price'], 8);

        $feeRecord = new TradePerpetualFundingFee();
        $feeRecord->user_id = $position['user_id'];
        $feeRecord->contract_id = $rateData['currency_id'];
        $feeRecord->position_id = $position['id'];
        $feeRecord->margin_mode = $position['margin_mode'];
        $feeRecord->side = $position['side'];
        $feeRecord->funding_rate = $rateData['funding_rate'];
        $feeRecord->position_size = $position['quantity'];
        $feeRecord->position_value = (float)$positionValue;
        $feeRecord->funding_fee = $fundingFee;
        $feeRecord->funding_time = $rateData['funding_time'];
        $feeRecord->status = 1; // 1-待收取
        $feeRecord->save();

        return $feeRecord;
    }

    /**
     * 分批获取活跃仓位
     */
    protected function getActivePositionsBatch(int $currencyId, int $limit, int $offset): array
    {
        return TradePerpetualPosition::where('currency_id', $currencyId)
            ->where('status', PositionStatus::HOLDING->value)
            ->where('quantity', '>', 0)
            ->offset($offset)
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * 获取活跃仓位（保留原方法用于其他地方）
     */
    protected function getActivePositions(int $currencyId): array
    {
        return TradePerpetualPosition::where('currency_id', $currencyId)
            ->where('status', PositionStatus::HOLDING->value)
            ->where('quantity', '>', 0)
            ->get()
            ->toArray();
    }

    /**
     * 获取计价币种ID
     */
    protected function getQuoteCurrencyId(int $baseCurrencyId): int
    {
        try {
            $currencyKey = CurrencyConfigKey::getCurrencyKey($baseCurrencyId);
            $quoteCurrencyId = $this->redis->hGet($currencyKey, 'quote_assets_id');

            if (!$quoteCurrencyId) {
                return 1273; // 默认USDT
            }

            return (int)$quoteCurrencyId;

        } catch (\Exception $e) {
            $this->logger->error('获取计价币种ID失败', [
                'base_currency_id' => $baseCurrencyId,
                'error' => $e->getMessage()
            ]);
            return 2; // 默认USDT
        }
    }

    /**
     * 带回退机制的用户余额更新（优先可用余额，不足时从保证金扣除）
     */
    protected function updateUserBalanceWithFallback(int $userId, int $currencyId, float $amount): bool
    {
        try {
            if ($amount > 0) {
                // 用户需要支付资金费用，优先从可用余额扣除
                $success = $this->userAccountsAssetService->deductAvailableAsset(
                    $userId,
                    AccountType::FUTURES->value,
                    $currencyId,
                    $amount,
                    FlowsType::PERPETUAL_FUNDING_FEE->value
                );

                if (!$success) {
                    $success = $this->userAccountsAssetService->deductFrozenAsset(
                        $userId,
                        AccountType::FUTURES->value,
                        $currencyId,
                        $amount,
                        FlowsType::PERPETUAL_FUNDING_FEE->value
                    );
                }

                return $success;
            } else {
                // 用户收取资金费用，增加到可用余额
                return $this->userAccountsAssetService->addAvailableAsset(
                    $userId,
                    AccountType::FUTURES->value,
                    $currencyId,
                    abs($amount),
                    FlowsType::PERPETUAL_FUNDING_FEE->value
                );
            }
        } catch (\Exception $e) {
            $this->logger->error('更新用户余额失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 更新用户余额（原方法保留）
     */
    protected function updateUserBalance(int $userId, int $currencyId, float $amount): bool
    {
        try {
            if ($amount > 0) {
                // 用户需要支付资金费用，扣减余额
                return $this->userAccountsAssetService->deductAvailableAsset(
                    $userId,
                    AccountType::FUTURES->value,
                    $currencyId,
                    $amount,
                    FlowsType::PERPETUAL_FUNDING_FEE->value
                );
            } else {
                // 用户收取资金费用，增加余额
                return $this->userAccountsAssetService->addAvailableAsset(
                    $userId,
                    AccountType::FUTURES->value,
                    $currencyId,
                    abs($amount),
                    FlowsType::PERPETUAL_FUNDING_FEE->value
                );
            }
        } catch (\Exception $e) {
            $this->logger->error('更新用户余额失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 逐仓模式资金费用处理（优先可用余额，不足时从仓位保证金扣除）
     */
    protected function updateIsolatedPositionFeeWithFallback(array $position, float $amount): bool
    {
        try {
            if ($amount > 0) {
                // 需要支付资金费用，优先从可用余额扣除
                $quoteCurrencyId = $this->getQuoteCurrencyId($position['currency_id']);
                $success = $this->userAccountsAssetService->deductAvailableAsset(
                    $position['user_id'],
                    AccountType::FUTURES->value,
                    $quoteCurrencyId,
                    $amount,
                    FlowsType::PERPETUAL_FUNDING_FEE->value
                );

                if (!$success) {
                    // 从仓位保证金扣除（此方法内部会更新total_funding）
                    $success = $this->updatePositionMargin($position['id'], $amount);
                } else {
                    // 从可用余额扣除成功，仍需更新仓位的total_funding
                    $this->updatePositionTotalFunding($position['id'], $amount);
                }

                return $success;
            } else {
                // 收取资金费用，增加到可用余额
                $quoteCurrencyId = $this->getQuoteCurrencyId($position['currency_id']);
                $success = $this->userAccountsAssetService->addAvailableAsset(
                    $position['user_id'],
                    AccountType::FUTURES->value,
                    $quoteCurrencyId,
                    abs($amount),
                    FlowsType::PERPETUAL_FUNDING_FEE->value
                );

                // 更新仓位的total_funding
                if ($success) {
                    $this->updatePositionTotalFunding($position['id'], $amount);
                }

                return $success;
            }
        } catch (\Exception $e) {
            $this->logger->error('逐仓资金费用处理失败', [
                'position_id' => $position['id'],
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 更新仓位保证金
     */
    protected function updatePositionMargin(int $positionId, float $amount): bool
    {
        try {
            return Db::transaction(function () use ($positionId, $amount) {
                $position = TradePerpetualPosition::lockForUpdate()->find($positionId);
                if (!$position) {
                    return false;
                }

                // 获取计价币种ID
                $quoteCurrencyId = $this->getQuoteCurrencyId($position->currency_id);

                if ($amount > 0) {
                    // 需要支付资金费用，从保证金中扣除
                    $newMarginAmount = bcsub((string)$position->margin_amount, (string)$amount, 8);

                    // 检查保证金是否足够
                    if (bccomp($newMarginAmount, '0', 8) < 0) {
                        // 保证金不足，只扣除可用部分
                        $actualDeducted = $position->margin_amount; // 实际扣除金额
                        $newMarginAmount = '0';
                    } else {
                        $actualDeducted = $amount; // 实际扣除金额
                    }

                    // 从用户冻结资金中扣除对应金额
                    $frozenSuccess = $this->userAccountsAssetService->deductFrozenAsset(
                        $position->user_id,
                        AccountType::FUTURES->value,
                        $quoteCurrencyId,
                        $actualDeducted,
                        FlowsType::PERPETUAL_FUNDING_FEE->value
                    );

                    if (!$frozenSuccess) {
                        $this->logger->error('扣除冻结资金失败', [
                            'position_id' => $positionId,
                            'user_id' => $position->user_id,
                            'amount' => $actualDeducted
                        ]);
                        return false;
                    }

                    // 更新累计资金费用（支付为正数）
                    $newTotalFunding = bcadd((string)$position->total_funding, (string)$actualDeducted, 8);
                } else {
                    // 收取资金费用，增加到保证金
                    $newMarginAmount = bcadd((string)$position->margin_amount, (string)abs($amount), 8);

                    // 增加用户冻结资金
                    $frozenSuccess = $this->userAccountsAssetService->freezeAsset(
                        $position->user_id,
                        AccountType::FUTURES->value,
                        $quoteCurrencyId,
                        abs($amount),
                        FlowsType::PERPETUAL_FUNDING_FEE->value
                    );

                    if (!$frozenSuccess) {
                        $this->logger->error('增加冻结资金失败', [
                            'position_id' => $positionId,
                            'user_id' => $position->user_id,
                            'amount' => abs($amount)
                        ]);
                        return false;
                    }

                    // 更新累计资金费用（收取为负数）
                    $newTotalFunding = bcsub((string)$position->total_funding, (string)abs($amount), 8);
                }

                // 更新仓位字段
                $position->margin_amount = $newMarginAmount;
                $position->total_funding = $newTotalFunding;

                return $position->save();
            });
        } catch (\Exception $e) {
            $this->logger->error('更新仓位保证金失败', [
                'position_id' => $positionId,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 更新仓位累计资金费用
     */
    protected function updatePositionTotalFunding(int $positionId, float $fundingFee): bool
    {
        try {
            return Db::transaction(function () use ($positionId, $fundingFee) {
                $position = TradePerpetualPosition::lockForUpdate()->find($positionId);
                if (!$position) {
                    return false;
                }

                if ($fundingFee > 0) {
                    // 支付资金费用（正数）
                    $position->total_funding = bcadd((string)$position->total_funding, (string)$fundingFee, 8);
                } else {
                    // 收取资金费用（负数）
                    $position->total_funding = bcsub((string)$position->total_funding, (string)abs($fundingFee), 8);
                }

                return $position->save();
            });
        } catch (\Exception $e) {
            $this->logger->error('更新仓位累计资金费用失败', [
                'position_id' => $positionId,
                'funding_fee' => $fundingFee,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 更新费用记录状态
     */
    protected function updateFeeRecordsStatus(array $feeRecords, int $status): void
    {
        foreach ($feeRecords as $record) {
            $record->status = $status;
            $record->save();
        }
    }
}
