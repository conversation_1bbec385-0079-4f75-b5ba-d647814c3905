<?php

declare(strict_types=1);
/**
 * 永续合约强制平仓服务
 */

namespace App\Http\Api\Service\V1\Contract;

use App\Enum\MarketType;
use App\Model\Trade\TradePerpetualLiquidation;
use App\Model\Trade\TradePerpetualPosition;
use App\Model\Trade\TradeConfig;
use App\Model\Enums\Trade\Perpetual\PositionStatus;
use App\Model\Enums\Trade\Perpetual\LiquidationStatus;
use App\Model\Enums\Trade\Perpetual\LiquidationType;
use App\Model\Enums\Trade\Perpetual\ContractSide;
use App\Model\Enums\Trade\Perpetual\ContractOrderType;
use App\Model\Enums\Trade\Perpetual\TimeInForce;
use App\Enum\Contract\PerpetualPositionCacheKey;
use App\Enum\MarketData\TickerSyncKey;
use App\Enum\User\UserAssetsCacheKey;
use App\Model\Enums\User\AccountType;
use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

class PerpetualLiquidationService
{
    #[Inject]
    protected PerpetualTradeService $perpetualTradeService;

    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct()
    {
        $this->logger = logger('perpetual-liquidation', 'perpetual/liquidation.log');
    }

    /**
     * 执行强制平仓
     */
    public function executeLiquidation(int $userId, int $positionId, ?TradePerpetualPosition $position = null): bool
    {
        return Db::transaction(function () use ($userId, $positionId, $position) {
            try {

                if (!$position) {
                    throw new BusinessException(ResultCode::FAIL, '仓位不存在或已平仓');
                }

                // 2. 获取当前标记价格
                $markPrice = $this->getCurrentMarkPrice($position->currency_id);
                if (!$markPrice) {
                    $markPrice = 0;
                }

                // 3. 创建强平记录
                $liquidationRecord = $this->createLiquidationRecord($position, $markPrice);

                // 4. 创建强平市价订单
                $liquidationOrder = $this->createLiquidationOrder($position, $markPrice);

                // 5. 更新强平记录的订单ID和状态
                $liquidationRecord->liquidation_order_id = $liquidationOrder['order_id'];
                $liquidationRecord->status = LiquidationStatus::EXECUTING->value; // 2-执行中
                $liquidationRecord->save();

                $this->logger->info('强平执行成功', [
                    'user_id' => $userId,
                    'position_id' => $positionId,
                    'liquidation_record_id' => $liquidationRecord->id,
                    'liquidation_order_id' => $liquidationOrder['order_id'],
                    'mark_price' => $markPrice,
                    'liquidated_quantity' => $position->available_quantity
                ]);

                return true;

            } catch (\Exception $e) {
                $this->logger->error('强平执行失败', [
                    'user_id' => $userId,
                    'position_id' => $positionId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                throw $e;
            }
        });
    }

    /**
     * 获取待强平的仓位
     */
    protected function getPositionForLiquidation(int $userId, int $positionId): ?TradePerpetualPosition
    {
        return TradePerpetualPosition::where('id', $positionId)
            ->where('user_id', $userId)
            ->where('status', PositionStatus::HOLDING->value)
            ->where('available_quantity', '>', 0)
            ->first();
    }

    /**
     * 获取当前标记价格
     */
    protected function getCurrentMarkPrice(int $currencyId): ?float
    {
        try {
            // 从市场数据获取最新价格作为标记价格
            $priceData = $this->redis->hGet(TickerSyncKey::getOuterTradeKey($currencyId, MarketType::MARGIN->value), 'price');

            if ($priceData && $priceData > 0) {
                return (float)$priceData;
            }

            return null;

        } catch (\Exception $e) {
            $this->logger->error('获取标记价格失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 创建强平记录（基础信息，关联仓位ID）
     */
    protected function createLiquidationRecord(TradePerpetualPosition $position, float $markPrice): TradePerpetualLiquidation
    {
        // 计算当前保证金率
        $marginRatio = $this->calculateMarginRatio($position, $markPrice);

        $liquidationRecord = new TradePerpetualLiquidation();
        $liquidationRecord->user_id = $position->user_id;
        $liquidationRecord->position_id = $position->id;
        $liquidationRecord->currency_id = $position->currency_id;
        $liquidationRecord->margin_mode = $position->margin_mode;
        $liquidationRecord->liquidation_type = LiquidationType::INSUFFICIENT_MARGIN->value;
        $liquidationRecord->trigger_source = 1; // 1-系统自动
        $liquidationRecord->original_quantity = $position->quantity; // 原始持仓数量
        $liquidationRecord->liquidated_quantity = $position->available_quantity; // 强平数量
        $liquidationRecord->liquidation_price = 0; // 强平价格，订单成交后更新
        $liquidationRecord->mark_price = $markPrice; // 触发时标记价格
        $liquidationRecord->margin_ratio = $marginRatio; // 强平时保证金率
        $liquidationRecord->liquidation_fee = 0; // 强平手续费，订单成交后计算
        $liquidationRecord->insurance_fund = 0; // 保险基金使用，订单成交后计算
        $liquidationRecord->bankruptcy_amount = 0; // 穿仓金额，订单成交后计算
        $liquidationRecord->trigger_time = date('Y-m-d H:i:s'); // 强平触发时间
        $liquidationRecord->status = LiquidationStatus::PENDING->value; // 1-待执行
        $liquidationRecord->save();

        return $liquidationRecord;
    }

    /**
     * 创建强平市价订单
     */
    protected function createLiquidationOrder(TradePerpetualPosition $position, float $markPrice): array
    {
        // 确定强平订单方向（与仓位方向相反）
        // 仓位side: 1-多头，2-空头
        // 订单side: 1-买入开多，2-卖出开空，3-买入平空，4-卖出平多
        $orderSide = $position->side === ContractSide::BUY_OPEN->value ? ContractSide::SELL_CLOSE->value : ContractSide::BUY_CLOSE->value;

        // 构建强平订单参数
        $orderParams = [
            'currency_id' => $position->currency_id,
            'side' => $orderSide,
            'order_type' => ContractOrderType::MARKET->value, // 市价单
            'quantity' => $position->available_quantity, // 全部可用数量
            'price' => $markPrice, // 使用标记价格
            'margin_mode' => $position->margin_mode,
            'leverage' => $position->leverage,
            'time_in_force' => TimeInForce::GTC->value, // 立即成交或取消
            'position_id' => $position->id, // 关联仓位ID
            'reduce_only' => true
        ];

        try {
            // 调用交易服务创建订单
            $result = $this->perpetualTradeService->createOrder($position->user_id, $orderParams);

            if (!$result || !isset($result['order_id'])) {
                throw new BusinessException(ResultCode::FAIL, '创建强平订单失败');
            }

            $this->logger->info('强平订单创建成功', [
                'position_id' => $position->id,
                'order_id' => $result['order_id'],
                'side' => $orderSide,
                'quantity' => $position->available_quantity,
                'mark_price' => $markPrice
            ]);

            return $result;

        } catch (\Exception $e) {
            $this->logger->error('创建强平订单失败', [
                'position_id' => $position->id,
                'order_params' => $orderParams,
                'error' => $e->getMessage()
            ]);

            throw new BusinessException(ResultCode::FAIL, '创建强平订单失败: ' . $e->getMessage());
        }
    }

    /**
     * 清理仓位缓存
     */
    public function clearPositionCache(TradePerpetualPosition $position): void
    {
        try {
            $positionId = (int)$position->id;
            $userId = $position->user_id;
            $currencyId = $position->currency_id;
            $marginMode = $position->margin_mode;

            // 1. 删除仓位详情缓存
            $positionDetailKey = PerpetualPositionCacheKey::getPositionDetailKey($positionId);
            $this->redis->del($positionDetailKey);

            $side = $position->side;

            // 生成正确的缓存键
            $positionKey = PerpetualPositionCacheKey::generatePositionKey($currencyId, $side, $marginMode);
            $userPositionKey = PerpetualPositionCacheKey::generateUserPositionKey($userId, $side, $marginMode);

            // 2. 从用户仓位缓存中移除
            $userPositionsKey = PerpetualPositionCacheKey::getUserPositionsKey($userId);
            $this->redis->hDel($userPositionsKey, $positionKey);

            // 3. 从币种仓位索引中移除
            $currencyPositionsKey = PerpetualPositionCacheKey::getCurrencyPositionsKey($currencyId);
            $this->redis->hDel($currencyPositionsKey, $userPositionKey);

            // 4. 从活跃仓位集合中移除
            $activePositionsKey = PerpetualPositionCacheKey::getActivePositionsKey();
            $this->redis->sRem($activePositionsKey, $positionId);

            // 5. 从所有风险等级索引中移除
            foreach (PerpetualPositionCacheKey::getAllRiskLevels() as $level) {
                $riskLevelKey = PerpetualPositionCacheKey::getRiskLevelKey($level);
                $this->redis->zRem($riskLevelKey, $positionId);
            }

            // 6. 清理币种风险监控缓存中的统计数据
            $currencyRiskKey = PerpetualPositionCacheKey::getCurrencyRiskKey($currencyId);
            $this->redis->hIncrBy($currencyRiskKey, 'position_count', -1);

            // 7. 清理用户风险统计缓存
            $userRiskKey = PerpetualPositionCacheKey::getUserRiskKey($userId);
            $this->redis->hDel($userRiskKey, "position_{$positionId}");

            // 8. 从待风险计算队列中移除
            $pendingRiskKey = PerpetualPositionCacheKey::getPendingRiskCalculationKey($currencyId);
            $this->redis->sRem($pendingRiskKey, $positionId);

            // 9. 删除强平锁定缓存
            $liquidationLockKey = PerpetualPositionCacheKey::getLiquidationLockKey($positionId);
            $this->redis->del($liquidationLockKey);

            // 10. 更新全局统计
            $globalStatsKey = PerpetualPositionCacheKey::getGlobalStatsKey();
            $this->redis->hIncrBy($globalStatsKey, 'total_positions', -1);
            $this->redis->hSet($globalStatsKey, 'last_updated', time());

            $this->logger->debug('仓位缓存清理完成', [
                'position_id' => $positionId,
                'user_id' => $userId,
                'currency_id' => $currencyId
            ]);

        } catch (\Exception $e) {
            $this->logger->error('清理仓位缓存失败', [
                'position_id' => $position->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 检查强平条件
     */
    public function checkLiquidationCondition(int $userId, int $positionId): bool
    {
        try {
            $position = TradePerpetualPosition::where('id', $positionId)
                ->where('user_id', $userId)
                ->where('status', PositionStatus::HOLDING->value)
                ->first();

            if (!$position) {
                return false;
            }

            // 获取当前标记价格
            $markPrice = $this->getCurrentMarkPrice($position->currency_id);
            if (!$markPrice) {
                return false;
            }

            // 计算当前保证金率
            $marginRatio = $this->calculateMarginRatio($position, $markPrice);

            // 获取维持保证金率
            $maintenanceMarginRate = $this->getMaintenanceMarginRate($position);

            // 判断是否触发强平条件
            return bccomp((string)$marginRatio, (string)$maintenanceMarginRate, 8) <= 0;

        } catch (\Exception $e) {
            $this->logger->error('检查强平条件失败', [
                'user_id' => $userId,
                'position_id' => $positionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 计算保证金率
     */
    protected function calculateMarginRatio(TradePerpetualPosition $position, float $markPrice): float
    {
        // 计算未实现盈亏
        $unrealizedPnl = $this->calculateUnrealizedPnl($position, $markPrice);

        // 计算名义价值
        $notionalValue = bcmul((string)$position->available_quantity, (string)$markPrice, 8);

        if ($position->margin_mode === 1) {
            // 全仓模式：使用账户总余额
            $accountBalance = $this->getUserAccountBalance($position->user_id, $position->currency_id);
            $totalBalance = bcadd((string)$accountBalance['total'], (string)$unrealizedPnl, 8);
            return (float)bcdiv($totalBalance, $notionalValue, 8);
        } else {
            // 逐仓模式：使用仓位保证金
            $marginBalance = bcadd((string)$position->margin_amount, (string)$unrealizedPnl, 8);
            return (float)bcdiv($marginBalance, $notionalValue, 8);
        }
    }

    /**
     * 计算未实现盈亏
     */
    protected function calculateUnrealizedPnl(TradePerpetualPosition $position, float $markPrice): float
    {
        $priceDiff = bcsub((string)$markPrice, (string)$position->entry_price, 8);

        // 仓位side: 1-多头，2-空头
        if ($position->side === 1) {
            // 多头：(当前价格 - 开仓价格) × 数量
            return (float)bcmul((string)$position->available_quantity, $priceDiff, 8);
        } else {
            // 空头：(开仓价格 - 当前价格) × 数量
            return (float)bcmul((string)$position->available_quantity, bcmul($priceDiff, '-1', 8), 8);
        }
    }

    /**
     * 获取维持保证金率
     */
    protected function getMaintenanceMarginRate(TradePerpetualPosition $position): float
    {
        try {
            $userRiskKey = PerpetualPositionCacheKey::getUserRiskKey($position->user_id);
            $fieldKey = "margin_rate_{$position->currency_id}_{$position->margin_mode}";

            $cachedRate = $this->redis->hGet($userRiskKey, $fieldKey);

            if ($cachedRate !== false) {
                return (float)$cachedRate;
            }

            // 返回默认维持保证金率 0.5%
            return 0.005;

        } catch (\Exception $e) {
            $this->logger->error('获取维持保证金率失败', [
                'position_id' => $position->id,
                'error' => $e->getMessage()
            ]);
            return 0.005;
        }
    }

    /**
     * 获取用户账户余额
     */
    protected function getUserAccountBalance(int $userId, int $currencyId): array
    {
        try {
            $assetsKey = UserAssetsCacheKey::getFuturesAssetsKey($userId, AccountType::FUTURES->value);

            $available = $this->redis->hGet($assetsKey, (string)$currencyId) ?: '0';
            $frozen = $this->redis->hGet($assetsKey, "{$currencyId}-frozen") ?: '0';

            return [
                'available' => (float)$available,
                'frozen' => (float)$frozen,
                'total' => (float)bcadd($available, $frozen, 8)
            ];

        } catch (\Exception $e) {
            $this->logger->error('获取用户账户余额失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);

            return [
                'available' => 0.0,
                'frozen' => 0.0,
                'total' => 0.0
            ];
        }
    }

    /**
     * 更新强平记录为完成状态
     */
    public function completeLiquidationRecord(int $liquidationRecordId, float $actualFee = 0, float $insuranceFund = 0, float $bankruptcyAmount = 0): bool
    {
        try {
            $liquidationRecord = TradePerpetualLiquidation::find($liquidationRecordId);

            if (!$liquidationRecord) {
                $this->logger->error('强平记录不存在', ['liquidation_record_id' => $liquidationRecordId]);
                return false;
            }

            $liquidationRecord->status = LiquidationStatus::COMPLETED->value; // 3-已完成
            $liquidationRecord->liquidation_fee = $actualFee; // 实际手续费
            $liquidationRecord->insurance_fund = $insuranceFund; // 保险基金使用
            $liquidationRecord->bankruptcy_amount = $bankruptcyAmount; // 穿仓金额
            $liquidationRecord->completed_time = date('Y-m-d H:i:s'); // 强平完成时间
            $liquidationRecord->save();


            $this->logger->info('强平记录更新为完成状态', [
                'liquidation_record_id' => $liquidationRecordId,
                'actual_fee' => $actualFee,
                'insurance_fund' => $insuranceFund,
                'bankruptcy_amount' => $bankruptcyAmount
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('更新强平记录失败', [
                'liquidation_record_id' => $liquidationRecordId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 更新强平记录为失败状态
     */
    public function failLiquidationRecord(int $liquidationRecordId, string $reason = ''): bool
    {
        try {
            $liquidationRecord = TradePerpetualLiquidation::find($liquidationRecordId);

            if (!$liquidationRecord) {
                $this->logger->error('强平记录不存在', ['liquidation_record_id' => $liquidationRecordId]);
                return false;
            }

            $liquidationRecord->status = LiquidationStatus::FAILED->value; // 4-执行失败
            $liquidationRecord->save();

            $this->logger->warning('强平记录标记为失败', [
                'liquidation_record_id' => $liquidationRecordId,
                'reason' => $reason
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('更新强平记录失败状态失败', [
                'liquidation_record_id' => $liquidationRecordId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 处理强平订单完成（由仓位平仓监听器调用）
     */
    public function handleLiquidationOrderCompleted(int $perpetualOrderId, float $executedPrice, float $executedQuantity): bool
    {
        try {
            // 1. 查找关联的强平记录
            // liquidation_order_id 存储的是 TradePerpetualOrder.id
            $liquidationRecord = TradePerpetualLiquidation::where('liquidation_order_id', $perpetualOrderId)->first();

            if (!$liquidationRecord) {
                // 不是强平订单，直接返回
                return true;
            }

            // 2. 获取交易配置中的强平费率
            $liquidationFeeRate = $this->getLiquidationFeeRate($liquidationRecord->currency_id);

            // 3. 计算强平手续费
            $liquidationFee = 0;
            if ($liquidationFeeRate > 0) {
                $liquidationFee = bcmul(
                    bcmul((string)$executedQuantity, (string)$executedPrice, 8),
                    (string)$liquidationFeeRate,
                    8
                );
            }

            // 4. 计算穿仓金额和保险基金使用
            $bankruptcyResult = $this->calculateBankruptcyAmount($liquidationRecord, $executedPrice);

            // 5. 更新强平记录
            $liquidationRecord->liquidation_price = $executedPrice; // 实际强平价格
            $liquidationRecord->liquidation_fee = (float)$liquidationFee; // 强平手续费
            $liquidationRecord->insurance_fund = $bankruptcyResult['insurance_fund']; // 保险基金使用
            $liquidationRecord->bankruptcy_amount = $bankruptcyResult['bankruptcy_amount']; // 穿仓金额
            $liquidationRecord->completed_time = date('Y-m-d H:i:s'); // 强平完成时间
            $liquidationRecord->status = LiquidationStatus::COMPLETED->value; // 3-已完成
            $liquidationRecord->save();

            $this->logger->info('强平订单完成处理', [
                'perpetual_order_id' => $perpetualOrderId,
                'liquidation_record_id' => $liquidationRecord->id,
                'executed_price' => $executedPrice,
                'executed_quantity' => $executedQuantity,
                'liquidation_fee' => $liquidationFee,
                'insurance_fund' => $bankruptcyResult['insurance_fund'],
                'bankruptcy_amount' => $bankruptcyResult['bankruptcy_amount']
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('处理强平订单完成失败', [
                'perpetual_order_id' => $perpetualOrderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 获取强平费率
     */
    protected function getLiquidationFeeRate(int $currencyId): float
    {
        try {
            // 从trade_config表获取强平费率
            $tradeConfig = TradeConfig::where('currency_id', $currencyId)
                ->where('market_type', MarketType::MARGIN->value) // 5-永续合约
                ->first();

            if ($tradeConfig && $tradeConfig->liquidation_fee > 0) {
                return $tradeConfig->liquidation_fee;
            }

            return 0; // 如果配置为0或不存在，则不收手续费

        } catch (\Exception $e) {
            $this->logger->error('获取强平费率失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * 计算穿仓金额和保险基金使用
     */
    protected function calculateBankruptcyAmount(TradePerpetualLiquidation $liquidationRecord, float $executedPrice): array
    {
        try {
            // 获取仓位信息
            $position = TradePerpetualPosition::find($liquidationRecord->position_id);
            if (!$position) {
                return ['insurance_fund' => 0, 'bankruptcy_amount' => 0];
            }

            // 计算理论破产价格（保证金为0时的价格）
            $bankruptcyPrice = $this->calculateBankruptcyPrice($position);

            // 如果实际成交价格比破产价格更有利，差额归保险基金
            $insuranceFund = 0;
            $bankruptcyAmount = 0;

            if ($position->side === 1) { // 多头
                if ($executedPrice > $bankruptcyPrice) {
                    // 强平价格高于破产价格，有盈余归保险基金
                    $insuranceFund = bcmul(
                        (string)$liquidationRecord->liquidated_quantity,
                        bcsub((string)$executedPrice, (string)$bankruptcyPrice, 8),
                        8
                    );
                } else {
                    // 强平价格低于破产价格，产生穿仓
                    $bankruptcyAmount = bcmul(
                        (string)$liquidationRecord->liquidated_quantity,
                        bcsub((string)$bankruptcyPrice, (string)$executedPrice, 8),
                        8
                    );
                }
            } else { // 空头
                if ($executedPrice < $bankruptcyPrice) {
                    // 强平价格低于破产价格，有盈余归保险基金
                    $insuranceFund = bcmul(
                        (string)$liquidationRecord->liquidated_quantity,
                        bcsub((string)$bankruptcyPrice, (string)$executedPrice, 8),
                        8
                    );
                } else {
                    // 强平价格高于破产价格，产生穿仓
                    $bankruptcyAmount = bcmul(
                        (string)$liquidationRecord->liquidated_quantity,
                        bcsub((string)$executedPrice, (string)$bankruptcyPrice, 8),
                        8
                    );
                }
            }

            return [
                'insurance_fund' => (float)$insuranceFund,
                'bankruptcy_amount' => (float)$bankruptcyAmount
            ];

        } catch (\Exception $e) {
            $this->logger->error('计算穿仓金额失败', [
                'liquidation_record_id' => $liquidationRecord->id,
                'error' => $e->getMessage()
            ]);
            return ['insurance_fund' => 0, 'bankruptcy_amount' => 0];
        }
    }

    /**
     * 计算破产价格
     */
    protected function calculateBankruptcyPrice(TradePerpetualPosition $position): float
    {
        // 破产价格 = 开仓价格 ± (保证金 / 数量)
        // 多头：破产价格 = 开仓价格 - (保证金 / 数量)
        // 空头：破产价格 = 开仓价格 + (保证金 / 数量)

        $marginPerUnit = bcdiv((string)$position->margin_amount, (string)$position->quantity, 8);

        if ($position->side === 1) { // 多头
            return (float)bcsub((string)$position->entry_price, $marginPerUnit, 8);
        } else { // 空头
            return (float)bcadd((string)$position->entry_price, $marginPerUnit, 8);
        }
    }

    /**
     * 计算实际盈亏
     */
    protected function calculateActualPnl(TradePerpetualPosition $position, float $price): float
    {
        $priceDiff = bcsub((string)$price, (string)$position->entry_price, 8);

        if ($position->side === 1) { // 多头
            return (float)bcmul((string)$position->quantity, $priceDiff, 8);
        } else { // 空头
            return (float)bcmul((string)$position->quantity, bcmul($priceDiff, '-1', 8), 8);
        }
    }
}