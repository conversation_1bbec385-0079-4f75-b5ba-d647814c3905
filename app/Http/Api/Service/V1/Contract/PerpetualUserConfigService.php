<?php

declare(strict_types=1);
/**
 * 永续合约用户配置服务
 */

namespace App\Http\Api\Service\V1\Contract;

use App\Http\Common\ResultCode;
use App\Model\User\UserPerprtualConfig;
use App\Model\Trade\TradeMarginLevel;
use App\Model\Trade\TradePerpetualPosition;
use App\Enum\Contract\PerpetualPositionCacheKey;
use App\Exception\BusinessException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use Hyperf\Redis\Redis;

class PerpetualUserConfigService
{
    #[Inject]
    protected UserPerprtualConfig $userPerpetualConfig;

    #[Inject]
    protected Redis $redis;

    /**
     * 获取用户合约交易配置
     */
    public function getUserConfig(int $userId, int $currencyId): array
    {
        $config = $this->userPerpetualConfig
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->first();
        
        if (!$config) {
            // 如果用户配置不存在，创建默认配置
            $config = $this->createDefaultConfig($userId, $currencyId);
        }

        return [
            'user_id' => $config->user_id,
            'currency_id' => $config->currency_id,
            'hold_units' => $config->hold_units,
            'hold_mode' => $config->hold_mode,
            'pnl_source' => $config->pnl_source,
            'tp_sl_source' => $config->tp_sl_source,
            'assets_mode' => $config->aassets_mode,
            'price_protect' => $config->price_protect,
            'lever' => $config->lever,
            'iso_long_lever'=> $config->iso_long_lever,
            'iso_short_lever' => $config->iso_short_lever,
            'margin_type' => $config->margin_type,
            'created_at' => $config->created_at->toDateTimeString(),
            'updated_at' => $config->updated_at->toDateTimeString(),
        ];
    }

    /**
     * 更新用户合约交易配置
     */
    public function updateUserConfig(int $userId, int $currencyId, array $configData): array
    {
        Db::beginTransaction();
        try {
            // 验证配置参数
            $this->validateConfigData($configData);

            $config = $this->userPerpetualConfig
                ->where('user_id', $userId)
                ->where('currency_id', $currencyId)
                ->first();
            
            if (!$config) {
                // 如果配置不存在，创建新配置
                $config = new UserPerprtualConfig();
                $config->user_id = $userId;
                $config->currency_id = $currencyId;
            }

            // 更新配置字段
            if (isset($configData['hold_units'])) {
                $config->hold_units = (int)$configData['hold_units'];
            }
            if (isset($configData['hold_mode'])) {
                $config->hold_mode = (int)$configData['hold_mode'];
            }
            if (isset($configData['pnl_source'])) {
                $config->pnl_source = (int)$configData['pnl_source'];
            }
            if (isset($configData['tp_sl_source'])) {
                $config->tp_sl_source = (int)$configData['tp_sl_source'];
            }
            if (isset($configData['assets_mode'])) {
                $config->aassets_mode = (int)$configData['assets_mode'];
            }
            if (isset($configData['price_protect'])) {
                $config->price_protect = (int)$configData['price_protect'];
            }
            if(isset($configData['iso_long_lever'])){
                $config->iso_long_lever = (int)$configData['iso_long_lever'];
            }
            if(isset($configData['iso_long_lever'])){
                $config->iso_long_lever = (int)$configData['iso_long_lever'];
            }
            // 记录杠杆是否有变化
            $leverageChanged = false;
            $marginTypeChanged = false;

            if (isset($configData['lever'])) {
                $oldLever = $config->lever;
                $config->lever = (int)$configData['lever'];
                $leverageChanged = ($oldLever != $config->lever);
            }
            if (isset($configData['margin_type'])) {
                $oldMarginType = $config->margin_type;
                $config->margin_type = (int)$configData['margin_type'];
                $marginTypeChanged = ($oldMarginType != $config->margin_type);
            }

            $config->save();

            // 如果杠杆或保证金类型有变化，更新维持保证金率缓存
            if ($leverageChanged || $marginTypeChanged) {
                $this->updateMaintenanceMarginRateCache($userId, $currencyId, $config->lever, $config->margin_type);
            }

            Db::commit();

            return $this->getUserConfig($userId, $currencyId);
        } catch (\Exception $e) {
            Db::rollBack();
            var_dump($e);
            throw new BusinessException(ResultCode::FAIL,'更新配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建默认配置
     */
    private function createDefaultConfig(int $userId, int $currencyId): UserPerprtualConfig
    {
        $config = new UserPerprtualConfig();
        $config->user_id = $userId;
        $config->currency_id = $currencyId;
        $config->hold_units = 1; // 默认：数量
        $config->hold_mode = 1; // 默认：单向持仓
        $config->pnl_source = 1; // 默认：标记价格
        $config->tp_sl_source = 1; // 默认：最新价格
        $config->aassets_mode = 1; // 默认：单币种保证金
        $config->price_protect = 1; // 默认：开启价差保护
        $config->lever = 20; // 默认：20倍杠杆
        $config->margin_type = 1; // 默认：全仓保证金
        $config->save();

        return $config;
    }

    /**
     * 验证配置数据
     */
    private function validateConfigData(array $configData): void
    {
        // 验证持仓单位
        if (isset($configData['hold_units']) && !in_array($configData['hold_units'], [1, 2, 3])) {
            throw new BusinessException(ResultCode::FAIL,'持仓单位参数错误');
        }

        // 验证持仓模式
        if (isset($configData['hold_mode']) && !in_array($configData['hold_mode'], [1, 2])) {
            throw new BusinessException(ResultCode::FAIL,'持仓模式参数错误');
        }

        // 验证盈亏来源
        if (isset($configData['pnl_source']) && !in_array($configData['pnl_source'], [1, 2])) {
            throw new BusinessException(ResultCode::FAIL,'盈亏来源参数错误');
        }

        // 验证止盈止损来源
        if (isset($configData['tp_sl_source']) && !in_array($configData['tp_sl_source'], [1, 2])) {
            throw new BusinessException(ResultCode::FAIL,'止盈止损来源参数错误');
        }

        // 验证资产模式
        if (isset($configData['assets_mode']) && !in_array($configData['assets_mode'], [1, 2])) {
            throw new BusinessException(ResultCode::FAIL,'资产模式参数错误');
        }

        // 验证价差保护
        if (isset($configData['price_protect']) && !in_array($configData['price_protect'], [0, 1])) {
            throw new BusinessException(ResultCode::FAIL,'价差保护参数错误');
        }

        // 验证保证金类型
        if (isset($configData['margin_type']) && !in_array($configData['margin_type'], [1, 2])) {
            throw new BusinessException(ResultCode::FAIL,'保证金类型参数错误');
        }
    }

    /**
     * 获取持仓单位名称
     */
    private function getHoldUnitsName(int $holdUnits): string
    {
        return match($holdUnits) {
            1 => '数量',
            2 => '成本价值',
            3 => '名义价值',
            default => '未知'
        };
    }

    /**
     * 获取持仓模式名称
     */
    private function getHoldModeName(int $holdMode): string
    {
        return match($holdMode) {
            1 => '单向持仓',
            2 => '双向持仓',
            default => '未知'
        };
    }

    /**
     * 获取盈亏来源名称
     */
    private function getPnlSourceName(int $pnlSource): string
    {
        return match($pnlSource) {
            1 => '标记价格',
            2 => '最新价格',
            default => '未知'
        };
    }

    /**
     * 获取止盈止损来源名称
     */
    private function getTpSlSourceName(int $tpSlSource): string
    {
        return match($tpSlSource) {
            1 => '最新价格',
            2 => '标记价格',
            default => '未知'
        };
    }

    /**
     * 获取资产模式名称
     */
    private function getAssetModeName(int $assetsMode): string
    {
        return match($assetsMode) {
            1 => '单币种保证金',
            2 => '联合保证金',
            default => '未知'
        };
    }

    /**
     * 获取保证金类型名称
     */
    private function getMarginTypeName(int $marginType): string
    {
        return match($marginType) {
            1 => '全仓保证金',
            2 => '逐仓保证金',
            default => '未知'
        };
    }

    /**
     * 重置用户配置为默认值
     */
    public function resetUserConfig(int $userId, int $currencyId): array
    {
        $config = $this->userPerpetualConfig
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->first();
        
        if ($config) {
            $config->delete();
        }

        // 重新创建默认配置
        $newConfig = $this->createDefaultConfig($userId, $currencyId);

        return $this->getUserConfig($userId, $currencyId);
    }

    /**
     * 更新维持保证金率缓存
     */
    protected function updateMaintenanceMarginRateCache(int $userId, int $currencyId, int $leverage, int $marginType): void
    {
        try {
            // 1. 计算名义价值（全仓模式需要计算总名义价值）
            $notionalValue = 0;
            if ($marginType === 1) {
                // 全仓模式：计算用户在该币种的总名义价值
                $notionalValue = $this->getTotalNotionalValue($userId, $currencyId);
            }

            // 2. 查询维持保证金率
            $marginRate = $this->getMarginRateFromLevel($currencyId, $notionalValue, $leverage);

            // 3. 缓存到用户风险统计缓存
            $userRiskKey = PerpetualPositionCacheKey::getUserRiskKey($userId);

            $cacheData = [
                "margin_rate_{$currencyId}_{$marginType}" => (string)$marginRate,
                "leverage_{$currencyId}" => (string)$leverage,
                "notional_value_{$currencyId}_{$marginType}" => (string)$notionalValue,
                "last_calculated_{$currencyId}_{$marginType}" => (string)time(),
            ];

            $this->redis->hMSet($userRiskKey, $cacheData);
            // 用户风险统计不设置过期时间，随用户配置变更而更新

        } catch (\Exception $e) {
            // 记录错误但不影响主流程
            logger('perpetual-config')->error('更新维持保证金率缓存失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'leverage' => $leverage,
                'margin_type' => $marginType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取用户在该币种的总名义价值（全仓模式）
     */
    protected function getTotalNotionalValue(int $userId, int $currencyId): float
    {
        $positions = TradePerpetualPosition::where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_mode', 1) // 全仓模式
            ->where('status', 1) // 持仓中
            ->where('quantity', '>', 0)
            ->get();

        $totalNotionalValue = 0;
        foreach ($positions as $position) {
            // 名义价值 = 数量 × 开仓价格
            $notionalValue = bcmul((string)$position->quantity, (string)$position->entry_price, 8);
            $totalNotionalValue = bcadd((string)$totalNotionalValue, $notionalValue, 8);
        }

        return (float)$totalNotionalValue;
    }

    /**
     * 从trade_margin_level表查询维持保证金率
     */
    protected function getMarginRateFromLevel(int $currencyId, float $notionalValue, int $leverage): float
    {
        try {
            // 1. 优先根据名义价值匹配档位
            $marginLevel = TradeMarginLevel::where('currency_id', $currencyId)
                ->where('margin_min', '<=', $notionalValue)
                ->where('margin_max', '>=', $notionalValue)
                ->orderBy('level')
                ->first();

            if (!$marginLevel) {
                // 2. 如果没有找到对应档位，使用杠杆倍数匹配档位
                $marginLevel = TradeMarginLevel::where('currency_id', $currencyId)
                    ->where('leverage_min', '<=', $leverage)
                    ->where('leverage_max', '>=', $leverage)
                    ->orderBy('level')
                    ->first();
            }

            if (!$marginLevel) {
                // 3. 如果还是没有找到，查找最大档位
                $marginLevel = TradeMarginLevel::where('currency_id', $currencyId)
                    ->orderBy('margin_max', 'desc')
                    ->first();
            }

            if (!$marginLevel) {
                // 4. 如果完全没有配置，使用默认维持保证金率 0.5%
                return 0.005;
            }

            return (float)$marginLevel->margin_rate;

        } catch (\Exception $e) {
            logger('perpetual-config')->error('查询维持保证金率失败', [
                'currency_id' => $currencyId,
                'notional_value' => $notionalValue,
                'leverage' => $leverage,
                'error' => $e->getMessage()
            ]);

            return 0.005;
        }
    }
}