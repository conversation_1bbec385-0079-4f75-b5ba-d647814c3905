<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Contract;

use App\Enum\MarketData\TickerSyncKey;
use App\Enum\CurrencyConfigKey;
use App\Enum\MarketType;
use App\Model\Trade\TradePerpetualFundingConfig;
use App\Model\Trade\TradePerpetualFundingRate;
use Carbon\Carbon;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

class PerpetualFundingRateCalculationService
{
    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected LoggerInterface $logger;

    /**
     * 计算指定币种的资金费率
     */
    public function calculateFundingRate(int $currencyId): ?array
    {
        try {
            // 1. 获取配置
            $config = $this->getFundingConfig($currencyId);
            if (!$config) {
                return null;
            }

            // 2. 获取价格数据
            $priceData = $this->getPriceData($currencyId);
            if (!$priceData) {
                $this->logger->warning('无法获取价格数据', ['currency_id' => $currencyId]);
                return null;
            }

            // 3. 计算溢价率
            $premiumRate = $this->calculatePremiumRate(
                $priceData['mark_price'], 
                $priceData['index_price']
            );

            // 4. 计算资金费率
            $fundingRate = $this->calculateRate($premiumRate, $config);

            // 5. 计算下次收取时间
            $nextFundingTime = $this->calculateNextFundingTime($config->interval);

            return [
                'currency_id' => $currencyId,
                'funding_rate' => $fundingRate,
                'premium_rate' => $premiumRate,
                'interest_rate' => $config->base_rate,
                'mark_price' => $priceData['mark_price'],
                'index_price' => $priceData['index_price'],
                'funding_time' => Carbon::now(),
                'next_funding_time' => $nextFundingTime,
                'config' => $config
            ];

        } catch (\Exception $e) {
            $this->logger->error('计算资金费率失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 保存资金费率记录
     */
    public function saveFundingRateRecord(array $rateData): bool
    {
        try {
            $fundingRate = new TradePerpetualFundingRate();
            $fundingRate->contract_id = $rateData['currency_id'];
            $fundingRate->funding_rate = $rateData['funding_rate'];
            $fundingRate->funding_time = $rateData['funding_time'];
            $fundingRate->next_funding_time = $rateData['next_funding_time'];
            $fundingRate->premium_rate = $rateData['premium_rate'];
            $fundingRate->interest_rate = $rateData['interest_rate'];
            $fundingRate->mark_price = $rateData['mark_price'];
            $fundingRate->index_price = $rateData['index_price'];
            $fundingRate->created_at = Carbon::now();

            return $fundingRate->save();

        } catch (\Exception $e) {
            $this->logger->error('保存资金费率记录失败', [
                'currency_id' => $rateData['currency_id'] ?? 0,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取资金费率配置
     */
    protected function getFundingConfig(int $currencyId): ?TradePerpetualFundingConfig
    {
        return TradePerpetualFundingConfig::where('currency_id', $currencyId)
            ->where('status', 1)
            ->first();
    }

    /**
     * 获取价格数据
     */
    protected function getPriceData(int $currencyId): ?array
    {
        try {
            // 获取合约价格（标记价格）- market_type = 5
            $contractKey = TickerSyncKey::getOuterTradeKey($currencyId, MarketType::MARGIN->value);
            $markPrice = $this->redis->hGet($contractKey, 'price');

            // 获取现货价格（指数价格）- market_type = 1
            $spotKey = TickerSyncKey::getOuterTradeKey($currencyId, MarketType::CRYPTO->value);
            $indexPrice = $this->redis->hGet($spotKey, 'price');

            if (!$markPrice || !$indexPrice) {
                return null;
            }

            return [
                'mark_price' => (float)$markPrice,
                'index_price' => (float)$indexPrice
            ];

        } catch (\Exception $e) {
            $this->logger->error('获取价格数据失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 计算溢价率
     */
    protected function calculatePremiumRate(float $markPrice, float $indexPrice): float
    {
        if ($indexPrice <= 0) {
            return 0.0;
        }

        return ($markPrice - $indexPrice) / $indexPrice;
    }

    /**
     * 计算最终资金费率
     */
    protected function calculateRate(float $premiumRate, TradePerpetualFundingConfig $config): float
    {
        // 资金费率 = 溢价率 + 基础利率
        $fundingRate = $premiumRate + $config->base_rate;

        // 限制在配置的范围内
        $fundingRate = max($config->min, min($fundingRate, $config->max));

        return $fundingRate;
    }

    /**
     * 计算下次资金费率收取时间
     */
    protected function calculateNextFundingTime(int $intervalHours): Carbon
    {
        return Carbon::now()->addHours($intervalHours);
    }

    /**
     * 获取当前资金费率
     */
    public function getCurrentFundingRate(int $currencyId): float
    {
        $latestRate = TradePerpetualFundingRate::where('contract_id', $currencyId)
            ->orderBy('funding_time', 'desc')
            ->first();

        return $latestRate ? $latestRate->funding_rate : 0.0;
    }

    /**
     * 检查是否需要计算新的资金费率
     */
    public function shouldCalculateNewRate(int $currencyId): bool
    {
        $config = $this->getFundingConfig($currencyId);
        if (!$config) {
            return false;
        }

        $latestRate = TradePerpetualFundingRate::where('contract_id', $currencyId)
            ->orderBy('funding_time', 'desc')
            ->first();

        if (!$latestRate) {
            return true; // 没有历史记录，需要计算
        }

        // 检查是否到了下次收取时间
        $nextFundingTime = Carbon::parse($latestRate->next_funding_time);
        return Carbon::now()->gte($nextFundingTime);
    }
}
