<?php

declare(strict_types=1);
/**
 * 永续合约配置服务
 */

namespace App\Http\Api\Service\V1\Contract;

use App\Model\Trade\TradeConfig;
use App\Model\Trade\TradeMarginLevel;
use App\Model\Trade\TradePerpetualFundingRate;
use App\Model\Trade\TradePerpetualFundingConfig;
use App\Model\User\VipLevel;
use App\Enum\MarketType;
use App\Enum\MarketData\TickerSyncKey;
use App\Enum\CurrencyConfigKey;
use Carbon\Carbon;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

class PerpetualInfoService
{
    #[Inject]
    protected TradeConfig $tradeConfig;

    #[Inject]
    protected TradeMarginLevel $tradeMarginLevel;

    #[Inject]
    protected VipLevel $vipLevel;

    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected PerpetualFundingRateCalculationService $fundingRateService;

    /**
     * 获取合约交易规则
     */
    public function getExchangeInfo(?int $currencyId = null): array
    {
        $query = $this->tradeConfig->newQuery()
            ->where('market_type', MarketType::MARGIN->value)
            ->with(['currency']);

        if ($currencyId) {
            $query->where('currency_id', $currencyId);
        }

        $configs = $query->get();

        $result = [];
        foreach ($configs as $config) {
            $result[] = [
                'currency_id' => $config->currency_id,
                'symbol' => $config->currency->symbol ?? '',
                'min_trade_num' => $config->min_trade_num,
                'max_trade_num' => $config->max_trade_num,
                'min_trade_price' => $config->min_trade_price,
                'max_trade_price' => $config->max_trade_price,
                'limit_price_rate' => $config->limit_price_rate,
                'maker_limit' => $config->maker_limit,
                'limit_limit' => $config->limit_limit,
                'order_limit' => $config->order_limit,
                'trigger_protect' => $config->trigger_protect,
            ];
        }

        return $result;
    }

    /**
     * 获取杠杆档位配置
     */
    public function getLeverageBracket(?int $currencyId = null): array
    {
        $query = $this->tradeMarginLevel->newQuery()
            ->orderBy('currency_id')
            ->orderBy('level');

        if ($currencyId) {
            $query->where('currency_id', $currencyId);
        }

        $brackets = $query->get();

        $result = [];
        foreach ($brackets as $bracket) {
            $result[] = [
                'currency_id' => $bracket->currency_id,
                'level' => $bracket->level,
                'notional_floor' => $bracket->margin_min,
                'notional_cap' => $bracket->margin_max,
                'maintenance_margin_ratio' => $bracket->margin_rate,
                'cum' => $bracket->cum_amount,
                'min_leverage' => $bracket->leverage_min,
                'max_leverage' => $bracket->leverage_max,
            ];
        }

        return $result;
    }

    /**
     * 获取单个币种的标记价格
     */
    protected function getSingleCurrencyMarkPrice(int $currencyId): ?array
    {
        try {
            $priceData = $this->getCurrentPriceData($currencyId);

            // 获取最新资金费率
            $latestRate = TradePerpetualFundingRate::where('contract_id', $currencyId)
                ->orderBy('funding_time', 'desc')
                ->first();

            return [
                'currency_id' => $currencyId,
                'symbol' => $this->getCurrencySymbol($currencyId),
                'mark_price' => $priceData['mark_price'],
                'index_price' => $priceData['index_price'],
                'estimated_settle_price' => $priceData['index_price'], // 预估结算价格通常等于指数价格
                'last_funding_rate' => $latestRate ? $latestRate->funding_rate : '0.00',
                'next_funding_time' => $latestRate ? (is_string($latestRate->next_funding_time) ? strtotime($latestRate->next_funding_time) * 1000 : $latestRate->next_funding_time->timestamp * 1000) : (time() + 8 * 3600) * 1000,
                'time' => time() * 1000,
            ];
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 获取资金费率
     */
    public function getFundingRate(?int $currencyId = null): array
    {
        if ($currencyId) {
            // 获取指定币种的资金费率
            return $this->getSingleCurrencyFundingRate($currencyId);
        }

        // 获取所有币种的资金费率
        $configs = TradePerpetualFundingConfig::where('status', 1)->get();
        $result = [];

        foreach ($configs as $config) {
            $fundingRateData = $this->getSingleCurrencyFundingRate($config->currency_id);
            if ($fundingRateData) {
                $result[] = $fundingRateData;
            }
        }

        return $result;
    }

    /**
     * 获取资金费率历史
     */
    public function getFundingRateHistory(int $currencyId, int $limit = 100, ?int $startTime = null, ?int $endTime = null): array
    {
        $query = TradePerpetualFundingRate::where('contract_id', $currencyId)
            ->orderBy('funding_time', 'desc');

        // 时间范围过滤
        if ($startTime) {
            $query->where('funding_time', '>=', Carbon::createFromTimestamp($startTime));
        }

        if ($endTime) {
            $query->where('funding_time', '<=', Carbon::createFromTimestamp($endTime));
        }

        $fundingRates = $query->limit($limit)->get();

        $result = [];
        foreach ($fundingRates as $rate) {
            $result[] = [
                'currency_id' => $rate->contract_id,
                'symbol' => $this->getCurrencySymbol($rate->contract_id),
                'funding_rate' => $rate->funding_rate,
                'funding_time' => is_string($rate->funding_time) ? strtotime($rate->funding_time) * 1000 : $rate->funding_time->timestamp * 1000,
                'mark_price' => $rate->mark_price,
                'index_price' => $rate->index_price,
            ];
        }

        return $result;
    }

    /**
     * 获取单个币种的资金费率
     */
    protected function getSingleCurrencyFundingRate(int $currencyId): ?array
    {
        try {
            // 获取最新的资金费率记录
            $latestRate = TradePerpetualFundingRate::where('contract_id', $currencyId)
                ->orderBy('funding_time', 'desc')
                ->first();

            // 获取配置信息
            $config = TradePerpetualFundingConfig::where('currency_id', $currencyId)
                ->where('status', 1)
                ->first();
            if (!$config) {
                return null;
            }

            // 获取当前价格数据
            $priceData = $this->getCurrentPriceData($currencyId);

            // 如果没有历史费率记录，计算当前费率
            if (!$latestRate) {
                $currentRate = $this->fundingRateService->calculateFundingRate($currencyId);
                if (!$currentRate) {
                    return null;
                }

                return [
                    'currency_id' => $currencyId,
                    'symbol' => $this->getCurrencySymbol($currencyId),
                    'funding_rate' => $currentRate['funding_rate'],
                    'funding_time' => is_string($currentRate['funding_time']) ? strtotime($currentRate['funding_time']) * 1000 : $currentRate['funding_time']->timestamp * 1000,
                    'next_funding_time' => is_string($currentRate['next_funding_time']) ? strtotime($currentRate['next_funding_time']) * 1000 : $currentRate['next_funding_time']->timestamp * 1000,
                    'mark_price' => $currentRate['mark_price'],
                    'index_price' => $currentRate['index_price'],
                    // 配置数据
                    'interval' => $config->interval,
                    'base_rate' => $config->base_rate,
                    'min_rate' => $config->min,
                    'max_rate' => $config->max,
                ];
            }

            return [
                'currency_id' => $currencyId,
                'symbol' => $this->getCurrencySymbol($currencyId),
                'funding_rate' => $latestRate->funding_rate,
                'funding_time' => is_string($latestRate->funding_time) ? strtotime($latestRate->funding_time) * 1000 : $latestRate->funding_time->timestamp * 1000,
                'next_funding_time' => is_string($latestRate->next_funding_time) ? strtotime($latestRate->next_funding_time) * 1000 : $latestRate->next_funding_time->timestamp * 1000,
                'mark_price' => $priceData['mark_price'] ?? $latestRate->mark_price,
                'index_price' => $priceData['index_price'] ?? $latestRate->index_price,
                // 配置数据
                'interval' => $config->interval,
                'base_rate' => $config->base_rate,
                'min_rate' => $config->min,
                'max_rate' => $config->max,
            ];

        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 获取当前价格数据
     */
    protected function getCurrentPriceData(int $currencyId): array
    {
        try {
            // 获取合约价格（标记价格）
            $contractKey = TickerSyncKey::getOuterTradeKey($currencyId, 5);
            $markPrice = $this->redis->hGet($contractKey, 'price');

            // 获取现货价格（指数价格）
            $spotKey = TickerSyncKey::getOuterTradeKey($currencyId, 1);
            $indexPrice = $this->redis->hGet($spotKey, 'price');

            return [
                'mark_price' => $markPrice ? (float)$markPrice : 0.0,
                'index_price' => $indexPrice ? (float)$indexPrice : 0.0,
            ];
        } catch (\Exception $e) {
            return [
                'mark_price' => 0.0,
                'index_price' => 0.0,
            ];
        }
    }

    /**
     * 获取仓位风险档位
     */
    public function getPositionRisk(int $currencyId): array
    {
        $marginLevels = TradeMarginLevel::where('currency_id', $currencyId)
            ->orderBy('level')
            ->get();

        $result = [];
        foreach ($marginLevels as $level) {
            $result[] = [
                'currency_id' => $level->currency_id,
                'level' => $level->level,
                'notional_floor' => $level->margin_min,
                'notional_cap' => $level->margin_max,
                'maintenance_margin_ratio' => $level->margin_rate,
                'cum' => $level->cum_amount,
                'min_leverage' => $level->leverage_min,
                'max_leverage' => $level->leverage_max,
            ];
        }

        return $result;
    }

    /**
     * 获取币种符号
     */
    protected function getCurrencySymbol(int $currencyId): string
    {
        try {
            $currencyKey = CurrencyConfigKey::getCurrencyKey($currencyId);
            $symbol = $this->redis->hGet($currencyKey, 'symbol');

            return $symbol ?: '';
        } catch (\Exception $e) {
            return '';
        }
    }
}