<?php

declare(strict_types=1);
/**
 * DynamicsService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-04
 * Website:xxx
 */

namespace App\Http\Api\Service\Dynamics;

use App\Http\Api\Request\Dynamics\DynamicsRequest;
use App\Model\Article\Dynamics;
use App\Model\Article\DynamicsCurrency;
use App\Model\Article\Enums\DynamicsAuth;
use App\Model\Article\Enums\DynamicsType;
use App\Model\User\User;
use App\QueryBuilder\QueryBuilder;
use App\Repository\Article\DynamicsRepository;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use RuntimeException;

class DynamicsService
{
    #[Inject]
    protected DynamicsRepository $repository;
    /**
     * 查询动态列表
     * Summary of list
     * @return array
     */
    public function list(DynamicsRequest $request)
    {
        $page = $request->input('page',default: 1);
        $pageSize = $request->input('page_size',10);
        // $params = $request->all();
        // $params = array_merge($params, [
        //     'pid_id' => 0,
        // ]);
        // return $this->repository->page($params,$page,$pageSize);
        // $query = Dynamics::query();
        // $query->where('pid_id',0);
        // $query->orderBy('id','desc');
        // $query->with(['user:id,display_name,avatar','hottopic']);
        // return $query->paginate($page, $pageSize);

        //通过权限过滤 公开，关注我的【】
        //关注用户ID
        $userId = $request->userId();
        $userIds = User::whereJsonContains('concern_uids', $userId)->pluck('id');
        return QueryBuilder::for(Dynamics::class, $request)
                    ->where(function ($query) use ($userIds) {
                    $query->where('look_auth', DynamicsAuth::PUBLIC->value)
                        ->orWhere(function ($subQuery) use ($userIds) {
                            $subQuery->where('look_auth', DynamicsAuth::LIKED->value)
                                    ->whereIn("user_id", $userIds);
                        });
                })
                ->with(['user:id,display_name,avatar','hottopic'])
                ->filters(['type', 'title'])
                ->orderByDesc('id')
                ->allowedSorts(['id', 'created_at'])
                ->pagex(function($item){
                    if($item->dynamics_currency){
                        $item->dynamics_currency = DynamicsCurrency::query()->whereIn('symbol_id',$item->dynamics_currency)->get();
                    }else{
                        $item->dynamics_currency = [];
                    }
                    return $item;
                });
    }

    /**
     * 获取动态详情
     * Summary of detail
     * @param mixed $id
     */
    public function detail($id) {
        $this->repository->getModel()->increment(Dynamics::FIELD_LOOK_NUM);
        $detail = $this->repository->getModel()->with(['user:id,display_name,avatar'])->where('id',$id)->first();
        if ($detail) {
            $detail->makeHidden([
                Dynamics::FIELD_COLLECT_UIDS,
                Dynamics::FIELD_FORWARD_UIDS,
                Dynamics::FIELD_CONCERN_UIDS,
                Dynamics::FIELD_USER_ID
            ]);
        }
        return $detail;
    }

    /**
     * 发布动态
     * Summary of create
     * @param mixed $request
     */
    public function create(DynamicsRequest $request) {
        $params = $request->all();
        $params['user_id'] = $request->userId();
        return $this->repository->create($params);
    }

    /**
     * 修改动态
     * Summary of update
     * @param mixed $request
     */
    public function update(DynamicsRequest $request) {
        $params = $request->all();
        $id = $params['id'];
        unset($params['id']);
        return $this->repository->updateById($id,$params);
    }

    /**
     * 删除动态
     * Summary of delete
     * @param mixed $request
     */
    public function delete(DynamicsRequest $request) {
        return $this->repository->getModel()->where(['id'=> $request->input('id')])->delete();
    }

    /**
     * 评论
     * Summary of comment
     * @param mixed $request
     */
    public function comment(DynamicsRequest $request) {
        $params = $request->all();
        $params['pid_id'] = $params['dynamics_id'];
        $params['user_id'] = $request->userId();
        unset( $params['dynamics_id']);
        return $this->repository->create($params);
    }

    /**
     * 评论列表
     * Summary of comment
     * @param mixed $request
     */
    public function commentList(DynamicsRequest $request) {
        $page = $request->input('page',1);
        $pageSize = $request->input('page_size',10);
        $params = $request->all();
        $params = array_merge($params, [
            'pid_id' => $params['dynamics_id'],
        ]);
        return $this->repository->page($params,$page,$pageSize);
    }

    /**
     * 点赞
     * Summary of liked
     * @param mixed $request
     */
    public function liked(DynamicsRequest $request) {
        $params = $request->all();
        $lieked = $this->repository->getModel()->where('id',$params['dynamics_id'])->value('liked_uids');
        // 判断是否已点赞 $lieked 是否包含当前用户
        $lieked = $lieked ?? [];
        if(!$lieked) {
            $lieked = [$request->userId()];
        }else if (!in_array($request->userId(),$lieked)) {
            $lieked = array_merge($lieked,[$request->userId()]);
        }
        $lieked = json_encode($lieked);
        Db::transaction(function() use($params,$lieked) {
            $this->repository->getModel()->where('id',$params['dynamics_id'])->update(['liked_uids'=>$lieked]);
            $this->repository->getModel()->where('id',$params['dynamics_id'])->increment('liked');
        });
        return true;
    }

    /**
     * 取消点赞
     * Summary of unLiked
     * @param mixed $request
     */
    public function unLiked(DynamicsRequest $request) {
        $params = $request->all();
        $lieked = $this->repository->getModel()->where('id',$params['dynamics_id'])->value('liked_uids');
        // 判断是否已点赞 $lieked 是否包含当前用户
        $lieked = $lieked ?? [];
        if (in_array($request->userId(), $lieked)) {
            $key = array_search($request->userId(), $lieked);
            if ($key !== false) {
                unset($lieked[$key]);
            }
            DB::transaction(function() use($params,$lieked) {
                $this->repository->getModel()->where('id', $params['dynamics_id'])->update(['liked_uids' => json_encode(array_values($lieked))]);
                $this->repository->getModel()->where('id',$params['dynamics_id'])->increment('liked');
            });
        }
        return true;
    }


    /**
     * 转发
     * Summary of forward
     * @param mixed $request
     */
    public function forward(DynamicsRequest $request): bool {
        $params = $request->all();
        $forward = $this->repository->getModel()->where('id',$params['dynamics_id'])->value('forward_uids');
        // 判断是否已关注 $forward 是否包含当前用户
        $forward = $forward ?? [];
        if(!$forward) {
            $forward = [$request->userId()];
        }else if (!in_array($request->userId(),$forward)) {
            $forward = array_merge($forward,[$request->userId()]);
        }
        $forward = json_encode($forward);
        $this->repository->getModel()->where('id',$params['dynamics_id'])->update(['forward_uids'=>$forward]);
        return true;
    }

    /**
     * 取消转发
     * Summary of unForward
     * @param mixed $request
     */
    public function unForward(DynamicsRequest $request): bool {
        $params = $request->all();
        $forward = $this->repository->getModel()->where('id',$params['dynamics_id'])->value('forward_uids');
        // 判断是否已关注 $forward 是否包含当前用户
        $forward = $forward ?? [];
        if (in_array($request->userId(), $forward)) {
            $key = array_search($request->userId(), $forward);
            if ($key !== false) {
                unset($forward[$key]);
            }
            $this->repository->getModel()->where('id', $params['dynamics_id'])->update(['forward_uids' => json_encode(array_values($forward))]);
        }
        return true;
    }
    /**
     * 收藏
     * Summary of collect
     * @param mixed $request
     */
    public function collect(DynamicsRequest $request): bool {
        $params = $request->all();
        $collect = $this->repository->getModel()->where('id',$params['dynamics_id'])->value('collect_uids');
        // 判断是否已关注 $collect 是否包含当前用户
        $collect = $collect ?? [];
        if(!$collect) {
            $collect = [$request->userId()];
        }else if (!in_array($request->userId(),$collect)) {
            $collect = array_merge($collect,[$request->userId()]);
        }
        $collect = json_encode($collect);
        $this->repository->getModel()->where('id',$params['dynamics_id'])->update(['collect_uids'=>$collect]);
        return true;
    }

    /**
     * 取消收藏
     * Summary of unCollect
     * @param mixed $request
     */
    public function unCollect(DynamicsRequest $request): bool {
        $params = $request->all();
        $collect = $this->repository->getModel()->where('id',$params['dynamics_id'])->value('collect_uids');
        // 判断是否已关注 $collect 是否包含当前用户
        $collect = $collect ?? [];
        if (in_array($request->userId(), $collect)) {
            $key = array_search($request->userId(), $collect);
            if ($key !== false) {
                unset($collect[$key]);
            }
            $this->repository->getModel()->where('id', $params['dynamics_id'])->update(['collect_uids' => json_encode(array_values($collect))]);
        }
        return true;
    }

    /**
     * 用户信息
     * Summary of userInfo
     * @param \App\Http\Api\Request\Dynamics\DynamicsRequest $request
     * @return void
     */
    public function userInfo($userId) {
        //用户的头像，名称，简介，粉丝数量，关注人数，发布数量
        $user = User::where(['id'=>$userId])->first([
                    User::FIELD_ID,
                    User::FIELD_DISPLAY_NAME,
                    User::FIELD_AVATAR,
                    User::FIELD_INTRODCTION,
                    User::FIELD_CONCERN_UIDS
                ]);
        $rtnArray = $user->toArray();
        $rtnArray['fan_num'] = count($rtnArray['concern_uids']);//粉丝数量
        $rtnArray['article_num'] = Dynamics::where(['user_id'=>$userId])->count();//发布数量
        $rtnArray['concern_num'] = User::whereJsonContains('concern_uids', $userId)->count();//关注人数
        return $rtnArray;
    }

    /**
     * 某个人的文章列表
     * Summary of userArircle
     * @param mixed $request
     * @param mixed $userId
     * @return QueryBuilder[]|\Hyperf\Contract\LengthAwarePaginatorInterface|\Hyperf\Database\Model\Collection
     */
    public function userArircle($request,$userId)    {
        return QueryBuilder::for(Dynamics::class, $request)
                ->with(['user:id,display_name,avatar','hottopic'])
                ->where('type',DynamicsType::ARTICLE->value)
                ->where('user_id',$userId)
                ->where('pid_id',0)
                ->filters(['title'])
                ->orderByDesc('id')
                ->allowedSorts(['id', 'created_at'])
                ->pagex(function($item){
                    if($item->dynamics_currency){
                        $item->dynamics_currency = DynamicsCurrency::query()->whereIn('id',$item->dynamics_currency)->get();
                    }else{
                        $item->dynamics_currency = [];
                    }
                    return $item;
            });
    }

    /**
     * 某个人的动态列表
     * Summary of userDynamics
     * @param mixed $request
     * @param mixed $userId
     * @return QueryBuilder[]|\Hyperf\Contract\LengthAwarePaginatorInterface|\Hyperf\Database\Model\Collection
     */
    public function userDynamics($request,$userId)    {
        $type = $request->input('type', '1');
        $query = QueryBuilder::for(Dynamics::class, $request)
                    ->with(['user:id,display_name,avatar','hottopic'])
                    ->where('type',DynamicsType::POINT->value);
        if($type == '1'){
            //全部动态
            $query->where(function ($query) use ($userId) {
                $query->where('user_id', $userId)
                    ->orWhereRaw("JSON_CONTAINS(forward_uids, ?)", [$userId])
                    ->orWhereRaw("JSON_CONTAINS(collect_uids, ?)", [$userId])
                    ->orWhereRaw("JSON_CONTAINS(liked_uids, ?)", [$userId]);
            });
        }else if($type == '2'){
            //他互动的（他收藏，转发，回复的）
            $query->where(function ($query) use ($userId) {
                    $query->whereRaw("JSON_CONTAINS(forward_uids, ?)", [$userId])
                    ->orWhereRaw("JSON_CONTAINS(collect_uids, ?)", [$userId])
                    ->orWhereRaw("JSON_CONTAINS(liked_uids, ?)", [$userId]);
            });
        }else if($type == '3'){
            //他发布的
            $query->where('user_id', $userId);
        }else{
            throw new RuntimeException('动态类型错误');
        }
        return $query->where('pid_id',0)
                    ->filters(['title'])
                    ->orderByDesc('id')
                    ->allowedSorts(['id', 'created_at'])
                    ->pagex(function($item){
                        if($item->dynamics_currency){
                            $item->dynamics_currency = DynamicsCurrency::query()->whereIn('id',$item->dynamics_currency)->get();
                        }else{
                            $item->dynamics_currency = [];
                        }
                        return $item;
                });
    }


}
