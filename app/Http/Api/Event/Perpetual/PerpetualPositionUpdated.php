<?php

declare(strict_types=1);

namespace App\Http\Api\Event\Perpetual;

use App\Model\Trade\TradePerpetualPosition;

/**
 * 永续合约仓位更新事件
 */
class PerpetualPositionUpdated
{
    public TradePerpetualPosition $position;
    public string $action;
    public array $changes;
    public array $originalData;
    public float $timestamp;

    public function __construct(
        TradePerpetualPosition $position,
        string $action,
        array $changes = [],
        array $originalData = []
    ) {
        $this->position = $position;
        $this->action = $action;
        $this->changes = $changes;
        $this->originalData = $originalData;
        $this->timestamp = microtime(true);
    }

    /**
     * 获取事件数据
     */
    public function getEventData(): array
    {
        return [
            'event_type' => 'position_updated',
            'position_id' => $this->position->id,
            'user_id' => $this->position->user_id,
            'currency_id' => $this->position->currency_id,
            'action' => $this->action,
            'changes' => $this->changes,
            'original_data' => $this->originalData,
            'current_data' => $this->position->toArray(),
            'timestamp' => $this->timestamp,
            'created_at' => date('Y-m-d H:i:s', (int)$this->timestamp)
        ];
    }

    /**
     * 判断是否需要更新缓存
     */
    public function shouldUpdateCache(): bool
    {
        $cacheUpdateActions = [
            'created',
            'updated',
            'opened',
            'increased',
            'decreased',
            'closed',
            'liquidated',
            'margin_adjusted',
        ];

        return in_array($this->action, $cacheUpdateActions);
    }

    /**
     * 判断是否需要触发风险计算
     */
    public function shouldTriggerRiskCalculation(): bool
    {
        $riskCalculationActions = [
            'opened',
            'increased',
            'decreased',
            'margin_adjusted',
        ];

        return in_array($this->action, $riskCalculationActions);
    }

    /**
     * 判断是否是关键操作
     */
    public function isCriticalAction(): bool
    {
        $criticalActions = [
            'liquidated',
            'closed',
            'margin_call',
        ];

        return in_array($this->action, $criticalActions);
    }
}
