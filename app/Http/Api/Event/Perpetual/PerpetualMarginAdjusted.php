<?php

declare(strict_types=1);

namespace App\Http\Api\Event\Perpetual;

use App\Model\Trade\TradePerpetualPosition;

/**
 * 永续合约保证金调整事件
 */
class PerpetualMarginAdjusted
{
    public TradePerpetualPosition $position;
    public float $adjustmentAmount;
    public string $adjustmentType;
    public float $originalMargin;
    public float $newMargin;
    public float $timestamp;

    public function __construct(
        TradePerpetualPosition $position,
        float $adjustmentAmount,
        string $adjustmentType,
        float $originalMargin
    ) {
        $this->position = $position;
        $this->adjustmentAmount = $adjustmentAmount;
        $this->adjustmentType = $adjustmentType;
        $this->originalMargin = $originalMargin;
        $this->newMargin = $position->margin_amount;
        $this->timestamp = microtime(true);
    }

    /**
     * 获取事件数据
     */
    public function getEventData(): array
    {
        return [
            'event_type' => 'margin_adjusted',
            'position_id' => $this->position->id,
            'user_id' => $this->position->user_id,
            'currency_id' => $this->position->currency_id,
            'side' => $this->position->side,
            'margin_mode' => $this->position->margin_mode,
            'adjustment_amount' => $this->adjustmentAmount,
            'adjustment_type' => $this->adjustmentType,
            'original_margin' => $this->originalMargin,
            'new_margin' => $this->newMargin,
            'timestamp' => $this->timestamp,
            'created_at' => date('Y-m-d H:i:s', (int)$this->timestamp)
        ];
    }

    /**
     * 判断是否是增加保证金
     */
    public function isIncreaseMargin(): bool
    {
        return $this->adjustmentType === 'increase';
    }

    /**
     * 判断是否是减少保证金
     */
    public function isDecreaseMargin(): bool
    {
        return $this->adjustmentType === 'decrease';
    }
}
