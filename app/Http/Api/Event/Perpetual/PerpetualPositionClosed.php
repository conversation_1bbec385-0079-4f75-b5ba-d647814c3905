<?php

declare(strict_types=1);

namespace App\Http\Api\Event\Perpetual;

use App\Model\Trade\TradePerpetualPosition;

/**
 * 永续合约平仓事件
 */
class PerpetualPositionClosed
{
    public TradePerpetualPosition $position;
    public float $closedQuantity;
    public float $closePrice;
    public float $realizedPnl;
    public array $orderData;
    public float $timestamp;

    public function __construct(
        TradePerpetualPosition $position,
        float $closedQuantity,
        float $closePrice,
        float $realizedPnl,
        array $orderData = []
    ) {
        $this->position = $position;
        $this->closedQuantity = $closedQuantity;
        $this->closePrice = $closePrice;
        $this->realizedPnl = $realizedPnl;
        $this->orderData = $orderData;
        $this->timestamp = microtime(true);
    }

    /**
     * 获取事件数据
     */
    public function getEventData(): array
    {
        return [
            'event_type' => 'position_closed',
            'position_id' => $this->position->id,
            'user_id' => $this->position->user_id,
            'currency_id' => $this->position->currency_id,
            'side' => $this->position->side,
            'margin_mode' => $this->position->margin_mode,
            'closed_quantity' => $this->closedQuantity,
            'close_price' => $this->closePrice,
            'realized_pnl' => $this->realizedPnl,
            'remaining_quantity' => $this->position->quantity,
            'order_data' => $this->orderData,
            'timestamp' => $this->timestamp,
            'created_at' => date('Y-m-d H:i:s', (int)$this->timestamp)
        ];
    }

    /**
     * 判断是否完全平仓
     */
    public function isFullyClosed(): bool
    {
        return $this->position->quantity <= 0;
    }
}
