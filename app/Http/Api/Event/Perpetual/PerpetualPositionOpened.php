<?php

declare(strict_types=1);

namespace App\Http\Api\Event\Perpetual;

use App\Model\Trade\TradePerpetualPosition;

/**
 * 永续合约开仓事件
 */
class PerpetualPositionOpened
{
    public TradePerpetualPosition $position;
    public array $orderData;
    public float $timestamp;

    public function __construct(TradePerpetualPosition $position, array $orderData = [])
    {
        $this->position = $position;
        $this->orderData = $orderData;
        $this->timestamp = microtime(true);
    }

    /**
     * 获取事件数据
     */
    public function getEventData(): array
    {
        return [
            'event_type' => 'position_opened',
            'position_id' => $this->position->id,
            'user_id' => $this->position->user_id,
            'currency_id' => $this->position->currency_id,
            'side' => $this->position->side,
            'margin_mode' => $this->position->margin_mode,
            'quantity' => $this->position->quantity,
            'entry_price' => $this->position->entry_price,
            'leverage' => $this->position->leverage,
            'margin_amount' => $this->position->margin_amount,
            'order_data' => $this->orderData,
            'timestamp' => $this->timestamp,
            'created_at' => date('Y-m-d H:i:s', (int)$this->timestamp)
        ];
    }
}
