<?php

declare(strict_types=1);

namespace App\Http\Api\Event\Perpetual;

use App\Model\Trade\TradePerpetualPosition;

/**
 * 永续合约强平事件
 */
class PerpetualPositionLiquidated
{
    public TradePerpetualPosition $position;
    public float $liquidationPrice;
    public float $liquidatedQuantity;
    public float $liquidationFee;
    public array $riskInfo;
    public array $liquidationOrderData;
    public float $timestamp;

    public function __construct(
        TradePerpetualPosition $position,
        float $liquidationPrice,
        float $liquidatedQuantity,
        float $liquidationFee,
        array $riskInfo = [],
        array $liquidationOrderData = []
    ) {
        $this->position = $position;
        $this->liquidationPrice = $liquidationPrice;
        $this->liquidatedQuantity = $liquidatedQuantity;
        $this->liquidationFee = $liquidationFee;
        $this->riskInfo = $riskInfo;
        $this->liquidationOrderData = $liquidationOrderData;
        $this->timestamp = microtime(true);
    }

    /**
     * 获取事件数据
     */
    public function getEventData(): array
    {
        return [
            'event_type' => 'position_liquidated',
            'position_id' => $this->position->id,
            'user_id' => $this->position->user_id,
            'currency_id' => $this->position->currency_id,
            'side' => $this->position->side,
            'margin_mode' => $this->position->margin_mode,
            'liquidation_price' => $this->liquidationPrice,
            'liquidated_quantity' => $this->liquidatedQuantity,
            'liquidation_fee' => $this->liquidationFee,
            'remaining_quantity' => $this->position->quantity,
            'risk_info' => $this->riskInfo,
            'liquidation_order_data' => $this->liquidationOrderData,
            'timestamp' => $this->timestamp,
            'created_at' => date('Y-m-d H:i:s', (int)$this->timestamp)
        ];
    }

    /**
     * 判断是否完全强平
     */
    public function isFullyLiquidated(): bool
    {
        return $this->position->quantity <= 0;
    }

    /**
     * 获取强平原因
     */
    public function getLiquidationReason(): string
    {
        return $this->riskInfo['liquidation_reason'] ?? 'margin_insufficient';
    }
}
