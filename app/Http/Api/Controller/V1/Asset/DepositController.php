<?php

declare(strict_types=1);

namespace App\Http\Api\Controller\V1\Asset;

use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Api\Request\V1\Asset\DepositRequest;
use App\Http\Api\Service\V1\Asset\DepositService;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;

#[Controller(prefix: 'api/v1/deposit')]
#[Middleware(middleware: TokenMiddleware::class)]
class DepositController extends AbstractController
{
    /**
     * @var DepositService $service
     */
    public mixed $service;

    /**
     * @var DepositRequest $rules
     */
    public mixed $rules;

    /**
     * 获取支持充值的币种
     */
    #[GetMapping('currencies')]
    public function getSupportedCurrencies(): Result
    {
        $this->requestValidate($this->rules->getSupportedCurrenciesRules());

        try {
            $result = $this->service->getSupportedCurrencies();
            return $this->success($result, '获取支持充值币种成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取充值地址
     */
    #[GetMapping('address')]
    public function getDepositAddress(): Result
    {
        $this->requestValidate($this->rules->getDepositAddressRules());

        $currencyId = (int)$this->request->input('currency_id');
        $chainId = $this->request->input('chain_id');

        $result = $this->service->getOrCreateDepositAddress($currencyId, $chainId);
        return $this->success($result, '获取充值地址成功');
    }
}
