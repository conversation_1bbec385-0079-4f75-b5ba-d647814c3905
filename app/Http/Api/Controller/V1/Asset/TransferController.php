<?php

declare(strict_types=1);

namespace App\Http\Api\Controller\V1\Asset;

use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Api\Request\V1\Asset\TransferRequest;
use App\Http\Api\Service\V1\Asset\TransferService;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\HttpServer\Annotation\PostMapping;

#[Controller(prefix: 'api/v1/transfer')]
#[Middleware(middleware: TokenMiddleware::class)]
class TransferController extends AbstractController
{
    /**
     * @var TransferService $service
     */
    public mixed $service;

    /**
     * @var TransferRequest $rules
     */
    public mixed $rules;

    /**
     * 获取指定账户类型有余额的币种列表
     */
    #[GetMapping('currencies')]
    public function getAccountCurrencies(): Result
    {
        $this->requestValidate($this->rules->getAccountCurrenciesRules());

        try {
            $accountType = (int)$this->request->input('account_type');
            $result = $this->service->getAccountCurrencies($accountType);
            return $this->success($result, '获取账户币种列表成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 执行资金划转
     */
    #[PostMapping('submit')]
    public function transfer(): Result
    {
        $this->requestValidate($this->rules->transferRules());

        $data = [
            'currency_id' => (int)$this->request->input('currency_id'),
            'from_account_type' => (int)$this->request->input('from_account_type'),
            'to_account_type' => (int)$this->request->input('to_account_type'),
            'amount' => (float)$this->request->input('amount'),
            'remark' => $this->request->input('remark', ''),
            'isolated_asset_type' => $this->request->input('isolated_asset_type', ''),
            'isolated_target_currency_id' => $this->request->input('isolated_target_currency_id'),
        ];

        $result = $this->service->transfer($data);
        return $this->success($result, '资金划转成功');
    }

    /**
     * 获取划转记录
     */
    #[GetMapping('records')]
    public function getTransferRecords(): Result
    {
        $this->requestValidate($this->rules->getTransferRecordsRules());

        try {
            $filters = [
                'currency_id' => $this->request->input('currency_id'),
                'from_account_type' => $this->request->input('from_account_type'),
                'to_account_type' => $this->request->input('to_account_type'),
                'status' => $this->request->input('status'),
                'start_time' => $this->request->input('start_time'),
                'end_time' => $this->request->input('end_time'),
                'page' => (int)($this->request->input('page', 1)),
                'limit' => (int)($this->request->input('limit', 20)),
            ];

            $result = $this->service->getTransferRecords($filters);
            return $this->success($result, '获取划转记录成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取资金账户类型列表
     */
    #[GetMapping('account-types')]
    public function getAccountTypes(): Result
    {
        $this->requestValidate($this->rules->getAccountTypesRules());

        try {
            $lang = $this->request->input('lang', 'zh-CN');
            $result = $this->service->getAccountTypes($lang);
            return $this->success($result, '获取账户类型列表成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
