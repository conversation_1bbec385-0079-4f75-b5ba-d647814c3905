<?php

declare(strict_types=1);

namespace App\Http\Api\Controller\V1\Asset;

use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Api\Request\V1\Asset\AssetRequest;
use App\Http\Api\Service\V1\Asset\AssetService;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;

#[Controller(prefix: 'api/v1/asset')]
#[Middleware(middleware: TokenMiddleware::class)]
class AssetController extends AbstractController
{
    /**
     * @var AssetService $service
     */
    public mixed $service;

    /**
     * @var AssetRequest $rules
     */
    public mixed $rules;

    /**
     * 获取用户全部资产
     */
    #[GetMapping('balance')]
    public function getBalance(): Result
    {
        $this->requestValidate($this->rules->getBalanceRules());

        try {
            $result = $this->service->getUserAllAssets();
            return $this->success($result, '获取用户资产成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取单币种资产详情
     */
    #[GetMapping('currency/{currency_id}')]
    public function getCurrencyDetail(): Result
    {
        $this->requestValidate($this->rules->getCurrencyDetailRules());

        try {
            $currencyId = (int)$this->request->route('currency_id');
            $result = $this->service->getCurrencyAssetDetail($currencyId);
            return $this->success($result, '获取币种资产详情成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取资产流水记录
     */
    #[GetMapping('flows')]
    public function getFlows(): Result
    {
        $this->requestValidate($this->rules->getFlowsRules());

        try {
            $filters = [
                'currency_id' => $this->request->input('currency_id'),
                'account_type' => $this->request->input('account_type'),
                'flow_type' => $this->request->input('flow_type'),
                'start_time' => $this->request->input('start_time'),
                'end_time' => $this->request->input('end_time'),
                'page' => (int)($this->request->input('page', 1)),
                'limit' => (int)($this->request->input('limit', 20)),
            ];

            $result = $this->service->getAssetFlows($filters);
            return $this->success($result, '获取资产流水记录成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取流水类型解释
     */
    #[GetMapping('flow-types')]
    public function getFlowTypes(): Result
    {
        $this->requestValidate($this->rules->getFlowTypesRules());

        try {
            $lang = $this->request->input('lang', 'zh-CN');
            $result = $this->service->getFlowTypes($lang);
            return $this->success($result, '获取流水类型解释成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取资产统计概览
     */
    #[GetMapping('summary')]
    public function getSummary(): Result
    {
        $this->requestValidate($this->rules->getSummaryRules());

        try {
            $result = $this->service->getAssetSummary();
            return $this->success($result, '获取资产统计概览成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
