<?php

declare(strict_types=1);

namespace App\Http\Api\Controller\V1\Asset;

use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Api\Request\V1\Asset\WithdrawRequest;
use App\Http\Api\Service\V1\Asset\WithdrawService;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\HttpServer\Annotation\PostMapping;

#[Controller(prefix: 'api/v1/withdraw')]
#[Middleware(middleware: TokenMiddleware::class)]
class WithdrawController extends AbstractController
{
    /**
     * @var WithdrawService $service
     */
    public mixed $service;

    /**
     * @var WithdrawRequest $rules
     */
    public mixed $rules;

    /**
     * 获取可提币种
     */
    #[GetMapping('currencies')]
    public function getAvailableCurrencies(): Result
    {
        $this->requestValidate($this->rules->getAvailableCurrenciesRules());

        try {
            $result = $this->service->getAvailableCurrencies();
            return $this->success($result, '获取可提币种成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取提币配置
     */
    #[GetMapping('config')]
    public function getWithdrawConfig(): Result
    {
        $this->requestValidate($this->rules->getWithdrawConfigRules());

        try {
            $currencyId = (int)$this->request->input('currency_id');
            $result = $this->service->getWithdrawConfig($currencyId);
            return $this->success($result, '获取提币配置成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 提交提币申请
     */
    #[PostMapping('submit')]
    public function submitWithdraw(): Result
    {
        $this->requestValidate($this->rules->submitWithdrawRules());

        $data = [
            'currency_id' => (int)$this->request->input('currency_id'),
            'withdraw_type' => (int)$this->request->input('withdraw_type'),
            'amount' => (float)$this->request->input('amount'),
            'chain_id' => $this->request->input('chain_id'),
            'to_address' => $this->request->input('to_address'),
            'memo' => $this->request->input('memo'),
            'to_email' => $this->request->input('to_email'),
            'to_phone' => $this->request->input('to_phone'),
            'to_phone_country_code' => $this->request->input('to_phone_country_code'),
            'to_uid' => $this->request->input('to_uid'),
        ];

        $result = $this->service->submitWithdraw($data);
        return $this->success($result, '提交提币申请成功');
    }

    /**
     * 查询提币记录
     */
    #[GetMapping('records')]
    public function getWithdrawRecords(): Result
    {
        $this->requestValidate($this->rules->getWithdrawRecordsRules());

        try {
            $filters = [
                'currency_id' => $this->request->input('currency_id'),
                'withdraw_type' => $this->request->input('withdraw_type'),
                'status' => $this->request->input('status'),
                'start_time' => $this->request->input('start_time'),
                'end_time' => $this->request->input('end_time'),
                'page' => (int)($this->request->input('page', 1)),
                'limit' => (int)($this->request->input('limit', 20)),
            ];

            $result = $this->service->getWithdrawRecords($filters);
            return $this->success($result, '获取提币记录成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 添加快速提币地址
     */
    #[PostMapping('address/add')]
    public function addWithdrawAddress(): Result
    {
        $this->requestValidate($this->rules->addWithdrawAddressRules());

        $data = [
            'currency_id' => (int)$this->request->input('currency_id'),
            'chain_id' => $this->request->input('chain_id'),
            'address' => $this->request->input('address'),
            'remark' => $this->request->input('remark', ''),
        ];

        $result = $this->service->addWithdrawAddress($data);
        return $this->success($result, '添加提币地址成功');

    }

    /**
     * 获取快速提币地址列表
     */
    #[GetMapping('address/list')]
    public function getWithdrawAddresses(): Result
    {
        $this->requestValidate($this->rules->getWithdrawAddressesRules());

        try {
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;
            $result = $this->service->getWithdrawAddresses($currencyId);
            return $this->success($result, '获取提币地址列表成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除快速提币地址
     */
    #[DeleteMapping('address/{id}')]
    public function deleteWithdrawAddress(): Result
    {
        $this->requestValidate($this->rules->deleteWithdrawAddressRules());

        try {
            $addressId = (int)$this->request->route('id');
            $result = $this->service->deleteWithdrawAddress($addressId);

            if ($result) {
                return $this->success([], '删除提币地址成功');
            } else {
                return $this->error('删除提币地址失败');
            }
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
