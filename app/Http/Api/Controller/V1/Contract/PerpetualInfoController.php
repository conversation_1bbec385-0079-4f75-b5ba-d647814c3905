<?php

declare(strict_types=1);
/**
 * 永续合约信息控制器
 */

namespace App\Http\Api\Controller\V1\Contract;

use App\Http\Api\Service\V1\Contract\PerpetualInfoService;
use App\Http\Common\Controller\AbstractController;
use App\Http\Api\Request\V1\Contract\PerpetualInfoRequest;
use App\Http\Api\Service\V1\Contract\PerpetualConfigService;
use App\Http\Api\Middleware\TokenMiddleware;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;

#[Controller(prefix: 'api/v1/contract/perpetual/info')]
class PerpetualInfoController extends AbstractController
{
    /**
     * @var PerpetualInfoService $service;
     */
    public mixed $service;

    /**
     * @var PerpetualInfoRequest $rules
     */
    public mixed $rules;

    /**
     * 获取合约交易规则
     */
    #[GetMapping('exchange-info')]
    public function getExchangeInfo(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->exchangeInfoRules());

        try {
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;

            $result = $this->service->getExchangeInfo($currencyId);

            return $this->success($result, '获取交易规则成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取杠杆档位配置
     */
    #[GetMapping('leverage-bracket')]
    public function getLeverageBracket(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->leverageBracketRules());

        try {
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;

            $result = $this->service->getLeverageBracket($currencyId);

            return $this->success($result, '获取杠杆档位配置成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取资金费率
     */
    #[GetMapping('funding-rate')]
    public function getFundingRate(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->fundingRateRules());

        try {
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;

            $result = $this->service->getFundingRate($currencyId);
            return $this->success($result, '获取资金费率成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取资金费率历史
     */
    #[GetMapping('funding-rate/history')]
    public function getFundingRateHistory(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->fundingRateHistoryRules());

        try {
            $currencyId = (int)$this->request->input('currency_id');
            $limit = (int)($this->request->input('limit', 100));
            $startTime = $this->request->input('start_time') ? (int)$this->request->input('start_time') : null;
            $endTime = $this->request->input('end_time') ? (int)$this->request->input('end_time') : null;

            $result = $this->service->getFundingRateHistory($currencyId, $limit, $startTime, $endTime);

            return $this->success($result, '获取资金费率历史成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取仓位风险档位
     */
    #[GetMapping('position-risk')]
    public function getPositionRisk(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->positionRiskRules());

        try {
            $currencyId = (int)$this->request->input('currency_id');

            $result = $this->service->getPositionRisk($currencyId);

            return $this->success($result, '获取仓位风险档位成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}