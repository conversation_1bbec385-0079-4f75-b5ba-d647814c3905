<?php

declare(strict_types=1);
/**
 * 永续合约仓位请求验证
 */

namespace App\Http\Api\Request\V1\Contract;

use App\Http\Api\Request\BaseFormRequest;
use App\Model\Enums\Trade\Perpetual\MarginMode;
use App\Model\Enums\Trade\Perpetual\ContractSide;
use Hyperf\Validation\Rule;

class PerpetualPositionRequest extends BaseFormRequest
{
    /**
     * 查询仓位列表验证规则
     */
    public function positionListRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'margin_mode' => ['nullable', Rule::in([1, 2])], // 1-全仓 2-逐仓
            'side' => ['nullable', Rule::in([1, 2])], // 1-多头 2-空头
            'status' => ['nullable', Rule::in([1,2,3])],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'page' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 查询仓位详情验证规则
     */
    public function positionDetailRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'margin_mode' => ['required', Rule::in([1, 2])],
        ];
    }

    /**
     * 调整保证金验证规则
     */
    public function adjustMarginRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'margin_mode' => ['required', Rule::in([1, 2])],
            'amount' => ['required', 'numeric', 'min:0.00000001'],
            'type' => ['required', 'integer', 'in:1,2'], // 1-增加 2-减少
        ];
    }

    /**
     * 调整杠杆验证规则
     */
    public function adjustLeverageRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'margin_mode' => ['required', Rule::in([1, 2])],
            'leverage' => ['required', 'numeric', 'min:1', 'max:100'],
        ];
    }

    /**
     * 切换保证金模式验证规则
     */
    public function switchMarginModeRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'margin_mode' => ['required', Rule::in([1, 2])],
        ];
    }

    /**
     * 查询持仓风险验证规则
     */
    public function positionRiskRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'margin_mode' => ['nullable', Rule::in([1, 2])],
        ];
    }

    /**
     * 查询强平历史验证规则
     */
    public function liquidationHistoryRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'page' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 查询ADL历史验证规则
     */
    public function adlHistoryRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'page' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 查询用户配置验证规则
     */
    public function configGetRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
        ];
    }

    /**
     * 更新用户配置验证规则
     */
    public function configUpdateRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'hold_units' => ['nullable', 'integer', Rule::in([1, 2, 3])], // 1-数量 2-成本价值 3-名义价值
            'hold_mode' => ['nullable', 'integer', Rule::in([1, 2])], // 1-单向持仓 2-双向持仓
            'pnl_source' => ['nullable', 'integer', Rule::in([1, 2])], // 1-标记价格 2-最新价格
            'tp_sl_source' => ['nullable', 'integer', Rule::in([1, 2])], // 1-最新价格 2-标记价格
            'assets_mode' => ['nullable', 'integer', Rule::in([1, 2])], // 1-单币种保证金 2-联合保证金
            'price_protect' => ['nullable', 'integer', Rule::in([0, 1])], // 0-关闭 1-开启
            'lever' => ['nullable', 'integer', 'min:1'], // 杠杆倍数
            'iso_long_lever' => ['nullable','integer','min:1'],
            'iso_short_lever' => ['nullable','integer','min:1'],
            'margin_type' => ['nullable', 'integer', Rule::in([1, 2])], // 1-全仓保证金 2-逐仓保证金
        ];
    }

    /**
     * 重置用户配置验证规则
     */
    public function configResetRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
        ];
    }
}