<?php

declare(strict_types=1);
/**
 * 永续合约交易请求验证
 */

namespace App\Http\Api\Request\V1\Contract;

use App\Http\Api\Request\BaseFormRequest;
use App\Model\Enums\Trade\Perpetual\MarginMode;
use App\Model\Enums\Trade\Perpetual\ContractSide;
use App\Model\Enums\Trade\Perpetual\ContractOrderType;
use App\Enum\OrderStatus;
use Hyperf\Validation\Rule;

class PerpetualTradeRequest extends BaseFormRequest
{
    /**
     * 永续合约下单验证规则
     */
    public function orderRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'side' => ['required', Rule::in([1, 2, 3, 4])], // 1-买入开多 2-卖出开空 3-买入平空 4-卖出平多
            'margin_mode' => ['required', Rule::in([1, 2])], // 1-全仓 2-逐仓
            'leverage' => ['required', 'numeric', 'min:1', 'max:200'],
            'quantity' => ['required', 'numeric', 'min:0.00000001'],
            'price' => ['nullable', 'numeric', 'min:0.00000001'],
            'order_type' => ['required', 'integer'],
            'reduce_only' => ['nullable', 'boolean'],
            'time_in_force' => ['nullable', 'integer'],
            'stop_price' => ['nullable', 'numeric', 'min:0.00000001'],
            'trigger_type' => ['nullable', 'integer', 'in:1,2'], // 1-标记价格 2-最新价格
            'is_trader' => ['nullable','integer','in:0,1'] //是否交易员订单
        ];
    }

    /**
     * 撤单验证规则
     */
    public function cancelRules(): array
    {
        return [
            'order_id' => ['required', 'integer', 'min:1'],
        ];
    }

    /**
     * 查询订单列表验证规则
     */
    public function orderListRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'margin_mode' => ['nullable', Rule::in([1, 2])],
            'side' => ['nullable', Rule::in([1, 2, 3, 4])],
            'status' => ['nullable', 'integer', 'min:1'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'page' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 查询订单详情验证规则
     */
    public function orderDetailRules(): array
    {
        return [
            'order_id' => ['required', 'integer', 'min:1'],
        ];
    }

    /**
     * 查询交易历史验证规则
     */
    public function tradeHistoryRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'margin_mode' => ['nullable', Rule::in([1, 2])],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'page' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 批量撤单验证规则
     */
    public function cancelAllRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 全部平仓验证规则
     */
    public function closeAllPositionsRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 反手开仓验证规则
     */
    public function reversePositionRules(): array
    {
        return [
            'position_id' => ['required', 'integer', 'min:1'],
            'quantity' => ['nullable', 'numeric', 'min:0.00000001'],
            'reduce_only' => ['nullable', 'boolean'],
        ];
    }
}