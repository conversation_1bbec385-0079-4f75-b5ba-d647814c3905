<?php

declare(strict_types=1);

namespace App\Http\Api\Request\V1\Asset;

use App\Http\Api\Request\BaseFormRequest;

class DepositRequest extends BaseFormRequest
{
    /**
     * 获取支持充值币种验证规则
     */
    public function getSupportedCurrenciesRules(): array
    {
        return [];
    }

    /**
     * 获取充值地址验证规则
     */
    public function getDepositAddressRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'chain_id' => ['required', 'string', 'max:50'],
        ];
    }
}
