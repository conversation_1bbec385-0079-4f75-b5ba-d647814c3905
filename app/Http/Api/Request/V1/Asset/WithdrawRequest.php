<?php

declare(strict_types=1);

namespace App\Http\Api\Request\V1\Asset;

use App\Http\Api\Request\BaseFormRequest;
use App\Model\Enums\Asset\WithdrawType;

class WithdrawRequest extends BaseFormRequest
{
    /**
     * 获取可提币种验证规则
     */
    public function getAvailableCurrenciesRules(): array
    {
        return [];
    }

    /**
     * 获取提币配置验证规则
     */
    public function getWithdrawConfigRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
        ];
    }

    /**
     * 提交提币申请验证规则
     */
    public function submitWithdrawRules(): array
    {
        $withdrawType = (int)$this->input('withdraw_type', 0);
        
        $baseRules = [
            'currency_id' => ['required', 'integer', 'min:1'],
            'withdraw_type' => ['required', 'integer', 'in:1,2,3,4'],
            'amount' => ['required', 'numeric', 'min:0.00000001'],
        ];

        // 根据提币类型添加特定验证规则
        switch ($withdrawType) {
            case WithdrawType::CHAIN->value:
                return array_merge($baseRules, [
                    'chain_id' => ['required', 'string', 'max:50'],
                    'to_address' => ['required', 'string', 'max:255'],
                    'memo' => ['nullable', 'string', 'max:100'],
                ]);

            case WithdrawType::INTERNAL_EMAIL->value:
                return array_merge($baseRules, [
                    'to_email' => ['required', 'email', 'max:255'],
                ]);

            case WithdrawType::INTERNAL_PHONE->value:
                return array_merge($baseRules, [
                    'to_phone' => ['required', 'string', 'max:20'],
                    'to_phone_country_code' => ['required', 'string', 'max:10'],
                ]);

            case WithdrawType::INTERNAL_UID->value:
                return array_merge($baseRules, [
                    'to_uid' => ['required', 'string', 'max:50'],
                ]);

            default:
                return $baseRules;
        }
    }

    /**
     * 查询提币记录验证规则
     */
    public function getWithdrawRecordsRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'withdraw_type' => ['nullable', 'integer', 'in:1,2,3,4'],
            'status' => ['nullable', 'integer', 'in:1,2,3,4,5'],
            'start_time' => ['nullable', 'integer', 'min:1'],
            'end_time' => ['nullable', 'integer', 'min:1'],
            'page' => ['nullable', 'integer', 'min:1'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:100'],
        ];
    }

    /**
     * 添加提币地址验证规则
     */
    public function addWithdrawAddressRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'chain_id' => ['required', 'string', 'max:50'],
            'address' => ['required', 'string', 'max:255'],
            'remark' => ['nullable', 'string', 'max:100'],
        ];
    }

    /**
     * 获取提币地址列表验证规则
     */
    public function getWithdrawAddressesRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 删除提币地址验证规则
     */
    public function deleteWithdrawAddressRules(): array
    {
        return [
            'id' => ['required', 'integer', 'min:1'],
        ];
    }
}
