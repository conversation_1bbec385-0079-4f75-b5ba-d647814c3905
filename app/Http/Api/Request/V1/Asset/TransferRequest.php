<?php

declare(strict_types=1);

namespace App\Http\Api\Request\V1\Asset;

use App\Http\Api\Request\BaseFormRequest;
use App\Model\Enums\User\AccountType;

class TransferRequest extends BaseFormRequest
{
    /**
     * 获取账户币种列表验证规则
     */
    public function getAccountCurrenciesRules(): array
    {
        // 获取所有AccountType枚举值
        $accountTypes = array_map(fn($case) => $case->value, AccountType::cases());

        return [
            'account_type' => [
                'required',
                'integer',
                'in:' . implode(',', $accountTypes)
            ],
        ];
    }

    /**
     * 资金划转验证规则
     */
    public function transferRules(): array
    {
        // 获取所有AccountType枚举值
        $accountTypes = array_map(fn($case) => $case->value, AccountType::cases());

        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'from_account_type' => [
                'required',
                'integer',
                'in:' . implode(',', $accountTypes)
            ],
            'to_account_type' => [
                'required',
                'integer',
                'in:' . implode(',', $accountTypes),
                'different:from_account_type'
            ],
            'amount' => ['required', 'numeric', 'min:0.********'],
            'remark' => ['nullable', 'string', 'max:255'],
            'isolated_asset_type' => ['nullable', 'string', 'in:base,quote'],
            'isolated_target_currency_id' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 获取划转记录验证规则
     */
    public function getTransferRecordsRules(): array
    {
        // 获取所有AccountType枚举值
        $accountTypes = array_map(fn($case) => $case->value, AccountType::cases());

        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'from_account_type' => [
                'nullable',
                'integer',
                'in:' . implode(',', $accountTypes)
            ],
            'to_account_type' => [
                'nullable',
                'integer',
                'in:' . implode(',', $accountTypes)
            ],
            'status' => ['nullable', 'integer', 'in:1,2,3'],
            'start_time' => ['nullable', 'integer', 'min:1'],
            'end_time' => ['nullable', 'integer', 'min:1'],
            'page' => ['nullable', 'integer', 'min:1'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:100'],
        ];
    }

    /**
     * 获取账户类型列表验证规则
     */
    public function getAccountTypesRules(): array
    {
        return [
            'lang' => ['nullable', 'string', 'in:zh-CN,en-US'],
        ];
    }
}
