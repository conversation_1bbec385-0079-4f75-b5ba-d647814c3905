<?php

declare(strict_types=1);

namespace App\Http\Api\Request\V1\Asset;

use App\Http\Api\Request\BaseFormRequest;

class AssetRequest extends BaseFormRequest
{
    /**
     * 获取用户全部资产验证规则
     */
    public function getBalanceRules(): array
    {
        return [];
    }

    /**
     * 获取单币种资产详情验证规则
     */
    public function getCurrencyDetailRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
        ];
    }

    /**
     * 获取资产流水记录验证规则
     */
    public function getFlowsRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'account_type' => ['nullable', 'integer', 'in:0,1,2,3,7'],
            'flow_type' => ['nullable', 'integer', 'min:1'],
            'start_time' => ['nullable', 'integer', 'min:1'],
            'end_time' => ['nullable', 'integer', 'min:1'],
            'page' => ['nullable', 'integer', 'min:1'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:100'],
        ];
    }

    /**
     * 获取流水类型解释验证规则
     */
    public function getFlowTypesRules(): array
    {
        return [
            'lang' => ['nullable', 'string', 'in:zh-CN,en-US'],
        ];
    }

    /**
     * 获取资产统计概览验证规则
     */
    public function getSummaryRules(): array
    {
        return [];
    }
}
