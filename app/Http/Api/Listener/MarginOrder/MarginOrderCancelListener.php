<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆订单撤单事件监听器
 */

namespace App\Http\Api\Listener\MarginOrder;

use App\Http\Api\Event\Margin\OrderCancelEvent;
use App\Model\Trade\TradeMarginOrder;
use App\Model\Match\MatchOrder;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\Enums\Trade\Margin\MarginType;
use App\Service\RedisFactory\CacheRedis;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\Context\ApplicationContext;
use Hyperf\DbConnection\Db;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

#[Listener]
class MarginOrderCancelListener implements ListenerInterface
{
    protected CacheRedis $redis;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory,ContainerInterface $container)
    {
        $this->redis = $container->get(CacheRedis::class);
        $this->logger = $loggerFactory->get('margin-trade');
    }

    public function listen(): array
    {
        return [
            OrderCancelEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof OrderCancelEvent) {
            return;
        }

        $orderId = $event->order_id;

        $this->logger->info('收到杠杆订单撤单事件', ['order_id' => $orderId]);

        try {
            $lockKey = "margin_cancel_lock:{$orderId}";
            $this->executeWithLock($lockKey, function () use ($orderId) {
                Db::transaction(function () use ($orderId) {
                    $this->processMarginOrderCancel($orderId);
                });
            });

            $this->logger->info('杠杆订单撤单处理完成', ['order_id' => $orderId]);

        } catch (\Exception $e) {
            $this->logger->error('杠杆订单撤单处理失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 处理杠杆订单撤单
     */
    protected function processMarginOrderCancel(int $orderId): void
    {
        // 查找撮合引擎订单
        $matchOrder = MatchOrder::query()
            ->where('order_id', $orderId)
            ->first();

        if (!$matchOrder) {
            $this->logger->info('撮合引擎订单不存在，可能是机器人订单', [
                'order_id' => $orderId
            ]);
            return;
        }

        // 查找杠杆订单
        $marginOrder = TradeMarginOrder::query()
            ->where('match_order_id', $matchOrder->id)
            ->first();

        if (!$marginOrder) {
            $this->logger->info('杠杆订单不存在，可能是现货订单', [
                'match_order_id' => $matchOrder->id,
                'order_id' => $orderId
            ]);
            return;
        }

        // 计算需要解冻的资金
        $unfreezeData = $this->calculateUnfreezeAmount($marginOrder, $matchOrder);

        if ($unfreezeData['unfreeze_amount'] > 0) {
            // 解冻资金
            $result = ApplicationContext::getContainer()->get(UserAccountsAssetService::class)->unfreezeAsset(
                $unfreezeData['user_id'],
                $unfreezeData['account_type'],
                $unfreezeData['currency_id'],
                $unfreezeData['unfreeze_amount'],
                FlowsType::MARGIN_TRADE->value,
                0,
                $unfreezeData['frozen_field'],
                $unfreezeData['target_field']
            );

            if (!$result) {
                throw new \RuntimeException('解冻资金失败');
            }

            $this->logger->info('杠杆订单撤单资金解冻成功', [
                'margin_order_id' => $marginOrder->getId(),
                'user_id' => $unfreezeData['user_id'],
                'currency_id' => $unfreezeData['currency_id'],
                'unfreeze_amount' => $unfreezeData['unfreeze_amount'],
                'account_type' => $unfreezeData['account_type']
            ]);
        } else {
            $this->logger->info('杠杆订单无需解冻资金', [
                'margin_order_id' => $marginOrder->getId(),
                'match_order_id' => $matchOrder->id
            ]);
        }
    }

    /**
     * 计算需要解冻的资金金额
     */
    protected function calculateUnfreezeAmount(TradeMarginOrder $marginOrder, MatchOrder $matchOrder): array
    {
        $accountType = $marginOrder->getMarginType() === MarginType::CROSS 
            ? AccountType::MARGIN 
            : AccountType::ISOLATED;

        // 使用新的字段计算剩余冻结资金
        $frozenAmount = $marginOrder->getFrozenAmount();
        $usedAmount = $marginOrder->getUsedAmount();
        
        // 计算剩余需要解冻的资金
        $remainingFrozenAmount = bcsub((string)$frozenAmount, (string)$usedAmount, 18);
        
        if (bccomp($remainingFrozenAmount, '0', 18) <= 0) {
            // 没有剩余冻结资金，无需解冻
            return [
                'user_id' => $marginOrder->getUserId(),
                'currency_id' => 0,
                'unfreeze_amount' => 0,
                'account_type' => $accountType->value
            ];
        }

        // 确定解冻的币种ID和字段
        if ($matchOrder->side === 1) { // 买单
            if ($marginOrder->getMarginType() === MarginType::CROSS) {
                // 全仓买单：解冻独立的计价币资产记录
                $currencyId = $this->getQuoteCurrencyId($marginOrder->getCurrencyId());
                $frozenField = 'frozen';
                $targetField = 'available';
            } else {
                // 逐仓买单：从基础币记录的locked字段解冻到margin_quote字段
                $currencyId = $marginOrder->getCurrencyId();
                $frozenField = 'locked';
                $targetField = 'margin_quote';
            }
        } else { // 卖单
            // 卖单解冻基础币（全仓和逐仓都一样）
            $currencyId = $marginOrder->getCurrencyId();
            $frozenField = 'frozen';
            $targetField = 'available';
        }

        return [
            'user_id' => $marginOrder->getUserId(),
            'currency_id' => $currencyId,
            'unfreeze_amount' => (float)$remainingFrozenAmount,
            'account_type' => $accountType->value,
            'frozen_field' => $frozenField,
            'target_field' => $targetField
        ];
    }

    /**
     * 获取计价币种ID
     */
    protected function getQuoteCurrencyId(int $currencyId): int
    {
        $currencyKey = \App\Enum\CurrencyConfigKey::getCurrencyKey($currencyId);
        $quoteAssetsId = $this->redis->hGet($currencyKey, 'quote_assets_id');
        
        if ($quoteAssetsId === false || $quoteAssetsId === null) {
            throw new \RuntimeException("无法获取币种 {$currencyId} 的计价币种ID");
        }
        
        return (int)$quoteAssetsId;
    }

    /**
     * 使用Redis分布式锁执行操作
     */
    protected function executeWithLock(string $lockKey, callable $callback): void
    {
        $lockValue = uniqid() . '-' . getmypid();
        $maxRetryTime = 5; // 最大重试5秒
        $retryInterval = 0.1; // 每次重试间隔100ms
        $startTime = microtime(true);
        
        while (true) {
            $acquired = $this->redis->set($lockKey, $lockValue, ['NX', 'EX' => 30]);
            
            if ($acquired) {
                // 成功获取锁
                break;
            }
            
            // 检查是否超过最大重试时间
            if (microtime(true) - $startTime >= $maxRetryTime) {
                throw new \RuntimeException("获取锁超时: {$lockKey}，已重试{$maxRetryTime}秒");
            }
            
            // 等待后重试
            usleep((int)($retryInterval * 1000000)); // 转换为微秒
        }

        try {
            $callback();
        } finally {
            $this->releaseLock($lockKey, $lockValue);
        }
    }

    /**
     * 释放Redis分布式锁
     */
    protected function releaseLock(string $lockKey, string $lockValue): void
    {
        $script = "
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
        ";

        $this->redis->eval($script, [$lockKey, $lockValue], 1);
    }
} 