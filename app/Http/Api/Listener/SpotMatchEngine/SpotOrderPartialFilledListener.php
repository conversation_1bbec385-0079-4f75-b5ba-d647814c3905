<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\SpotMatchEngine;

use App\Enum\CurrencyConfigKey;
use App\Http\Api\Event\Spot\OrderPartialFilledEvent;
use App\Model\Currency\Currency;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\Match\MatchOrder;
use App\Model\Trade\TradeSpotOrder;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\Context\ApplicationContext;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

#[Listener]
class SpotOrderPartialFilledListener implements ListenerInterface
{
    protected UserAccountsAssetService $assetService;

    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('spot-partial-filled', 'spot-trade-logs');
    }

    public function listen(): array
    {
        return [
            OrderPartialFilledEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof OrderPartialFilledEvent) {
            return;
        }
        $this->assetService = ApplicationContext::getContainer()->get(UserAccountsAssetService::class);

        $this->logger->info('收到订单部分成交完成事件', [
            'order_id' => $event->order_id
        ]);

        try {
            // 使用Redis分布式锁
            $lockKey = "order_lock:{$event->order_id}";
            $this->executeWithLock([$lockKey], function () use ($event) {
                Db::transaction(function () use ($event) {
                    $this->processOrderPartialFilled($event->order_id);
                });
            });
        } catch (\Exception $e) {
            $this->logger->error('处理订单部分成交完成事件失败', [
                'order_id' => $event->order_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 处理订单部分成交完成（IOC/FOK订单的部分完成和部分取消）
     */
    protected function processOrderPartialFilled(int $orderId): void
    {
        // 查找撮合引擎订单
        $matchOrder = MatchOrder::query()
            ->where('order_id', $orderId)
            ->first();

        if (!$matchOrder) {
            $this->logger->info('订单不存在于数据库，可能是做市机器人订单', [
                'order_id' => $orderId
            ]);
            return; // 机器人订单不需要处理资金变动
        }

        // 查找现货订单
        $spotOrder = TradeSpotOrder::query()
            ->where('match_order', $matchOrder->id)
            ->first();

        if (!$spotOrder) {
            $this->logger->warning('未找到对应的现货订单', [
                'match_order_id' => $matchOrder->id,
                'order_id' => $orderId
            ]);
            return; // 可能也是机器人订单或数据异常
        }

        // 检查是否为真实用户订单
        if (!$this->isRealUser($spotOrder->user_id)) {
            $this->logger->info('跳过机器人用户的资金处理', [
                'order_id' => $orderId,
                'user_id' => $spotOrder->user_id
            ]);
            return;
        }

        // 返还未使用的冻结资金（IOC/FOK订单未成交部分）
        $this->returnUnusedFrozenFunds($spotOrder);

        $this->logger->info('订单部分成交完成处理完成', [
            'user_id' => $spotOrder->user_id,
            'order_id' => $spotOrder->id,
            'match_order_id' => $matchOrder->id
        ]);
    }

    /**
     * 返还未使用的冻结资金
     */
    protected function returnUnusedFrozenFunds(TradeSpotOrder $spotOrder): void
    {
        $frozenAmount = $spotOrder->frozen_amount ?? 0;
        $usedAmount = $spotOrder->used_amount ?? 0;

        // 计算剩余未使用的冻结资金
        $remainingFrozen = bcsub((string)$frozenAmount, (string)$usedAmount);

        if (bccomp($remainingFrozen, '0') > 0) {
            // 确定需要解冻的币种
            $unfreezeCurrencyId = $this->getUnfreezeCurrencyId($spotOrder);

            // 解冻剩余资金
            $this->assetService->unfreezeAsset(
                $spotOrder->user_id,
                AccountType::SPOT->value,
                $unfreezeCurrencyId,
                (float)$remainingFrozen,
                FlowsType::SPOT_TRADE->value,
                $spotOrder->id
            );

            $this->logger->info('返还IOC/FOK订单未成交部分的冻结资金', [
                'user_id' => $spotOrder->user_id,
                'order_id' => $spotOrder->id,
                'currency_id' => $unfreezeCurrencyId,
                'frozen_amount' => $frozenAmount,
                'used_amount' => $usedAmount,
                'returned_amount' => (float)$remainingFrozen
            ]);
        } else {
            $this->logger->info('无剩余冻结资金需要返还', [
                'user_id' => $spotOrder->user_id,
                'order_id' => $spotOrder->id,
                'frozen_amount' => $frozenAmount,
                'used_amount' => $usedAmount
            ]);
        }
    }

    /**
     * 获取需要解冻的币种ID
     */
    protected function getUnfreezeCurrencyId(TradeSpotOrder $spotOrder): int
    {
        if ($spotOrder->direction == 1) { // 买单
            // 买单解冻计价币种
            return $this->getQuoteCurrencyId($spotOrder->currency_id);
        } else { // 卖单
            // 卖单解冻基础币种
            return $spotOrder->currency_id;
        }
    }

    /**
     * 获取计价币种ID
     */
    protected function getQuoteCurrencyId(int $baseCurrencyId): int
    {
        try {
            // 从Redis获取币种配置
            $currencyKey = CurrencyConfigKey::getCurrencyKey($baseCurrencyId);
            $quoteCurrencyId = $this->redis->hGet($currencyKey, Currency::FIELD_QUOTE_ASSETS_ID);

            if ($quoteCurrencyId === false || $quoteCurrencyId === null) {
                $this->logger->warning('Redis中未找到计价币种配置', [
                    'base_currency_id' => $baseCurrencyId,
                    'redis_key' => $currencyKey,
                    'field' => Currency::FIELD_QUOTE_ASSETS_ID
                ]);
                return 1; // 默认返回USDT的ID
            }

            return (int)$quoteCurrencyId;

        } catch (\Exception $e) {
            $this->logger->warning('获取计价币种ID失败，使用默认值', [
                'base_currency_id' => $baseCurrencyId,
                'error' => $e->getMessage()
            ]);
            return 1; // 默认返回USDT的ID
        }
    }

    /**
     * 判断是否为真实用户（非机器人）
     */
    protected function isRealUser(int $userId): bool
    {
        try {
            if($userId <= 0){
                return false;
            }
            return true;

        } catch (\Exception $e) {
            $this->logger->warning('检查用户类型失败，默认为真实用户', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return true; // 异常时默认为真实用户，确保不影响正常交易
        }
    }

    /**
     * 使用Redis分布式锁执行操作
     */
    protected function executeWithLock(array $lockKeys, callable $callback): void
    {
        $lockTimeout = 10; // 锁超时时间（秒）
        $maxRetries = 30; // 最大重试次数
        $retryDelay = 100; // 重试间隔（毫秒）
        $acquiredLocks = [];

        try {
            // 按顺序获取所有锁（避免死锁）
            sort($lockKeys);

            foreach ($lockKeys as $lockKey) {
                $lockValue = uniqid();
                $acquired = false;
                $retries = 0;

                // 重试获取锁
                while (!$acquired && $retries < $maxRetries) {
                    $acquired = $this->redis->set($lockKey, $lockValue, ['NX', 'EX' => $lockTimeout]);

                    if (!$acquired) {
                        $retries++;
                        if ($retries < $maxRetries) {
                            // 等待一段时间后重试
                            usleep((int)($retryDelay * 1000)); // 转换为微秒
                            $retryDelay = min($retryDelay * 1.2, 500); // 指数退避，最大500ms
                        }
                    }
                }

                if (!$acquired) {
                    throw new \RuntimeException("获取锁超时: {$lockKey}，已重试 {$maxRetries} 次");
                }

                $acquiredLocks[$lockKey] = $lockValue;
            }

            // 执行业务逻辑
            $callback();

        } finally {
            // 释放所有获取的锁
            foreach ($acquiredLocks as $lockKey => $lockValue) {
                $this->releaseLock($lockKey, $lockValue);
            }
        }
    }

    /**
     * 释放Redis锁
     */
    protected function releaseLock(string $lockKey, string $lockValue): void
    {
        $script = "
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
        ";

        $this->redis->eval($script, [$lockKey, $lockValue], 1);
    }
}
