<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\Perpetual;

use App\Enum\AsyncExecutorKey;
use App\Enum\MarketType;
use App\Enum\OrderStatus;
use App\Http\Api\Event\Perpetual\OrderFilledEvent;
use App\Http\Api\Event\Perpetual\TraderOrderClosedEvent;
use App\Http\Api\Event\Perpetual\TraderOrderOpendEvent;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualPositionTrait;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualMarginTrait;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualLockTrait;
use App\Job\AsyncFunExecutorJob;
use App\Model\Enums\Trade\Perpetual\ContractSide;
use App\Model\Enums\Trade\Perpetual\PositionStatus;
use App\Model\Trade\TradePerpetualLiquidation;
use App\Model\Trade\TradePerpetualOrder;
use App\Http\Api\Service\V1\Contract\PerpetualLiquidationService;
use App\Model\Match\MatchOrder;
use App\Model\Trade\TradePerpetualPosition;
use App\Model\WebsocketData\OrderData\Margin\PerpetualOrderFilledMessage;
use App\Job\Socket\MessageSendJob;
use App\Model\Copy\CopyContractPosition;
use App\Model\Copy\Enums\CopyStatus;
use App\Model\Enums\Copy\CopyStatus as CopyCopyStatus;
use App\Model\Enums\Trade\Perpetual\ContractOrderType;
use Hyperf\Context\ApplicationContext;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerInterface;

#[Listener(priority: 50)]
class PerpetualOrderFilledListener implements ListenerInterface
{
    use PerpetualPositionTrait, PerpetualMarginTrait, PerpetualLockTrait;

    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    protected object $event;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('perpetual-trade', 'perpetual-logs');
    }

    /**
     * 获取订单对应的账户类型
     */
    protected function getAccountType(\App\Model\Trade\TradePerpetualOrder $order): int
    {
        return ((int)$order->is_copy === 1) || ((int)$order->is_trader === 1) ? \App\Model\Enums\User\AccountType::COPY->value : \App\Model\Enums\User\AccountType::FUTURES->value;
    }

    public function listen(): array
    {
        return [
            OrderFilledEvent::class,
        ];
    }

    public function process(object $event): void
    {
        $this->event = $event;
        if (!$event instanceof OrderFilledEvent) {
            return;
        }

        $orderId = $event->order_id;

        $this->logger->info('收到永续合约订单完全成交事件', ['order_id' => $orderId]);

        try {
            $lockKey = $this->getOrderLockKey($orderId);
            $this->executeWithLock($lockKey, function () use ($orderId) {
                Db::transaction(function () use ($orderId) {
                    $this->processPerpetualOrderFilled($orderId);
                });
            });

            $this->logger->info('永续合约订单完全成交处理完成', ['order_id' => $orderId]);

        } catch (\Exception $e) {
            $this->logger->error('永续合约订单完全成交处理失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 处理永续合约订单完全成交
     */
    protected function processPerpetualOrderFilled(int $orderId): void
    {
        // 一次性查询所有相关数据，避免重复查询
        $orderData = $this->loadOrderCompleteData($orderId);

        if (!$orderData) {
            $this->logger->info('订单数据不存在或不完整', ['order_id' => $orderId]);
            return;
        }

        $matchOrder = $orderData['match_order'];
        $perpetualOrder = $orderData['perpetual_order'];

        if((float)$perpetualOrder->used_amount <= 0 and $matchOrder->status < OrderStatus::PENDING->value){
            $this->scheduleRetryCleanup($perpetualOrder);
            return;
        }

        // 处理完全成交后的清理工作
        $this->processOrderFilledCleanup($perpetualOrder);

        // 处理强平订单完成逻辑（传递预加载的数据）
        if ($orderData['liquidation_record']) {
            $this->handleLiquidationOrderCompletedWithData($orderData);
        }

        //平仓订单才处理缓存清除
        if(
            in_array((int)$perpetualOrder->side,[ContractSide::BUY_CLOSE->value, ContractSide::SELL_CLOSE->value]) &&
            (float)$orderData['position']->available_quantity <= 0
        ){
            //清理仓位风险缓存
            $this->clearPositionCache($orderData['position']);
        }

        //开单的成交检测订单是否需要更新合约跟单记录表
        if(
            in_array((int)$perpetualOrder->side,[ContractSide::BUY_OPEN->value, ContractSide::SELL_OPEN->value])
        ){
            $this->updateCopyContractPosition($perpetualOrder);
        }

        try {
            //交易员订单事件触发，后续交易员订单仅可使用gtc下单保证订单事件触发完整性
            $perpetualOrder->order_type = (int)$matchOrder->order_type === 1 ? ContractOrderType::MARKET->value : ContractOrderType::LIMIT->value;
            $perpetualOrder->quantity = (float)$matchOrder->fill_quantity;
            $this->TraderOrderEvent($perpetualOrder);
        }catch (\Throwable $t){
            $this->logger->info("交易员订单事件触发异常：".$t->getMessage());
        }

        // 推送订单完全成交消息
        $this->pushFilledMessage($perpetualOrder, $matchOrder);
    }

    public function updateCopyContractPosition(TradePerpetualOrder $order): void
    {
        if((int)$order->is_copy === 1 and (int)$order->is_trader !== 1){
            CopyContractPosition::where(['follower_user_id' => $order->user_id, 'open_order_id'=> $order->id])->update([
                'status' => CopyCopyStatus::SUCCESS->value,
                'follower_position_id' => $order->position_id
            ]);
        }
    }

    /**
     * 交易员订单相关事件触发
     * @param TradePerpetualOrder $order
     * @return void
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function TraderOrderEvent(TradePerpetualOrder $order): void
    {
        go(function()use($order){
            if((int)$order->is_trader !== 1){
                return;
            }
            $event = match (intval($order->side)){
                ContractSide::BUY_OPEN->value, ContractSide::SELL_OPEN->value => new TraderOrderOpendEvent($order),
                ContractSide::BUY_CLOSE->value,ContractSide::SELL_CLOSE->value => new TraderOrderClosedEvent($order)
            };
            ApplicationContext::getContainer()->get(EventDispatcherInterface::class)->dispatch($event);
        });
    }



    /**
     * 处理订单完全成交后的清理工作
     */
    protected function processOrderFilledCleanup(TradePerpetualOrder $order): void
    {
        // 1. 验证撮合引擎订单的成交处理状态
        if (!$this->validateMatchOrderTradeStatus($order)) {
            $this->logger->warning('撮合引擎订单成交数据未完全处理，延迟处理', [
                'order_id' => $order->id,
                'match_order_id' => $order->match_order_id,
                'frozen_amount' => $order->frozen_amount,
                'used_amount' => $order->used_amount
            ]);

            // 延迟重试处理
            $this->scheduleRetryCleanup($order);
            return;
        }

        // 2. 返还多余的冻结保证金（主要针对市价单的2%缓冲）
        $this->returnExcessFrozenMargin($order);

        // 3. 解冻平仓订单的冻结持仓
        if ($this->isCloseOperation($order->side)) {
            $this->unfreezeCloseOrderPosition($order);
        }

        // 检查是否强平订单并处理强平完成逻辑
        $this->handleLiquidationOrderCompleted($order);

        $this->logger->info('订单完全成交清理完成', [
            'order_id' => $order->id,
            'user_id' => $order->user_id,
            'side' => $order->side,
            'frozen_amount' => $order->frozen_amount,
            'used_amount' => $order->used_amount
        ]);
    }

    /**
     * 验证撮合引擎订单的成交处理状态
     */
    protected function validateMatchOrderTradeStatus(TradePerpetualOrder $order): bool
    {
        // 获取撮合引擎订单
        $matchOrder = MatchOrder::query()
            ->where('id', $order->match_order_id)
            ->first();

        if (!$matchOrder) {
            $this->logger->error('撮合引擎订单不存在', [
                'order_id' => $order->id,
                'match_order_id' => $order->match_order_id
            ]);
            return false;
        }

        // 检查has_trade字段，确保成交数据已经处理
        if (!$matchOrder->has_trade) {
            $this->logger->info('撮合引擎订单成交数据尚未处理完成', [
                'order_id' => $order->id,
                'match_order_id' => $order->match_order_id,
                'has_trade' => $matchOrder->has_trade
            ]);
            return false;
        }

        return true;
    }

    /**
     * 延迟重试清理处理
     */
    protected function scheduleRetryCleanup(TradePerpetualOrder $order): void
    {
        // 方案1：通过MatchEngineOrderService重新触发事件
        pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value, new AsyncFunExecutorJob(
            '\App\Service\MatchEngineOrderService',
            'dispatcher',
            ["OrderFilledEvent", MarketType::MARGIN->value, [$this->event->order_id]]
        ));

        // 方案2：直接延迟重新处理（备选方案）
        // pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value, new AsyncFunExecutorJob(
        //     '\App\Http\Api\Listener\Perpetual\PerpetualOrderFilledListener',
        //     'processOrderFilledCleanup',
        //     [$order]
        // ));

        // 记录延迟重试日志
        $this->logger->info('订单完全成交处理延迟重试', [
            'order_id' => $order->id,
            'match_order_id' => $order->match_order_id,
            'retry_method' => 'async_event_dispatch'
        ]);
    }

    /**
     * 返还多余的冻结保证金
     */
    protected function returnExcessFrozenMargin(TradePerpetualOrder $order): void
    {
        // 获取订单对应的账户类型
        $accountType = $this->getAccountType($order);
        
        // 计算多余保证金
        $excessMargin = bcsub((string)$order->frozen_amount, (string)$order->used_amount, 8);
        $excessMargin = bcsub($excessMargin,(string)$order->actual_fee,8);

        if (bccomp($excessMargin, '0', 8) <= 0) {
            $this->logger->info('永续合约订单无多余保证金需要返还', [
                'order_id' => $order->id,
                'frozen_amount' => $order->frozen_amount,
                'used_amount' => $order->used_amount
            ]);
            return;
        }

        // 返还多余保证金
        $result = $this->returnExcessMargin(
            $order->user_id,
            $order->currency_id,
            (float)$excessMargin,
            $accountType
        );

        if (!$result) {
            throw new \RuntimeException('返还多余保证金失败');
        }

        $this->logger->info('永续合约订单完全成交，返还多余保证金成功', [
            'user_id' => $order->user_id,
            'order_id' => $order->id,
            'currency_id' => $order->currency_id,
            'frozen_amount' => $order->frozen_amount,
            'used_amount' => $order->used_amount,
            'returned_amount' => (float)$excessMargin
        ]);
    }

    /**
     * 解冻平仓订单的冻结持仓
     */
    protected function unfreezeCloseOrderPosition(TradePerpetualOrder $order): void
    {
        // 平仓订单完全成交后，需要解冻剩余的冻结持仓（如果有的话）
        // 这里主要处理部分成交后取消的情况，正常完全成交不会有剩余冻结
        
        $positionSide = $this->getPositionSideFromContractSide($order->side);
        
        // 获取撮合引擎订单信息
        $matchOrder = MatchOrder::query()
            ->where('id', $order->match_order_id)
            ->first();
            
        if (!$matchOrder) {
            return;
        }
        
        // 计算未成交数量（理论上应该为0，但为了安全起见）
        $unfilledQuantity = bcsub((string)$matchOrder->quantity, (string)$matchOrder->fill_quantity, 8);

        if (bccomp($unfilledQuantity, '0', 8) > 0) {
            $this->unfreezePositionQuantity(
                $order->user_id,
                $order->currency_id,
                $order->margin_mode,
                $positionSide,
                (float)$unfilledQuantity
            );
            
            $this->logger->info('解冻平仓订单剩余持仓', [
                'order_id' => $order->id,
                'user_id' => $order->user_id,
                'unfrozen_quantity' => (float)$unfilledQuantity
            ]);
        }
    }

    /**
     * 推送订单完全成交消息到WebSocket
     */
    protected function pushFilledMessage(TradePerpetualOrder $order, MatchOrder $matchOrder): void
    {
        try {
            // 获取仓位信息
            $position = null;
            if ($order->position_id) {
                $position = TradePerpetualPosition::find($order->position_id);
            }

            // 构造推送数据
            $messageData = [
                'order_id' => $order->id,
                'match_order_id' => $order->match_order_id,
                'position_id' => $order->position_id,
                'quantity' => $matchOrder->quantity,
                'filled_quantity' => $matchOrder->fill_quantity,
                'avg_price' => $matchOrder->avg_price,
                'filled_time' => time(),
            ];

            // 添加仓位信息（特别是平仓后仓位为0的情况）
            if ($position) {
                $messageData['position_quantity'] = $position->quantity;
                $messageData['position_entry_price'] = $position->entry_price;
                $messageData['position_margin'] = $position->margin_amount;
            } else {
                // 仓位已完全平仓
                $messageData['position_quantity'] = 0;
                $messageData['position_entry_price'] = 0;
                $messageData['position_margin'] = 0;
            }

            // 创建消息对象
            $message = new PerpetualOrderFilledMessage();

            // 推送到异步任务
            pushAsyncJob('socket-message', new MessageSendJob($order->user_id, $messageData, $message));

            $this->logger->info('永续合约订单完全成交消息推送成功', [
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'position_quantity' => $messageData['position_quantity']
            ]);

        } catch (\Exception $e) {
            $this->logger->error('永续合约订单完全成交消息推送失败', [
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 清理仓位平仓后的缓存数据
     * @param TradePerpetualPosition $position
     * @return void
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function clearPositionCache(TradePerpetualPosition $position): void
    {
        ApplicationContext::getContainer()->get(PerpetualLiquidationService::class)->clearPositionCache($position);
    }

    /**
     * 处理强平订单完成逻辑
     */
    protected function handleLiquidationOrderCompleted(TradePerpetualOrder $order): void
    {
        try {
            // 1. 检查是否是强平订单（通过关联的强平记录判断）
            // liquidation_order_id 存储的是 TradePerpetualOrder.id
            $liquidationRecord = TradePerpetualLiquidation::where('liquidation_order_id', $order->id)->first();

            if (!$liquidationRecord) {
                return;
            }

            // 2. 获取撮合引擎订单信息
            $matchOrder = MatchOrder::where('id', $order->match_order_id)->first();
            if (!$matchOrder) {
                $this->logger->error('强平订单对应的撮合引擎订单不存在', [
                    'order_id' => $order->id,
                    'match_order_id' => $order->match_order_id,
                    'liquidation_record_id' => $liquidationRecord->id
                ]);
                return;
            }

            // 3. 计算实际成交价格和数量
            $executedPrice = (float)$matchOrder->avg_price;
            $executedQuantity = (float)$matchOrder->fill_quantity;

            // 4. 调用强平服务处理完成逻辑
            $liquidationService = make(PerpetualLiquidationService::class);
            $result = $liquidationService->handleLiquidationOrderCompleted(
                $order->id, // 传递永续合约订单ID，不是match_order_id
                $executedPrice,
                $executedQuantity
            );

            if ($result) {
                $this->logger->info('强平订单完成处理成功', [
                    'perpetual_order_id' => $order->id,
                    'match_order_id' => $order->match_order_id,
                    'liquidation_record_id' => $liquidationRecord->id,
                    'executed_price' => $executedPrice,
                    'executed_quantity' => $executedQuantity
                ]);

                // 5. 更新仓位状态为已强平
                if ($order->position_id) {
                    $position = TradePerpetualPosition::find($order->position_id);
                    if ($position && $position->available_quantity <= 0) {
                        $position->status = PositionStatus::LIQUIDATED->value;
                        $position->save();

                        //  清理仓位缓存
                        $liquidationService->clearPositionCache($position);

                        $this->logger->info('仓位状态更新为已强平', [
                            'position_id' => $position->id,
                            'user_id' => $position->user_id,
                            'currency_id' => $position->currency_id
                        ]);
                    }
                }
            }

        } catch (\Exception $e) {
            $this->logger->error('处理强平订单完成失败', [
                'perpetual_order_id' => $order->id,
                'match_order_id' => $order->match_order_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 一次性加载订单完整数据，避免重复查询
     */
    protected function loadOrderCompleteData(int $orderId): ?array
    {
        try {
            // 1. 查找撮合引擎订单
            $matchOrder = MatchOrder::query()
                ->where('order_id', $orderId)
                ->first();

            if (!$matchOrder) {
                $this->logger->info('撮合引擎订单不存在，可能是机器人订单', [
                    'order_id' => $orderId
                ]);
                return null;
            }

            // 2. 查找永续合约订单
            $perpetualOrder = TradePerpetualOrder::query()
                ->where('match_order_id', $matchOrder->id)
                ->first();

            if (!$perpetualOrder) {
                $this->logger->info('永续合约订单不存在，可能是现货订单', [
                    'match_order_id' => $matchOrder->id,
                    'order_id' => $orderId
                ]);
                return null;
            }

            // 3. 查找仓位（如果有关联）
            $position = null;
            if ($perpetualOrder->position_id) {
                $position = TradePerpetualPosition::find($perpetualOrder->position_id);
            }

            // 4. 查找强平记录（如果是强平订单）
            $liquidationRecord = TradePerpetualLiquidation::where('liquidation_order_id', $perpetualOrder->id)->first();

            return [
                'match_order' => $matchOrder,
                'perpetual_order' => $perpetualOrder,
                'position' => $position,
                'liquidation_record' => $liquidationRecord,
                'execution_data' => [
                    'executed_price' => (float)$matchOrder->avg_price,
                    'executed_quantity' => (float)$matchOrder->fill_quantity,
                ]
            ];

        } catch (\Exception $e) {
            $this->logger->error('加载订单完整数据失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 处理强平订单完成逻辑（使用预加载数据）
     */
    protected function handleLiquidationOrderCompletedWithData(array $orderData): void
    {
        try {
            $perpetualOrder = $orderData['perpetual_order'];
            $matchOrder = $orderData['match_order'];
            $position = $orderData['position'];
            $liquidationRecord = $orderData['liquidation_record'];
            $executionData = $orderData['execution_data'];

            // 调用强平服务处理完成逻辑
            $liquidationService = make(PerpetualLiquidationService::class);
            $result = $liquidationService->handleLiquidationOrderCompleted(
                $perpetualOrder->id,
                $executionData['executed_price'],
                $executionData['executed_quantity']
            );

            if ($result) {
                $this->logger->info('强平订单完成处理成功', [
                    'perpetual_order_id' => $perpetualOrder->id,
                    'match_order_id' => $perpetualOrder->match_order_id,
                    'liquidation_record_id' => $liquidationRecord->id,
                    'executed_price' => $executionData['executed_price'],
                    'executed_quantity' => $executionData['executed_quantity']
                ]);

                // 更新仓位状态为已强平（使用预加载的仓位数据）
                if ($position && $position->available_quantity <= 0) {
                    $position->status = PositionStatus::LIQUIDATED->value;
                    $position->save();

                    // 清理仓位缓存
                    $liquidationService->clearPositionCache($position);

                    $this->logger->info('仓位状态更新为已强平', [
                        'position_id' => $position->id,
                        'user_id' => $perpetualOrder->user_id,
                        'currency_id' => $perpetualOrder->currency_id
                    ]);
                }
            }

        } catch (\Exception $e) {
            $this->logger->error('处理强平订单完成失败', [
                'perpetual_order_id' => $orderData['perpetual_order']->id ?? 0,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }


}
