<?php

/**
 * TraderOrderOpendListener.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/16
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Http\Api\Listener\Perpetual\Trader;

use App\Enum\MarketType;
use App\Enum\User\UserAssetsCacheKey;
use App\Http\Api\Event\Perpetual\TraderOrderOpendEvent;
use App\Http\Api\Service\V1\Contract\PerpetualTradeService;
use App\Model\Copy\CopyContractExpert;
use App\Model\Copy\CopyContractPosition;
use App\Model\Copy\CopyContractUserSetting;
use App\Model\Copy\CopyExclusiveMember;
use App\Model\Copy\Enums\CopyMode;
use App\Model\Copy\Enums\CopyStatus;
use App\Model\Copy\Enums\CopyType;
use App\Model\Copy\Enums\ExpertStatus;
use App\Model\Copy\Enums\LeverageMode;
use App\Model\Copy\Enums\MarginMode;
use App\Model\Enums\Trade\Perpetual\ContractOrderType;
use App\Model\Enums\User\AccountType;
use App\Model\Trade\TradePerpetualOrder;
use App\Service\RedisFactory\CacheRedis;
use App\Service\RedisFactory\MarketRedis;
use App\Service\UserAccounts\UserAccountsAssetService;
use App\Trait\Config\CurrencyConfigTrait;
use Hyperf\Context\ApplicationContext;
use Hyperf\Database\Model\Collection;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Psr\Log\LoggerInterface;

#[Listener]
class TraderOrderOpendListener implements ListenerInterface
{
    use CurrencyConfigTrait;

    /**
     * @var TradePerpetualOrder $event
     */
    protected object $event;

    protected LoggerInterface $logger;

    protected UserAccountsAssetService $service;

    protected CacheRedis $redis;

    protected MarketRedis $marketRedis;

    protected PerpetualTradeService $tradeService;

    protected float $currentPrice = 0;

    private array $currencyConfig = [];

    public function listen(): array
    {
        return [
            TraderOrderOpendEvent::class
        ];
    }

    public function process(object $event): void
    {
        $this->event = $event->order;
        $this->logger = logger('开单事件', 'copyTrade/copy_trade.log');
        $this->redis = ApplicationContext::getContainer()->get(CacheRedis::class);
        $this->marketRedis = ApplicationContext::getContainer()->get(MarketRedis::class);
        $this->service = ApplicationContext::getContainer()->get(UserAccountsAssetService::class);
        $this->tradeService = ApplicationContext::getContainer()->get(PerpetualTradeService::class);

        $this->loadCopyUser();
    }

    protected function loadCopyUser(): void
    {
        //copy_contract_expert 合约专家配置表用于查询是否开启尊享
        //区分尊享模式用户和普通模式用户
        //copy_contract_user_setting 用户设置
        //copy_exclusive_member 尊享模式成员表

        try {
            $trader = CopyContractExpert::query()->where('user_id', $this->event->user_id)->first();
            if (!$trader || !$trader->is_active || $trader->status != ExpertStatus::APPROVED) {
                return;
            }
            $fillterUser = [];
            if ($trader->exclusive_mode) {
                $fillterUser = CopyExclusiveMember::query()->where('expert_id', $this->event->user_id)->where('type', 1)->pluck('profit_sharing_rate','follower_user_id');
            }

            $this->currentPrice = $this->getCurrencyPrice((int)$this->event->currency_id,MarketType::MARGIN,$this->marketRedis);
            $this->currencyConfig = $this->getCurrencyConfig((int)$this->event->currency_id,$this->redis);

            CopyContractUserSetting::query()
                ->where('expert_id', $this->event->user_id)
                ->where('status', 1)
                ->when(count($fillterUser), function ($query) use ($fillterUser) {
                    $query->whereIn('follower_user_id', array_keys($fillterUser));
                })->chunk(50, function ($users)use($fillterUser,$trader) {
                    $this->userCopyOrder($users,$fillterUser,$trader);
                });
        } catch (\Throwable $t) {
            $this->logger->error("交易开单事件处理异常：" . $t->getMessage());
        }
    }

    protected function userCopyOrder(Collection $users,array $fillterUser,CopyContractExpert $trader): void
    {
        //主要是验证余额 其他的都不需要验证
        // 余额验证后构造参数调用handleOpenOrder 方法就行

        /**
         * 使用pipline 一次查询出全部用户的余额信息
         */
        try {
            $quote_currency_id = $this->getCurrencyConfigByKey((int)$this->event->currency_id, 'quote_assets_id', $this->redis);
            $pipline = $this->redis->pipeline();

            // 为每个用户添加 pipeline 查询
            foreach ($users as $user) {
                $assetKey = UserAssetsCacheKey::getAssetKey((int)$user->follower_user_id, AccountType::COPY->value, (int)$quote_currency_id);
                $pipline->hGet($assetKey, 'available');
            }
            // 执行 pipeline 并获取所有结果
            $balanceResults = $pipline->exec();

            // 将余额结果添加到用户对象中
            foreach ($users as $index => $user) {
                /**
                 * @var CopyContractUserSetting $user
                 */
                // 获取对应的余额结果，如果没有数据或为 false 则默认为 0

                try {
                    $balance = $balanceResults[$index] ?? 0;
                    if ($balance === false || $balance === null) {
                        $balance = 0;
                    }

                    if ((float)$balance <= 0) {
                        $this->logger->info("跟单用户{$user->follower_user_id}账户余额不足跳过跟单处理");
                        continue;
                    }

                    if (!in_array($this->event->currency_id, $user->copy_currencies) && !$user->auto_new_pairs) {
                        $this->logger->info("跟单用户{$user->follower_user_id}未开启跟单对{$this->event->currency_id}跳过跟单处理");
                        continue;
                    }

                    $params = $this->buildOrderParams($user);

                    $result = $this->tradeService->handleOpenOrder($user->follower_user_id, $this->event->currency_id,$params,null);

                    if(is_array($result)){
                        CopyContractPosition::updateOrCreate([
                            'follower_user_id' => $user->follower_user_id,
                            'expert_id' => $this->event->user_id,
                            'expert_position_id' => $this->event->position_id
                        ],[
                            'follower_user_id' => $user->follower_user_id,
                            'expert_id' => $this->event->user_id,
                            'expert_user_id' => $this->event->user_id,
                            'expert_position_id' => $this->event->position_id,
                            'follower_position_id' => 0,
                            'profit_sharing_rate' => count($fillterUser) > 0 && isset($fillterUser[$user->follower_user_id]) ? $fillterUser[$user->follower_user_id] : $trader->profit_sharing_rate,
                            'is_exclusive' => (bool)$trader->exclusive_mode,
                            'copy_settings_snapshot' => $user->toArray(),
                            'status' => \App\Model\Enums\Copy\CopyStatus::FAILED,
                            'open_order_id' => $result['order_id']
                        ])->save();
                        $this->logger->info("跟单用户{$user->follower_user_id}开单成功", [
                            'copy_order_id' => $this->event->id,
                            'follower_order_id' => $result['order_id'] ?? null,
                            'params' => $params
                        ]);
                    }else{
                        $this->logger->info("跟单用户{$user->follower_user_id}开单失败",['result' => $result]);
                    }
                } catch (\Throwable $t) {
                    $this->logger->error("跟单用户{$user->follower_user_id}开单失败：{$t->getMessage()}", [
                        'copy_order_id' => $this->event->id,
                        'params' => $params,
                        'error' => $t->getTraceAsString()
                    ]);
                }

            }
        }catch (\Throwable $t){
            $this->logger->error("跟单用户开单失败：{$t->getMessage()}", [
                'error' => $t->getTraceAsString()
            ]);
        }
    }

    protected function buildOrderParams(CopyContractUserSetting $userSetting): array
    {
        // 1. 基础参数从专家订单复制
        $params = [
            'currency_id' => $this->event->currency_id,
            'side' => $this->event->side,
            'order_type' => $this->event->order_type,
            'is_copy' => 1,
            'copy_order_id' => $this->event->id,
            'reduce_only' => $this->event->reduce_only ?? 0,
            'time_in_force' => $this->event->time_in_force ?? 1
        ];

        // 2. 根据用户设置确定保证金模式
        if ($userSetting->margin_mode === MarginMode::FOLLOW_EXPERT) {
            // 跟随专家的保证金模式
            $params['margin_mode'] = $this->event->margin_mode;
        } else {
            // 使用用户自定义的保证金模式
            // 模型中2 全仓 3逐仓 映射实际 1-全仓 2-逐仓
            $params['margin_mode'] = match ($userSetting->margin_mode->value){
                MarginMode::CROSS->value => \App\Model\Enums\Trade\Perpetual\MarginMode::CROSS->value,
                MarginMode::ISOLATED->value => \App\Model\Enums\Trade\Perpetual\MarginMode::ISOLATED->value
            };
        }

        // 3. 根据用户设置确定杠杆倍数
        if ($userSetting->leverage_mode === LeverageMode::FOLLOW_EXPERT) {
            // 跟随专家的杠杆倍数
            $params['leverage'] = $this->event->leverage;
        } else {
            // 使用用户自定义的杠杆倍数
            $params['leverage'] = (float)$userSetting->custom_leverage;
        }

        // 4. 计算跟单数量
        $params['quantity'] = $this->calculateCopyQuantity($userSetting);

        // 5. 设置价格（限价单需要价格）
        if ((int)$this->event->order_type === ContractOrderType::LIMIT->value) { // 限价单
            $params['price'] = $this->event->price;
        }

        return $params;
    }

    /**
     * 计算跟单数量
     */
    protected function calculateCopyQuantity(CopyContractUserSetting $userSetting): float
    {
        // 获取专家订单的数量
        $expertQuantity = (float)$this->event->quantity;

        if ($userSetting->mode === CopyMode::SMART_RATE) {
            // 智能比例模式：根据投资金额比例计算
            return $this->calculateQuantityByInvestmentRatio($userSetting, $expertQuantity);
        } else {
            // 多元探索模式：根据固定额度或倍率计算
            return $this->calculateQuantityByExploreMode($userSetting, $expertQuantity);
        }
    }

    /**
     * 智能比例模式计算数量
     */
    protected function calculateQuantityByInvestmentRatio(CopyContractUserSetting $userSetting, float $expertQuantity): float
    {
        // 获取专家的投资金额（这里需要获取专家的保证金金额）
        $expertMarginAmount = (float)$this->event->margin_amount;

        // 用户设置的投资金额
        $userInvestmentAmount = (float)$userSetting->investment_amount;

        if ($expertMarginAmount <= 0) {
            return 0;
        }

        // 按投资金额比例计算
        $ratio = $userInvestmentAmount / $expertMarginAmount;
        $copyQuantity = $expertQuantity * $ratio;

        // 检查是否超过最大跟随金额限制
        $maxFollowAmount = (float)$userSetting->max_follow_amount;
        if ($maxFollowAmount > 0) {
            // 计算当前订单所需保证金
            $currentPrice = $this->event->price > 0 ? $this->event->price : $this->getCurrentMarketPrice();
            $leverage = $userSetting->leverage_mode === LeverageMode::FOLLOW_EXPERT ? $this->event->leverage : $userSetting->custom_leverage;
            $requiredMargin = ($copyQuantity * $currentPrice) / $leverage;

            if ($requiredMargin > $maxFollowAmount) {
                // 按最大跟随金额重新计算数量
                $copyQuantity = ($maxFollowAmount * $leverage) / $currentPrice;
            }
        }

        return number_format($copyQuantity,$this->currencyConfig['m_quantity_precision'],'.','');
    }

    /**
     * 多元探索模式计算数量
     */
    protected function calculateQuantityByExploreMode(CopyContractUserSetting $userSetting, float $expertQuantity): float
    {
        if ($userSetting->copy_type === CopyType::FIXED_AMOUNT) {
            // 固定额度模式
            $fixedAmount = (float)$userSetting->fixed_amount;
            $currentPrice = $this->event->price > 0 ? $this->event->price : $this->getCurrentMarketPrice();
            $leverage = $userSetting->leverage_mode === LeverageMode::FOLLOW_EXPERT ? $this->event->leverage : $userSetting->custom_leverage;

            // 固定保证金金额计算数量
            return ($fixedAmount * $leverage) / $currentPrice;
        } else {
            // 倍率模式
            $rate = (float)$userSetting->rate / 100; // 百分比转小数
            $copyQuantity = $expertQuantity * $rate;

            // 检查是否超过最大跟随金额限制
            $maxFollowAmount = (float)$userSetting->max_follow_amount;
            if ($maxFollowAmount > 0) {
                $currentPrice = $this->event->price > 0 ? $this->event->price : $this->getCurrentMarketPrice();
                $leverage = $userSetting->leverage_mode === LeverageMode::FOLLOW_EXPERT ? $this->event->leverage : $userSetting->custom_leverage;
                $requiredMargin = ($copyQuantity * $currentPrice) / $leverage;

                if ($requiredMargin > $maxFollowAmount) {
                    $copyQuantity = ($maxFollowAmount * $leverage) / $currentPrice;
                }
            }

            return $copyQuantity;
        }
    }

    /**
     * 获取当前市场价格
     */
    protected function getCurrentMarketPrice(): float
    {
        // 从 Redis 获取当前市场价格
        return $this->currentPrice;
    }


}