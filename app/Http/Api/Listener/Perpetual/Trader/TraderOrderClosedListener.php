<?php

/**
 * TraderOrderClosedListener.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/16
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Http\Api\Listener\Perpetual\Trader;

use App\Enum\Contract\PerpetualPositionCacheKey;
use App\Http\Api\Event\Perpetual\TraderOrderClosedEvent;
use App\Http\Api\Service\V1\Contract\PerpetualTradeService;
use App\Model\Copy\CopyContractPosition;
use App\Model\Enums\Copy\CopyStatus;
use App\Model\Match\MatchOrder;
use App\Model\Trade\TradePerpetualOrder;
use App\Model\Trade\TradePerpetualPosition;
use App\Service\RedisFactory\CacheRedis;
use App\Service\RedisFactory\MarketRedis;
use App\Trait\Config\CurrencyConfigTrait;
use Hyperf\Collection\Collection;
use Hyperf\Context\ApplicationContext;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Psr\Log\LoggerInterface;

#[Listener]
class TraderOrderClosedListener implements ListenerInterface
{
    use CurrencyConfigTrait;
    
    protected LoggerInterface $logger;
    protected CacheRedis $redis;
    protected MarketRedis $marketRedis;
    protected PerpetualTradeService $tradeService;
    private array $currencyConfig = [];

    public function listen(): array
    {
        return [
            TraderOrderClosedEvent::class
        ];
    }

    public function process(object $event): void
    {
        try {
            // 1. 初始化服务和日志
            $this->initializeServices();

            // 2. 获取交易员平仓订单信息
            $traderOrder = $event->order;

            // 3. 获取币种配置
            $this->currencyConfig = $this->getCurrencyConfig((int)$traderOrder->currency_id, $this->redis);

            $this->logger->info('收到交易员平仓事件', [
                'trader_order_id' => $traderOrder->id,
                'trader_user_id' => $traderOrder->user_id,
                'position_id' => $traderOrder->position_id,
                'side' => $traderOrder->side,
                'quantity' => $traderOrder->quantity
            ]);

            // 4. 获取交易员原始仓位信息
            $traderPosition = $this->getTraderPosition($traderOrder->position_id);
            if (!$traderPosition) {
                $this->logger->warning('交易员仓位不存在，跳过处理', [
                    'position_id' => $traderOrder->position_id
                ]);
                return;
            }

            // 5. 计算平仓比例
            $closeRatio = $this->calculateCloseRatio($traderOrder, $traderPosition);

            $this->logger->info('计算平仓比例', [
                'trader_order_id' => $traderOrder->id,
                'close_quantity' => $traderOrder->quantity,
                'position_total_quantity' => $traderPosition->quantity,
                'close_ratio' => number_format($closeRatio, $this->currencyConfig['m_quantity_precision'] ?? 8, '.', '')
            ]);

            // 6. 分块查询并处理跟单用户（一次50个）
            $this->processCopyUsersInChunks($traderPosition->id, $closeRatio, $traderOrder);

        } catch (\Throwable $e) {
            $this->logger->error('交易员平仓事件处理异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 初始化服务
     */
    protected function initializeServices(): void
    {
        $this->logger = logger('平仓事件', 'copyTrade/copy_trade.log');
        $this->redis = ApplicationContext::getContainer()->get(CacheRedis::class);
        $this->marketRedis = ApplicationContext::getContainer()->get(MarketRedis::class);
        $this->tradeService = ApplicationContext::getContainer()->get(PerpetualTradeService::class);
        // $this->currencyConfig = $this->getCurrencyConfig(); // This line is removed as per the edit hint
    }

    /**
     * 获取交易员仓位信息
     */
    protected function getTraderPosition(int $positionId): ?TradePerpetualPosition
    {
        return TradePerpetualPosition::find($positionId);
    }

    /**
     * 获取撮合引擎订单信息
     */
    protected function getMatchOrder(int $matchOrderId): ?MatchOrder
    {
        return MatchOrder::find($matchOrderId);
    }

    /**
     * 计算平仓比例
     */
    protected function calculateCloseRatio(TradePerpetualOrder $traderOrder, TradePerpetualPosition $traderPosition): float
    {
        // 修复：通过撮合引擎订单获取实际成交数量
        $matchOrder = $this->getMatchOrder($traderOrder->match_order_id);
        if (!$matchOrder) {
            $this->logger->warning('撮合引擎订单不存在', [
                'trader_order_id' => $traderOrder->id,
                'match_order_id' => $traderOrder->match_order_id
            ]);
            return 0;
        }

        $closeQuantity = (string)$matchOrder->fill_quantity; // 使用实际成交数量

        // 修复：重建平仓前的可平仓数量（当前剩余 + 已平仓数量）
        $currentRemaining = bcadd(
            (string)$traderPosition->available_quantity,
            (string)$traderPosition->frozen_quantity,
            8
        );
        $preCloseQuantity = bcadd($currentRemaining, $closeQuantity, 8);

        // 获取数量精度配置
        $precision = $this->currencyConfig['m_quantity_precision'] ?? 8;

        if (bccomp($preCloseQuantity, '0', $precision) <= 0) {
            return 0;
        }

        // 修复：基于平仓前的可平仓数量计算平仓比例，最大为1（100%）
        $ratio = bcdiv($closeQuantity, $preCloseQuantity, 8);
        return (float)bccomp($ratio, '1', 8) > 0 ? 1.0 : (float)$ratio;
    }

    /**
     * 分块处理跟单用户
     */
    protected function processCopyUsersInChunks(
        int $expertPositionId,
        float $closeRatio,
        TradePerpetualOrder $traderOrder
    ): void {
        $processedCount = 0;
        $successCount = 0;
        $failedCount = 0;

        // 分块查询跟单记录，每次处理50个
        CopyContractPosition::query()
            ->where('expert_position_id', $expertPositionId)
            ->where('status', CopyStatus::SUCCESS) // 只处理成功的跟单
            ->chunk(50, function (Collection $copyPositions) use ($closeRatio, $traderOrder, &$processedCount, &$successCount, &$failedCount) {
                $this->logger->info('开始处理跟单用户批次', [
                    'batch_size' => $copyPositions->count(),
                    'expert_position_id' => $traderOrder->position_id
                ]);

                $batchResult = $this->processCopyPositionsBatch($copyPositions, $closeRatio, $traderOrder);
                $processedCount += $batchResult['processed'];
                $successCount += $batchResult['success'];
                $failedCount += $batchResult['failed'];
            });

        $this->logger->info('跟单用户平仓处理完成', [
            'trader_order_id' => $traderOrder->id,
            'total_processed' => $processedCount,
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'close_ratio' => number_format($closeRatio, $this->currencyConfig['m_quantity_precision'] ?? 8, '.', '')
        ]);
    }

    /**
     * 批量处理单批次跟单用户
     */
    protected function processCopyPositionsBatch(
        Collection $copyPositions,
        float $closeRatio,
        TradePerpetualOrder $traderOrder
    ): array {
        $processed = 0;
        $success = 0;
        $failed = 0;

        // 1. 批量获取仓位数据（使用Redis缓存）
        $positionDataMap = $this->batchGetPositionData($copyPositions, $traderOrder->currency_id);

        foreach ($copyPositions as $copyPosition) {
            try {
                $processed++;

                // 2. 使用缓存数据而不是查询数据库
                $positionData = $positionDataMap[$copyPosition->follower_position_id] ?? null;

                if (!$positionData || bccomp($positionData['available_quantity'], '0', $this->currencyConfig['m_quantity_precision'] ?? 8) <= 0) {
                    $this->logger->info('跟单用户仓位无效，跳过处理', [
                        'copy_position_id' => $copyPosition->id,
                        'follower_user_id' => $copyPosition->follower_user_id,
                        'follower_position_id' => $copyPosition->follower_position_id,
                        'available_quantity' => $positionData['available_quantity'] ?? 0
                    ]);
                    continue;
                }

                // 3. 构造平仓订单参数（使用缓存数据）
                $params = $this->buildCloseOrderParamsFromCache($copyPosition, $positionData, $closeRatio, $traderOrder);

                if (bccomp((string)$params['quantity'], '0', $this->currencyConfig['m_quantity_precision'] ?? 8) <= 0) {
                    $this->logger->info('计算平仓数量为0，跳过处理', [
                        'copy_position_id' => $copyPosition->id,
                        'follower_user_id' => $copyPosition->follower_user_id,
                        'close_ratio' => number_format($closeRatio, $this->currencyConfig['m_quantity_precision'] ?? 8, '.', ''),
                        'available_quantity' => $positionData['available_quantity']
                    ]);
                    continue;
                }

                // 4. 直接调用平仓接口，不使用事务
                $result = $this->tradeService->handleCloseOrder(
                    $copyPosition->follower_user_id,
                    $traderOrder->currency_id,
                    $params
                );

                // 记录操作结果
                if (is_array($result) && isset($result['order_id'])) {
                    $success++;
                    $this->logger->info('跟单用户平仓成功', [
                        'copy_position_id' => $copyPosition->id,
                        'follower_user_id' => $copyPosition->follower_user_id,
                        'follower_order_id' => $result['order_id'],
                        'close_quantity' => number_format($params['quantity'], $this->currencyConfig['m_quantity_precision'] ?? 8, '.', ''),
                        'close_ratio' => number_format($closeRatio, $this->currencyConfig['m_quantity_precision'] ?? 8, '.', ''),
                        'from_cache' => true
                    ]);
                } else {
                    $failed++;
                    $this->logger->warning('跟单用户平仓失败', [
                        'copy_position_id' => $copyPosition->id,
                        'follower_user_id' => $copyPosition->follower_user_id,
                        'result' => $result,
                        'params' => $params
                    ]);
                }

            } catch (\Throwable $e) {
                $failed++;
                $this->logger->error('跟单用户平仓异常', [
                    'copy_position_id' => $copyPosition->id,
                    'follower_user_id' => $copyPosition->follower_user_id,
                    'expert_position_id' => $copyPosition->expert_position_id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        return [
            'processed' => $processed,
            'success' => $success,
            'failed' => $failed
        ];
    }

    /**
     * 批量获取仓位数据（使用Redis缓存）
     */
    protected function batchGetPositionData(Collection $copyPositions, int $currencyId): array
    {
        $pipeline = $this->redis->pipeline();
        
        // 为每个仓位添加 pipeline 查询
        foreach ($copyPositions as $copyPosition) {
            $positionKey = PerpetualPositionCacheKey::getPositionDetailKey(
                $copyPosition->follower_position_id
            );
            $pipeline->hMGet($positionKey, ['quantity', 'available_quantity', 'margin_mode', 'leverage']);
        }
        
        // 执行 pipeline 并获取所有结果
        $results = $pipeline->exec();
        
        // 映射结果到仓位ID
        $positionDataMap = [];
        foreach ($copyPositions as $index => $copyPosition) {
            $result = $results[$index] ?? [];
            
            if ($result && isset($result['quantity'], $result['available_quantity'])) {
                $positionDataMap[$copyPosition->follower_position_id] = [
                    'quantity' => (string)($result['quantity'] ?: '0'),
                    'available_quantity' => (string)($result['available_quantity'] ?: '0'),
                    'margin_mode' => $result['margin_mode'] ?? 1,
                    'leverage' => $result['leverage'] ?? 1
                ];
            } else {
                // 缓存中没有数据，记录日志但不阻断流程
                $this->logger->warning('仓位缓存数据不存在', [
                    'copy_position_id' => $copyPosition->id,
                    'follower_user_id' => $copyPosition->follower_user_id,
                    'follower_position_id' => $copyPosition->follower_position_id,
                    'currency_id' => $currencyId,
                    'cache_result' => $result
                ]);
            }
        }
        
        $this->logger->info('批量获取仓位数据完成', [
            'total_positions' => $copyPositions->count(),
            'cached_positions' => count($positionDataMap),
            'currency_id' => $currencyId
        ]);
        
        return $positionDataMap;
    }

    /**
     * 从缓存数据构造平仓订单参数
     */
    protected function buildCloseOrderParamsFromCache(
        CopyContractPosition $copyPosition,
        array $positionData,
        float $closeRatio,
        TradePerpetualOrder $traderOrder
    ): array {
        // 获取数量精度配置
        $precision = $this->currencyConfig['m_quantity_precision'] ?? 8;

        // 跟单用户的剩余仓位数量
        $followerTotalQuantity = $positionData['available_quantity'];

        // 判断是否为全部平仓
        if (bccomp((string)$closeRatio, '1.0', $precision) >= 0) {
            // 交易员全部平仓，跟单用户也全部平仓，直接使用剩余仓位数量
            $closeQuantity = $followerTotalQuantity;
            $this->logger->info('交易员全部平仓，跟单用户全部平仓', [
                'copy_position_id' => $copyPosition->id,
                'follower_user_id' => $copyPosition->follower_user_id,
                'follower_total_quantity' => $followerTotalQuantity,
                'close_ratio' => $closeRatio
            ]);
        } else {
            // 部分平仓，按比例计算
            $closeRatioStr = (string)$closeRatio;
            $closeQuantity = bcmul($followerTotalQuantity, $closeRatioStr, $precision);
            
            $this->logger->info('交易员部分平仓，按比例计算', [
                'copy_position_id' => $copyPosition->id,
                'follower_user_id' => $copyPosition->follower_user_id,
                'follower_total_quantity' => $followerTotalQuantity,
                'close_ratio' => $closeRatio,
                'calculated_quantity' => $closeQuantity
            ]);
        }

        // 确保不超过实际持仓（理论上不会超过，但为了安全）
        if (bccomp($closeQuantity, $followerTotalQuantity, $precision) > 0) {
            $closeQuantity = $followerTotalQuantity;
        }

        // 使用币种配置精度格式化数量
        $formattedQuantity = number_format((float)$closeQuantity, $precision, '.', '');

        return [
            'currency_id' => $traderOrder->currency_id,
            'side' => $traderOrder->side, // 平仓方向与交易员一致
            'quantity' => (float)$formattedQuantity,
            'order_type' => $traderOrder->order_type,
            'price' => $traderOrder->price ?? null,
            'is_copy' => 1,
            'copy_order_id' => $traderOrder->id,
            'position_id' => $copyPosition->follower_position_id,
            'margin_mode' => $positionData['margin_mode'],
            'leverage' => $positionData['leverage'],
            'reduce_only' => 1, // 平仓订单设置为只减仓
            'time_in_force' => $traderOrder->time_in_force ?? 1
        ];
    }
}