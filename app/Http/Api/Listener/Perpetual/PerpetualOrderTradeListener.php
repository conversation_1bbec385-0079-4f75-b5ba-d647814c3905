<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\Perpetual;

use App\Http\Api\Event\Perpetual\OrderTradeEvent;
use App\Http\Api\Event\Perpetual\PerpetualPositionOpened;
use App\Http\Api\Event\Perpetual\PerpetualPositionClosed;
use App\Http\Api\Event\Perpetual\PerpetualPositionUpdated;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualPositionTrait;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualMarginTrait;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualLockTrait;
use App\Model\Enums\User\FlowsType;
use App\Model\Trade\TradePerpetualOrder;
use App\Model\Match\MatchOrder;
use App\Model\Enums\Trade\Perpetual\ContractOrderType;
use App\Model\WebsocketData\OrderData\Margin\PerpetualOrderTradeMessage;
use App\Job\Socket\MessageSendJob;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;
use Hyperf\Context\ApplicationContext;
use Psr\EventDispatcher\EventDispatcherInterface;

#[Listener(priority: 100)]
class PerpetualOrderTradeListener implements ListenerInterface
{
    use PerpetualPositionTrait, PerpetualMarginTrait, PerpetualLockTrait;

    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('perpetual-trade', 'perpetual-logs');
    }

    /**
     * 获取订单对应的账户类型
     */
    protected function getAccountType(\App\Model\Trade\TradePerpetualOrder $order): int
    {
        return ((int)$order->is_copy === 1) || ((int)$order->is_trader === 1) ? \App\Model\Enums\User\AccountType::COPY->value : \App\Model\Enums\User\AccountType::FUTURES->value;
    }

    public function listen(): array
    {
        return [
            OrderTradeEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof OrderTradeEvent) {
            return;
        }

        $tradeData = $event->order;

        $this->logger->info('收到永续合约订单成交事件', $tradeData);

        try {
            // 获取需要锁定的订单ID
            $lockKeys = [];
            if (isset($tradeData['buy_order_id'])) {
                $lockKeys[] = $this->getOrderLockKey($tradeData['buy_order_id']);
            }
            if (isset($tradeData['sell_order_id'])) {
                $lockKeys[] = $this->getOrderLockKey($tradeData['sell_order_id']);
            }

            // 使用Redis分布式锁
            $this->executeWithMultipleLocks($lockKeys, function () use ($tradeData) {
                Db::transaction(function () use ($tradeData) {
                    // 处理买方订单成交
                    if (isset($tradeData['buy_order_id'])) {
                        $this->processPerpetualTradeForOrder($tradeData, $tradeData['buy_order_id']);
                    }

                    // 处理卖方订单成交
                    if (isset($tradeData['sell_order_id'])) {
                        $this->processPerpetualTradeForOrder($tradeData, $tradeData['sell_order_id']);
                    }
                });
            });

            $this->logger->info('永续合约订单成交处理完成', [
                'trade_id' => $tradeData['trade_id'],
                'buy_order_id' => $tradeData['buy_order_id'] ?? null,
                'sell_order_id' => $tradeData['sell_order_id'] ?? null,
                'price' => $tradeData['price'],
                'quantity' => $tradeData['quantity']
            ]);

        } catch (\Exception $e) {
            $this->logger->error('永续合约订单成交处理失败', [
                'trade_id' => $tradeData['trade_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 处理永续合约订单成交
     */
    protected function processPerpetualTradeForOrder(array $tradeData, int $orderId): void
    {
        // 查找撮合引擎订单
        $matchOrder = MatchOrder::query()
            ->where('order_id', $orderId)
            ->first();

        if (!$matchOrder) {
            $this->logger->info('撮合引擎订单不存在，可能是机器人订单', [
                'order_id' => $orderId
            ]);
            return;
        }

        // 查找永续合约订单
        $perpetualOrder = TradePerpetualOrder::query()
            ->where('match_order_id', $matchOrder->id)
            ->first();

        if (!$perpetualOrder) {
            $this->logger->info('永续合约订单不存在，可能是现货订单', [
                'match_order_id' => $matchOrder->id,
                'order_id' => $orderId
            ]);
            return;
        }

        // 提取成交数据
        $filledQuantity = (float) ($tradeData['quantity'] ?? 0);
        $filledPrice = (float) ($tradeData['price'] ?? 0);

        if ($filledQuantity <= 0 || $filledPrice <= 0) {
            $this->logger->warning('成交数据无效', [
                'order_id' => $orderId,
                'filled_quantity' => $filledQuantity,
                'filled_price' => $filledPrice
            ]);
            return;
        }

        // 处理成交
        $this->processOrderTrade($perpetualOrder, $matchOrder, $filledQuantity, $filledPrice);

        $this->logger->info('永续合约订单成交处理完成', [
            'perpetual_order_id' => $perpetualOrder->id,
            'match_order_id' => $matchOrder->id,
            'user_id' => $perpetualOrder->user_id,
            'filled_quantity' => $filledQuantity,
            'filled_price' => $filledPrice
        ]);
    }

    /**
     * 处理订单成交逻辑
     */
    protected function processOrderTrade(TradePerpetualOrder $order, MatchOrder $matchOrder, float $filledQuantity, float $filledPrice): void
    {
        // 获取订单对应的账户类型
        $accountType = $this->getAccountType($order);
        
        // 计算实际使用的保证金
        $usedMargin = $this->calculateUsedMargin($filledQuantity, $filledPrice, $order->leverage);

        // 计算并扣除手续费（从MatchOrder获取订单类型：1市价，2限价）
        $orderType = $matchOrder->order_type == 2 ? ContractOrderType::LIMIT->value : ContractOrderType::MARKET->value;
        $tradeFee = $this->calculateTradeFee($order->user_id, $filledQuantity, $filledPrice, $orderType);
        $this->deductTradeFee(
            $order->user_id,
            $order->currency_id,
            $tradeFee,
            $this->isOpenOperation($order->side) ? FlowsType::PERPETUAL_TRADE:FlowsType::PERPETUAL_TRADE_FEE,
            $accountType
        );

        // 更新订单的已使用保证金和实际手续费
        $order->used_amount = (float) bcadd((string) $order->used_amount, (string) $usedMargin, 8);
        $order->actual_fee = (float) bcadd((string) $order->actual_fee, (string) $tradeFee, 8);
        $order->save();

        // 根据订单类型处理仓位
        if ($this->isOpenOperation($order->side)) {
            $this->processOpenTrade($order, $filledQuantity, $filledPrice, $usedMargin,$tradeFee);
        } elseif ($this->isCloseOperation($order->side)) {
            $this->processCloseTrade($order, $filledQuantity, $filledPrice, $tradeFee);
        }

        // 推送成交消息到WebSocket
        $this->pushTradeMessage($order, $matchOrder, $filledQuantity, $filledPrice, $tradeFee);

        // 标记撮合引擎订单已处理成交数据
        $this->markMatchOrderTradeProcessed($matchOrder);
    }

    /**
     * 处理开仓成交
     */
    protected function processOpenTrade(TradePerpetualOrder $order, float $filledQuantity, float $filledPrice, float $usedMargin,float $tradeFee): void
    {
        $positionSide = $this->getPositionSideFromContractSide($order->side);

        // 查找或创建仓位
        $position = $this->findOrCreatePosition(
            $order->user_id,
            $order->currency_id,
            $order->margin_mode,
            $positionSide,
            (int)$order->is_copy
        );

        // 记录原始数量用于判断是否是新开仓
        $originalQuantity = $position->quantity;

        // 更新仓位
        $this->updateOpenPosition($position, $filledQuantity, $filledPrice, $usedMargin, $order->leverage,$tradeFee);

        // 关联订单与仓位（如果还未关联）
        if (!$order->position_id) {
            $order->position_id = $position->id;
            $order->save();
        }

        // 触发仓位变更事件
        $this->triggerPositionEvent($position, $originalQuantity, 'open', [
            'filled_quantity' => $filledQuantity,
            'filled_price' => $filledPrice,
            'used_margin' => $usedMargin,
            'order_id' => $order->id
        ]);

        $this->logger->info('开仓成交处理完成', [
            'user_id' => $order->user_id,
            'order_id' => $order->id,
            'position_id' => $position->id,
            'filled_quantity' => $filledQuantity,
            'filled_price' => $filledPrice,
            'used_margin' => $usedMargin,
            'actual_fee' => $order->actual_fee,
            'new_position_quantity' => $position->quantity,
            'new_entry_price' => $position->entry_price
        ]);
    }

    /**
     * 处理平仓成交
     */
    protected function processCloseTrade(TradePerpetualOrder $order, float $filledQuantity, float $filledPrice, float $tradeFee): void
    {
        $positionSide = $this->getPositionSideFromContractSide($order->side);

        // 查找仓位
        $position = $this->findOrCreatePosition(
            $order->user_id,
            $order->currency_id,
            $order->margin_mode,
            $positionSide
        );

        //        echo "查找的仓位id:".$position->id."\n";

        // 记录原始数量用于判断平仓类型
        $originalQuantity = $position->quantity;

        // 获取订单对应的账户类型
        $accountType = $this->getAccountType($order);
        
        // 更新仓位并获取释放的保证金
        $releasedMargin = $this->updateClosePosition($position, $filledQuantity, $filledPrice,$tradeFee);
        // 释放保证金和处理已实现盈亏，同时扣除手续费
        $this->releaseCloseMarginWithFee($order->user_id, $order->currency_id, (float) $releasedMargin, $position->realized_pnl, $tradeFee, $accountType);

        // 关联订单与仓位（如果还未关联）
        if (!$order->position_id) {
            $order->position_id = $position->id;
            $order->save();
        }

        // 触发仓位变更事件
        $this->triggerPositionEvent($position, $originalQuantity, 'close', [
            'filled_quantity' => $filledQuantity,
            'filled_price' => $filledPrice,
            'released_margin' => $releasedMargin,
            'realized_pnl' => $position->realized_pnl,
            'trade_fee' => $tradeFee,
            'order_id' => $order->id
        ]);

        $this->logger->info('平仓成交处理完成', [
            'user_id' => $order->user_id,
            'order_id' => $order->id,
            'position_id' => $position->id,
            'filled_quantity' => $filledQuantity,
            'filled_price' => $filledPrice,
            'released_margin' => $releasedMargin,
            'realized_pnl' => $position->realized_pnl,
            'actual_fee' => $order->actual_fee,
            'remaining_quantity' => $position->quantity
        ]);
    }

    /**
     * 推送成交消息到WebSocket
     */
    protected function pushTradeMessage(TradePerpetualOrder $order, MatchOrder $matchOrder, float $filledQuantity, float $filledPrice, float $tradeFee): void
    {
        try {
            // 获取仓位信息
            $position = null;
            if ($order->position_id) {
                $position = $this->findPositionById($order->position_id);
            }

            // 构造推送数据
            $messageData = [
                'trade_id' => uniqid('trade_'),
                'order_id' => $order->id,
                'match_order_id' => $order->match_order_id,
                'position_id' => $order->position_id,
                'filled_quantity' => $filledQuantity,
                'filled_price' => $filledPrice,
                'trade_time' => time(),
            ];

            // 添加仓位信息
            if ($position) {
                $messageData['position_quantity'] = $position->quantity;
                $messageData['position_entry_price'] = $position->entry_price;
                $messageData['position_margin'] = $position->margin_amount;
            }

            // 创建消息对象
            $message = new PerpetualOrderTradeMessage();

            // 推送到异步任务
            pushAsyncJob('socket-message', new MessageSendJob($order->user_id, $messageData, $message));

            $this->logger->info('永续合约成交消息推送成功', [
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'filled_quantity' => $filledQuantity,
                'filled_price' => $filledPrice
            ]);

        } catch (\Exception $e) {
            $this->logger->error('永续合约成交消息推送失败', [
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 标记撮合引擎订单已处理成交数据
     */
    protected function markMatchOrderTradeProcessed(MatchOrder $matchOrder): void
    {
        // 更新has_trade字段，标记成交数据已处理
        $matchOrder->has_trade = 1;
        $matchOrder->save();

        $this->logger->info('撮合引擎订单成交数据处理完成', [
            'match_order_id' => $matchOrder->id,
            'order_id' => $matchOrder->order_id,
            'has_trade' => $matchOrder->has_trade
        ]);
    }

    /**
     * 根据ID查找仓位
     */
    protected function findPositionById(int $positionId): ?\App\Model\Trade\TradePerpetualPosition
    {
        return \App\Model\Trade\TradePerpetualPosition::find($positionId);
    }

    /**
     * 触发仓位变更事件
     */
    protected function triggerPositionEvent(\App\Model\Trade\TradePerpetualPosition $position, float $originalQuantity, string $action, array $extraData = []): void
    {
        try {
            $container = ApplicationContext::getContainer();
            $eventDispatcher = $container->get(EventDispatcherInterface::class);

            if ($action === 'open') {
                if ($originalQuantity == 0) {
                    // 新开仓
                    $event = new PerpetualPositionOpened($position, $extraData);
                    $eventDispatcher->dispatch($event);
                } else {
                    // 加仓
                    $event = new PerpetualPositionUpdated($position, 'increased', [], []);
                    $eventDispatcher->dispatch($event);
                }
            } elseif ($action === 'close') {
                if ($position->available_quantity <= 0) {
                    // 完全平仓
                    $event = new PerpetualPositionClosed(
                        $position,
                        $extraData['filled_quantity'],
                        $extraData['filled_price'],
                        $extraData['realized_pnl'] ?? 0,
                        $extraData
                    );
                    $eventDispatcher->dispatch($event);
                } else {
                    // 部分平仓
                    $event = new PerpetualPositionUpdated($position, 'decreased', [], []);
                    $eventDispatcher->dispatch($event);
                }
            }

            $this->logger->info('仓位变更事件触发成功', [
                'position_id' => $position->id,
                'action' => $action,
                'original_quantity' => $originalQuantity,
                'current_quantity' => $position->quantity
            ]);

        } catch (\Exception $e) {
            $this->logger->error('触发仓位变更事件失败', [
                'position_id' => $position->id,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
        }
    }

}
