<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\Perpetual;

use App\Enum\Contract\PerpetualPositionCacheKey;
use App\Http\Api\Event\Perpetual\PerpetualPositionUpdated;
use App\Http\Api\Event\Perpetual\PerpetualPositionOpened;
use App\Http\Api\Event\Perpetual\PerpetualPositionClosed;
use App\Http\Api\Event\Perpetual\PerpetualPositionLiquidated;
use App\Http\Api\Event\Perpetual\PerpetualMarginAdjusted;
use App\Model\Trade\TradePerpetualPosition;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Redis\Redis;
use Hyperf\Di\Annotation\Inject;
use Psr\Log\LoggerInterface;

/**
 * 永续合约仓位缓存监听器
 * 监听仓位变更事件，实时同步Redis缓存
 */
#[Listener]
class PerpetualPositionCacheListener implements ListenerInterface
{
    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct()
    {
        $this->logger = logger('perpetual-cache', 'perpetual-cache.log');
    }

    public function listen(): array
    {
        return [
            PerpetualPositionUpdated::class,
            PerpetualPositionOpened::class,
            PerpetualPositionClosed::class,
            PerpetualPositionLiquidated::class,
            PerpetualMarginAdjusted::class,
        ];
    }

    public function process(object $event): void
    {
        try {
            if ($event instanceof PerpetualPositionUpdated) {
                $this->handlePositionUpdated($event);
            } elseif ($event instanceof PerpetualPositionOpened) {
                $this->handlePositionOpened($event);
            } elseif ($event instanceof PerpetualPositionClosed) {
                $this->handlePositionClosed($event);
            } elseif ($event instanceof PerpetualPositionLiquidated) {
                $this->handlePositionLiquidated($event);
            } elseif ($event instanceof PerpetualMarginAdjusted) {
                $this->handleMarginAdjusted($event);
            }

        } catch (\Exception $e) {
            $this->logger->error('处理仓位缓存事件失败', [
                'event' => get_class($event),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理仓位更新事件
     */
    protected function handlePositionUpdated(PerpetualPositionUpdated $event): void
    {
        if ($event->shouldUpdateCache()) {
            $this->syncPositionToRedis($event->position);
        }

        if ($event->shouldTriggerRiskCalculation()) {
            $this->addToRiskCalculationQueue($event->position);
        }

        $this->logger->debug('仓位更新事件处理完成', [
            'position_id' => $event->position->id,
            'action' => $event->action,
            'user_id' => $event->position->user_id
        ]);
    }

    /**
     * 处理开仓事件
     */
    protected function handlePositionOpened(PerpetualPositionOpened $event): void
    {
        $this->syncPositionToRedis($event->position);
        $this->addToRiskCalculationQueue($event->position);
        $this->updateGlobalStats('position_opened');

        $this->logger->info('开仓事件缓存同步完成', [
            'position_id' => $event->position->id,
            'user_id' => $event->position->user_id,
            'currency_id' => $event->position->currency_id,
            'quantity' => $event->position->quantity
        ]);
    }

    /**
     * 处理平仓事件
     */
    protected function handlePositionClosed(PerpetualPositionClosed $event): void
    {
        if ($event->isFullyClosed()) {
            $this->removePositionFromRedis($event->position);
            $this->updateGlobalStats('position_closed');
        } else {
            $this->syncPositionToRedis($event->position);
            $this->addToRiskCalculationQueue($event->position);
        }

        $this->logger->info('平仓事件缓存同步完成', [
            'position_id' => $event->position->id,
            'closed_quantity' => $event->closedQuantity,
            'remaining_quantity' => $event->position->quantity,
            'fully_closed' => $event->isFullyClosed()
        ]);
    }

    /**
     * 处理强平事件
     */
    protected function handlePositionLiquidated(PerpetualPositionLiquidated $event): void
    {
        if ($event->isFullyLiquidated()) {
            $this->removePositionFromRedis($event->position);
            $this->updateGlobalStats('position_liquidated');
        } else {
            $this->syncPositionToRedis($event->position);
        }

        $this->handleLiquidationSpecialProcessing($event);

        $this->logger->warning('强平事件缓存同步完成', [
            'position_id' => $event->position->id,
            'liquidated_quantity' => $event->liquidatedQuantity,
            'liquidation_price' => $event->liquidationPrice,
            'fully_liquidated' => $event->isFullyLiquidated()
        ]);
    }

    /**
     * 处理保证金调整事件
     */
    protected function handleMarginAdjusted(PerpetualMarginAdjusted $event): void
    {
        $this->syncPositionToRedis($event->position);
        $this->addToRiskCalculationQueue($event->position);

        $this->logger->info('保证金调整事件缓存同步完成', [
            'position_id' => $event->position->id,
            'adjustment_type' => $event->adjustmentType,
            'adjustment_amount' => $event->adjustmentAmount,
            'new_margin' => $event->newMargin
        ]);
    }

    /**
     * 同步仓位数据到Redis
     */
    protected function syncPositionToRedis(TradePerpetualPosition $position): void
    {
        try {
            $positionData = $this->preparePositionData($position);
            $this->syncToMultipleStructures($positionData);
            $this->updateRiskMonitoringIndexes($positionData);

        } catch (\Exception $e) {
            $this->logger->error('同步仓位数据到Redis失败', [
                'position_id' => $position->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 从Redis删除仓位数据
     */
    protected function removePositionFromRedis(TradePerpetualPosition $position): void
    {
        try {
            $positionKey = PerpetualPositionCacheKey::generatePositionKey(
                $position->currency_id, 
                $position->side, 
                $position->margin_mode
            );
            $userPositionKey = PerpetualPositionCacheKey::generateUserPositionKey(
                $position->user_id,
                $position->side,
                $position->margin_mode
            );

            // 删除各种缓存结构中的数据
            $userPositionsKey = PerpetualPositionCacheKey::getUserPositionsKey($position->user_id);
            $this->redis->hDel($userPositionsKey, $positionKey);

            $currencyPositionsKey = PerpetualPositionCacheKey::getCurrencyPositionsKey($position->currency_id);
            $this->redis->hDel($currencyPositionsKey, $userPositionKey);

            $positionDetailKey = PerpetualPositionCacheKey::getPositionDetailKey($position->id);
            $this->redis->del($positionDetailKey);

            $activePositionsKey = PerpetualPositionCacheKey::getActivePositionsKey();
            $this->redis->sRem($activePositionsKey, $position->id);

            foreach (PerpetualPositionCacheKey::getAllRiskLevels() as $level) {
                $riskLevelKey = PerpetualPositionCacheKey::getRiskLevelKey($level);
                $this->redis->zRem($riskLevelKey, $position->id);
            }

            $currencyRiskKey = PerpetualPositionCacheKey::getCurrencyRiskKey($position->currency_id);
            $this->redis->hIncrBy($currencyRiskKey, 'position_count', -1);

        } catch (\Exception $e) {
            $this->logger->error('从Redis删除仓位数据失败', [
                'position_id' => $position->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 准备仓位数据
     */
    protected function preparePositionData(TradePerpetualPosition $position): array
    {
        return [
            'position_id' => $position->id,
            'user_id' => $position->user_id,
            'currency_id' => $position->currency_id,
            'margin_mode' => $position->margin_mode,
            'side' => $position->side,
            'quantity' => (string)$position->quantity,
            'available_quantity' => (string)$position->available_quantity,
            'frozen_quantity' => (string)$position->frozen_quantity,
            'entry_price' => (string)$position->entry_price,
            'margin_amount' => (string)$position->margin_amount,
            'initial_margin' => (string)$position->initial_margin,
            'maintenance_margin' => (string)$position->maintenance_margin,
            'leverage' => (string)$position->leverage,
            'realized_pnl' => (string)$position->realized_pnl,
            'status' => $position->status,
            'auto_add_margin' => $position->auto_add_margin,
            'created_at' => $position->created_at->timestamp,
            'updated_at' => $position->updated_at->timestamp,
            'sync_time' => time(),
            'position_key' => PerpetualPositionCacheKey::generatePositionKey(
                $position->currency_id, 
                $position->side, 
                $position->margin_mode
            ),
            'user_position_key' => PerpetualPositionCacheKey::generateUserPositionKey(
                $position->user_id,
                $position->side,
                $position->margin_mode
            ),
            'notional_value' => (string)bcmul((string)$position->quantity, (string)$position->entry_price, 8),
        ];
    }

    /**
     * 同步到多个Redis数据结构
     */
    protected function syncToMultipleStructures(array $positionData): void
    {
        $userId = $positionData['user_id'];
        $currencyId = $positionData['currency_id'];
        $positionId = $positionData['position_id'];
        $positionKey = $positionData['position_key'];
        $userPositionKey = $positionData['user_position_key'];

        // 用户仓位Hash - 不设置过期时间，随业务逻辑维护
        $userPositionsKey = PerpetualPositionCacheKey::getUserPositionsKey($userId);
        $this->redis->hSet($userPositionsKey, $positionKey, json_encode($positionData));

        // 币种仓位索引Hash - 不设置过期时间，随业务逻辑维护
        $currencyPositionsKey = PerpetualPositionCacheKey::getCurrencyPositionsKey($currencyId);
        $this->redis->hSet($currencyPositionsKey, $userPositionKey, $positionId);

        // 仓位详情Hash - 不设置过期时间，随业务逻辑维护
        $positionDetailKey = PerpetualPositionCacheKey::getPositionDetailKey($positionId);
        $this->redis->hMSet($positionDetailKey, $positionData);

        // 活跃仓位集合 - 不设置过期时间，随业务逻辑维护
        $activePositionsKey = PerpetualPositionCacheKey::getActivePositionsKey();
        if ($positionData['quantity'] > 0 && $positionData['status'] == 1) {
            $this->redis->sAdd($activePositionsKey, $positionId);
        } else {
            $this->redis->sRem($activePositionsKey, $positionId);
        }
    }

    /**
     * 更新风险监控相关索引
     */
    protected function updateRiskMonitoringIndexes(array $positionData): void
    {
        $currencyId = $positionData['currency_id'];
        $positionId = $positionData['position_id'];

        // 更新币种风险统计 - 不设置过期时间
        $currencyRiskKey = PerpetualPositionCacheKey::getCurrencyRiskKey($currencyId);
        $this->redis->hSet($currencyRiskKey, 'last_updated', time());

        // 添加到待风险计算队列 - 这个可以设置短期过期时间，因为是临时队列
        if ($positionData['quantity'] > 0) {
            $pendingKey = PerpetualPositionCacheKey::getPendingRiskCalculationKey($currencyId);
            $this->redis->sAdd($pendingKey, $positionId);
            $this->redis->expire($pendingKey, 180); // 3分钟过期，避免队列堆积
        }
    }

    /**
     * 添加到风险计算队列
     */
    protected function addToRiskCalculationQueue(TradePerpetualPosition $position): void
    {
        $pendingKey = PerpetualPositionCacheKey::getPendingRiskCalculationKey($position->currency_id);
        $this->redis->sAdd($pendingKey, $position->id);

        // 风险计算队列设置短期过期时间，避免队列堆积
        $this->redis->expire($pendingKey, 180); // 3分钟过期
    }

    /**
     * 强平特殊处理
     */
    protected function handleLiquidationSpecialProcessing(PerpetualPositionLiquidated $event): void
    {
        $lockKey = PerpetualPositionCacheKey::getLiquidationLockKey($event->position->id);
        $this->redis->setex($lockKey, 300, time());

        $this->updateLiquidationStats($event);
    }

    /**
     * 更新强平统计
     */
    protected function updateLiquidationStats(PerpetualPositionLiquidated $event): void
    {
        $statsKey = PerpetualPositionCacheKey::getGlobalStatsKey();
        $this->redis->hIncrBy($statsKey, 'total_liquidations', 1);
        $this->redis->hIncrByFloat($statsKey, 'total_liquidation_amount', $event->liquidatedQuantity);
        $this->redis->hSet($statsKey, 'last_liquidation_time', time());
    }

    /**
     * 更新全局统计
     */
    protected function updateGlobalStats(string $action): void
    {
        $statsKey = PerpetualPositionCacheKey::getGlobalStatsKey();

        switch ($action) {
            case 'position_opened':
                $this->redis->hIncrBy($statsKey, 'total_positions', 1);
                break;
            case 'position_closed':
            case 'position_liquidated':
                $this->redis->hIncrBy($statsKey, 'total_positions', -1);
                break;
        }

        $this->redis->hSet($statsKey, 'last_updated', time());

        // 全局统计不设置过期时间，持久保存
    }
}
