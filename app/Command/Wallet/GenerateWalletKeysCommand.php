<?php

declare(strict_types=1);

namespace App\Command\Wallet;

use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;

#[Command]
class GenerateWalletKeysCommand extends HyperfCommand
{
    protected ?string $signature = 'wallet:generate-keys';
    
    protected string $description = '生成钱包加密密钥对';

    public function handle()
    {
        $this->line('开始生成钱包加密密钥对...', 'info');

        try {
            // 生成RSA密钥对配置
            $config = [
                "digest_alg" => "sha256",
                "private_key_bits" => 2048,
                "private_key_type" => OPENSSL_KEYTYPE_RSA,
            ];

            // 生成密钥对
            $res = openssl_pkey_new($config);
            if (!$res) {
                $this->line('密钥对生成失败: ' . openssl_error_string(), 'error');
                return;
            }
            
            // 导出私钥
            if (!openssl_pkey_export($res, $privateKey)) {
                $this->line('私钥导出失败: ' . openssl_error_string(), 'error');
                return;
            }
            
            // 导出公钥
            $publicKeyDetails = openssl_pkey_get_details($res);
            if (!$publicKeyDetails) {
                $this->line('公钥获取失败: ' . openssl_error_string(), 'error');
                return;
            }
            $publicKey = $publicKeyDetails['key'];

            // 创建config/wallet目录
            $configPath = BASE_PATH . '/config/wallet/';
            if (!is_dir($configPath)) {
                if (!mkdir($configPath, 0755, true)) {
                    $this->line('创建目录失败: ' . $configPath, 'error');
                    return;
                }
            }

            // 保存私钥
            $privateKeyPath = $configPath . 'private_key.pem';
            if (file_put_contents($privateKeyPath, $privateKey) === false) {
                $this->line('私钥保存失败', 'error');
                return;
            }

            // 保存公钥
            $publicKeyPath = $configPath . 'public_key.pem';
            if (file_put_contents($publicKeyPath, $publicKey) === false) {
                $this->line('公钥保存失败', 'error');
                return;
            }

            // 设置文件权限
            chmod($privateKeyPath, 0600); // 私钥只有所有者可读写
            chmod($publicKeyPath, 0644);  // 公钥所有者可读写，其他人只读

            $this->line('钱包密钥对生成成功！', 'info');
            $this->line('私钥保存至: config/wallet/private_key.pem', 'comment');
            $this->line('公钥保存至: config/wallet/public_key.pem', 'comment');
            $this->line('', 'info');
            $this->line('⚠️  重要提醒:', 'error');
            $this->line('1. 请妥善保管私钥文件[private_key.pem]，不要泄露给任何人', 'error');
            $this->line('2. 建议备份密钥文件[private_key.pem]到安全位置', 'error');
            $this->line('3. 私钥丢失将无法解密已加密的钱包私钥', 'error');

        } catch (\Exception $e) {
            $this->line('密钥生成过程中发生错误: ' . $e->getMessage(), 'error');
        }
    }
}
