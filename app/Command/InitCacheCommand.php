<?php

declare(strict_types=1);

namespace App\Command;

use App\Enum\Config\TradeConfigKey;
use App\Enum\Config\UserVipLevelKey;
use App\MarketData\Service\CryptoCurrencySync;
use App\Model\Trade\TradeConfig;
use App\Model\User\VipLevel;
use App\Service\CollectData\MarginConfigCollect;
use App\Service\RedisFactory\CacheRedis;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Context\ApplicationContext;
use Hyperf\Di\Annotation\Inject;
use Psr\Container\ContainerInterface;

#[Command]
class InitCacheCommand extends HyperfCommand
{
    #[Inject]
    protected CacheRedis $cacheRedis;

    public function __construct(protected ContainerInterface $container)
    {
        parent::__construct('cache:init');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('系统缓存数据初始化');
    }

    public function handle()
    {
        $this->line('开始加载系统配置缓存', 'info');

        //启用的币种数据缓存
        ApplicationContext::getContainer()->get(CryptoCurrencySync::class)->loadCurrenciesToRedis();

        $pipline = $this->cacheRedis->pipeline();

        //等级数据缓存
        $levelData = VipLevel::query()->where('status',1)->get()->toArray();

        foreach ($levelData as $level){
            $key = UserVipLevelKey::getConfigKey($level['id']);
            $pipline->hMset($key,$level);
        }

        //币种配置数据缓存

        $currencyConfig = TradeConfig::query()->with(['currency:id,status'])->whereHas('currency',function ($query){
            $query->where('status',1);
        })->get()->toArray();
        foreach ($currencyConfig as $config){
            $key = TradeConfigKey::getTradeConfigKey($config['currency_id'],$config['market_type']);
            unset($config['currency']);
            $pipline->hMset($key,$config);
        }
        $pipline->exec();

        //缓存杠杆配置数据
        ApplicationContext::getContainer()->get(MarginConfigCollect::class)->cacheMarginConfigToRedis();
    }
}
