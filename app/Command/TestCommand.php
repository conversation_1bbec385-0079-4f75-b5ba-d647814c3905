<?php

/**
 * TestCommand.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/27
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Command;

use App\Enum\AsyncExecutorKey;
use App\Enum\Config\TradeConfigKey;
use App\Enum\Config\UserVipLevelKey;
use App\Enum\MarketType;
use App\Http\Api\Event\UserCertificationEvent;
use App\Job\Contract\PerpetualLiquidationJob;
use App\MarketData\Service\CryptoCurrencySync;
use App\MarketData\Service\MatchEngine\DepthSaveService;
use App\Model\Currency\Currency;
use App\Model\Currency\CurrencyTransfer;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\Trade\TradeConfig;
use App\Model\Trade\TradePerpetualFundingConfig;
use App\Model\User\UserAccountsAsset;
use App\Model\User\VipLevel;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\AsyncQueue\Driver\DriverFactory;
use Hyperf\Command\Annotation\Command;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Context\ApplicationContext;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Psr\Container\ContainerInterface;
use Psr\EventDispatcher\EventDispatcherInterface;

#[Command]
class TestCommand extends HyperfCommand
{

    #[Inject]
    protected Redis $redis;

    public function __construct(protected ContainerInterface $container)
    {
        parent::__construct('test');
    }
    public function handle()
    {

        //初始化用户的资产数据
//        ApplicationContext::getContainer()->get(EventDispatcherInterface::class)->dispatch(
//            new UserCertificationEvent(2)
//        );

        //$accountService = ApplicationContext::getContainer()->get(UserAccountsAssetService::class);
//        $accountService->addAvailableAsset(
//            2,
//            AccountType::MARGIN->value,
//            866,
//            0.01,
//            FlowsType::RECHARGE->value
//        );

//        $accountService->addAvailableAsset(
//            2,
//            AccountType::ISOLATED->value,
//            866,
//            1000,
//            FlowsType::RECHARGE->value,
//            assetField: UserAccountsAsset::FIELD_MARGIN_QUOTE
//        );


//        $riskData = [
//            'margin_ratio' => 1,
//            'maintenance_margin_rate' => 1,
//            'cross_margin_info' => [
//                'need_cancel_orders' => true
//            ],
//            'currency_id' => 866,
//            'margin_type' => 'isolated',
//        ];
//        $liquidationJob = new PerpetualLiquidationJob(2, 56, $riskData);
//        pushAsyncJob(
//            AsyncExecutorKey::PERPETUAL_CONTRACT_QUEUE->value,
//            $liquidationJob
//        );
//        $liquidationJob = new PerpetualLiquidationJob(2, 57, $riskData);
//        pushAsyncJob(
//            AsyncExecutorKey::PERPETUAL_CONTRACT_QUEUE->value,
//            $liquidationJob
//        );

    }
}