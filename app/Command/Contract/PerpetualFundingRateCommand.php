<?php

declare(strict_types=1);

namespace App\Command\Contract;

use App\Http\Api\Service\V1\Contract\PerpetualFundingFeeCollectionService;
use App\Http\Api\Service\V1\Contract\PerpetualFundingRateCalculationService;
use App\Model\Trade\TradePerpetualFundingConfig;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

#[Command]
class PerpetualFundingRateCommand extends HyperfCommand
{
    #[Inject]
    protected ContainerInterface $container;

    #[Inject]
    protected LoggerInterface $logger;

    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected PerpetualFundingRateCalculationService $calculationService;

    #[Inject]
    protected PerpetualFundingFeeCollectionService $collectionService;

    public function __construct()
    {
        parent::__construct('perpetual:funding-rate');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('永续合约资金费率计算和收取任务');
    }

    public function handle()
    {
        try {
            // 1. 获取所有启用的币种配置
            $configs = $this->getActiveFundingConfigs();
            
            if (empty($configs)) {
                $this->line('无启用的资金费率配置', 'comment');
                return 0;
            }

            $processedCount = 0;
            $errorCount = 0;

            foreach ($configs as $config) {
                try {
                    // 2. 检查是否需要处理该币种
                    if (!$this->shouldProcessCurrency($config)) {
                        continue;
                    }

                    $this->line("处理币种ID: {$config['currency_id']}", 'info');

                    // 3. 计算资金费率
                    $rateData = $this->calculationService->calculateFundingRate($config['currency_id']);
                    if (!$rateData) {
                        $errorCount++;
                        continue;
                    }

                    // 4. 保存费率记录
                    $this->calculationService->saveFundingRateRecord($rateData);

                    // 5. 收取资金费用
                    $success = $this->collectionService->collectCurrencyFundingFees($config['currency_id'], $rateData);
                    
                    if ($success) {
                        $processedCount++;
                    } else {
                        $errorCount++;
                    }

                } catch (\Exception $e) {
                    $errorCount++;
                    $this->logger->error('处理币种资金费率异常', [
                        'currency_id' => $config['currency_id'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $this->logger->info('资金费率任务执行完成', [
                'total_configs' => count($configs),
                'processed_count' => $processedCount,
                'error_count' => $errorCount
            ]);

            return 0;

        } catch (\Exception $e) {
            $this->logger->error('资金费率任务执行失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * 获取所有启用的资金费率配置
     */
    protected function getActiveFundingConfigs(): array
    {
        return TradePerpetualFundingConfig::where('status', 1)
            ->orderBy('currency_id')
            ->get()
            ->toArray();
    }

    /**
     * 检查是否需要处理该币种
     */
    protected function shouldProcessCurrency(array $config): bool
    {
        try {
            // 使用Redis锁防止重复执行
            $lockKey = "funding_rate:lock:{$config['currency_id']}";
            $lockResult = $this->redis->set($lockKey, time(), ['NX', 'EX' => 3500]); // 1小时锁定
            
            if (!$lockResult && $this->redis->exists($lockKey)) {
                return false;
            }

            // 检查是否到了收取时间
            $shouldCalculate = $this->calculationService->shouldCalculateNewRate($config['currency_id']);
            
            if (!$shouldCalculate) {
                // 释放锁
                $this->redis->del($lockKey);
                return false;
            }

            return true;

        } catch (\Exception $e) {
            $this->logger->error('检查币种处理条件失败', [
                'currency_id' => $config['currency_id'],
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
