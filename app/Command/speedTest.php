<?php

declare(strict_types=1);

namespace App\Command;

use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Coroutine\Parallel;
use Hyperf\Di\Annotation\Inject;
use Psr\Container\ContainerInterface;

#[Command]
class speedTest extends HyperfCommand
{
    #[Inject]
    protected UserAccountsAssetService $assetService;

    public function __construct(protected ContainerInterface $container)
    {
        parent::__construct('speedtest');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('资金操作性能测试命令');
    }

    public function handle()
    {
        $this->info('开始资金操作性能测试...');

        // 测试参数
        $userId = 2;
        $accountType = AccountType::SPOT->value;
        $currencyId = 1273;
        $amount = 1.0;
        $flowType = FlowsType::RECHARGE->value;
        $testCount = 1000;

        $this->info("测试参数: 用户ID={$userId}, 账户类型={$accountType}, 币种ID={$currencyId}, 金额={$amount}, 测试次数={$testCount}");
        $this->info('');


        // 1. 测试增加资金
        $this->testAddAsset($userId, $accountType, $currencyId, $amount, $flowType, $testCount);

        // 2. 测试冻结资金
        $this->testFreezeAsset($userId, $accountType, $currencyId, $amount, $flowType, $testCount);

        // 3. 测试解冻资金
        $this->testUnfreezeAsset($userId, $accountType, $currencyId, $amount, $flowType, $testCount);

        $this->info('所有测试完成！');
    }

    /**
     * 测试增加资金
     */
    private function testAddAsset(int $userId, int $accountType, int $currencyId, float $amount, int $flowType, int $testCount): void
    {
        $this->info("=== 测试增加资金 ({$testCount}次并发) ===");

        $startTime = microtime(true);

        $parallel = new Parallel($testCount);

        for ($i = 0; $i < $testCount; $i++) {
            $parallel->add(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $i) {
                try {
                    $operationStart = microtime(true);
                    $result = $this->assetService->addAvailableAsset(
                        $userId,
                        $accountType,
                        $currencyId,
                        $amount,
                        $flowType,
                        $i // 使用循环索引作为relatedId
                    );
                    $operationEnd = microtime(true);

                    return [
                        'success' => $result,
                        'duration' => ($operationEnd - $operationStart) * 1000, // 转换为毫秒
                        'index' => $i
                    ];
                } catch (\Exception $e) {
                    return [
                        'success' => false,
                        'error' => $e->getMessage(),
                        'index' => $i
                    ];
                }
            });
        }

        $results = $parallel->wait();
        $endTime = microtime(true);

        $this->analyzeResults('增加资金', $results, $startTime, $endTime, $testCount);
    }

    /**
     * 测试冻结资金
     */
    private function testFreezeAsset(int $userId, int $accountType, int $currencyId, float $amount, int $flowType, int $testCount): void
    {
        $this->info("=== 测试冻结资金 ({$testCount}次并发) ===");

        $startTime = microtime(true);

        $parallel = new Parallel($testCount);

        for ($i = 0; $i < $testCount; $i++) {
            $parallel->add(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $i) {
                try {
                    $operationStart = microtime(true);
                    $result = $this->assetService->freezeAsset(
                        $userId,
                        $accountType,
                        $currencyId,
                        $amount,
                        $flowType,
                        $i // 使用循环索引作为relatedId
                    );
                    $operationEnd = microtime(true);

                    return [
                        'success' => $result,
                        'duration' => ($operationEnd - $operationStart) * 1000,
                        'index' => $i
                    ];
                } catch (\Exception $e) {
                    return [
                        'success' => false,
                        'error' => $e->getMessage(),
                        'index' => $i
                    ];
                }
            });
        }

        $results = $parallel->wait();
        $endTime = microtime(true);

        $this->analyzeResults('冻结资金', $results, $startTime, $endTime, $testCount);
    }

    /**
     * 测试解冻资金
     */
    private function testUnfreezeAsset(int $userId, int $accountType, int $currencyId, float $amount, int $flowType, int $testCount): void
    {
        $this->info("=== 测试解冻资金 ({$testCount}次并发) ===");

        $startTime = microtime(true);

        $parallel = new Parallel($testCount);

        for ($i = 0; $i < $testCount; $i++) {
            $parallel->add(function () use ($userId, $accountType, $currencyId, $amount, $flowType, $i) {
                try {
                    $operationStart = microtime(true);
                    $result = $this->assetService->unfreezeAsset(
                        $userId,
                        $accountType,
                        $currencyId,
                        $amount,
                        $flowType,
                        $i // 使用循环索引作为relatedId
                    );
                    $operationEnd = microtime(true);

                    return [
                        'success' => $result,
                        'duration' => ($operationEnd - $operationStart) * 1000,
                        'index' => $i
                    ];
                } catch (\Exception $e) {
                    return [
                        'success' => false,
                        'error' => $e->getMessage(),
                        'index' => $i
                    ];
                }
            });
        }

        $results = $parallel->wait();
        $endTime = microtime(true);

        $this->analyzeResults('解冻资金', $results, $startTime, $endTime, $testCount);
    }

    /**
     * 分析测试结果
     */
    private function analyzeResults(string $operation, array $results, float $startTime, float $endTime, int $testCount): void
    {
        $totalTime = ($endTime - $startTime) * 1000; // 转换为毫秒
        $successCount = 0;
        $failCount = 0;
        $durations = [];
        $errors = [];

        foreach ($results as $result) {
            if ($result['success']) {
                $successCount++;
                if (isset($result['duration'])) {
                    $durations[] = $result['duration'];
                }
            } else {
                $failCount++;
                if (isset($result['error'])) {
                    $errors[] = $result['error'];
                }
            }
        }

        // 计算统计数据
        $avgDuration = !empty($durations) ? array_sum($durations) / count($durations) : 0;
        $minDuration = !empty($durations) ? min($durations) : 0;
        $maxDuration = !empty($durations) ? max($durations) : 0;
        $successRate = ($successCount / $testCount) * 100;
        $qps = $testCount / ($totalTime / 1000); // 每秒处理数

        // 输出结果
        $this->info("操作类型: {$operation}");
        $this->info("总耗时: " . number_format($totalTime, 2) . " ms");
        $this->info("成功数量: {$successCount}/{$testCount}");
        $this->info("失败数量: {$failCount}");
        $this->info("成功率: " . number_format($successRate, 2) . "%");
        $this->info("QPS: " . number_format($qps, 2));

        if (!empty($durations)) {
            $this->info("单次操作耗时统计:");
            $this->info("  - 平均: " . number_format($avgDuration, 2) . " ms");
            $this->info("  - 最小: " . number_format($minDuration, 2) . " ms");
            $this->info("  - 最大: " . number_format($maxDuration, 2) . " ms");
        }

        if (!empty($errors)) {
            $this->error("错误信息:");
            $errorCounts = array_count_values($errors);
            foreach ($errorCounts as $error => $count) {
                $this->error("  - {$error}: {$count}次");
            }
        }

        $this->info('');
    }
}
