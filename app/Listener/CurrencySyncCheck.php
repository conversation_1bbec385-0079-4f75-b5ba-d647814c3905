<?php

/**
 * CurrencySyncCheck.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/24
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Listener;

use App\MarketData\Service\Chain\ChainCurrencySync;
use App\MarketData\Service\CryptoCurrencySync;
use App\MarketData\Service\CryptoKlineSync;
use Hyperf\Context\ApplicationContext;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Framework\Event\OnStart;
use Psr\Container\ContainerInterface;

#[Listener]
class CurrencySyncCheck implements ListenerInterface
{
    public function listen():array
    {
        return [
            OnStart::class
        ];
    }

    public function process(object $event): void
    {
//        go(function(){

//            $sync = ApplicationContext::getContainer()->get(CryptoCurrencySync::class);
             //同步币安的现货合约数据
//            $sync->sync();
            // 同步合约币种的保证金档位
//            $sync->syncMarginLevels();
//            $sync->syncContractCurrencyConfig();
//        });
//        go(function (){
            // 同步币安的k线数据
//           ApplicationContext::getContainer()->get(CryptoKlineSync::class)->syncAllKlines();
//        });

//        go(function(){
              // 同步现货和合约的币种配置
//            //ApplicationContext::getContainer()->get(CryptoCurrencySync::class)->syncContractCurrencyConfig();
            //ApplicationContext::getContainer()->get(CryptoCurrencySync::class)->syncSpotCurrencyConfig();
//        });

        go(function (){
            //加载币种配置到redis 随启动加载
            //ApplicationContext::getContainer()->get(CryptoCurrencySync::class)->clearCurrenciesFromRedis();
            //ApplicationContext::getContainer()->get(CryptoCurrencySync::class)->loadCurrenciesToRedis();
            //同步平台币种的充提配置和支持的链配置
            //ApplicationContext::getContainer()->get(CryptoCurrencySync::class)->syncBitgetCurrencyTransfer();

            //同步平台币种的栏目配置，定时启动
            //ApplicationContext::getContainer()->get(CryptoCurrencySync::class)->syncBitgetCurrencyCategories();

            //同步币安alpha 币种数据，需要定时同步
            //ApplicationContext::getContainer()->get(ChainCurrencySync::class)->syncChainCurrencies();

            //同步币安alpha 的币种详情数据，启动一次
            //ApplicationContext::getContainer()->get(ChainCurrencySync::class)->syncChainCurrencyMeta();
        });

    }
}