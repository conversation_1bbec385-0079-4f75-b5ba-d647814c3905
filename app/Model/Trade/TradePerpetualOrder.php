<?php

declare(strict_types=1);

namespace App\Model\Trade;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 订单ID
 * @property int $user_id 用户ID
 * @property int $currency_id 币种ID
 * @property int $match_order_id 撮合引擎订单ID
 * @property int $margin_mode 保证金模式：1-全仓，2-逐仓
 * @property int $side 方向：1-买入开多，2-卖出开空，3-买入平空，4-卖出平多
 * @property int $order_type 订单类型：1-限价，2-市价，3-止盈止损
 * @property float $price 委托价格
 * @property float $quantity 委托数量（币本位）
 * @property float $margin_amount 保证金金额
 * @property float $frozen_amount 冻结保证金金额
 * @property float $used_amount 已使用保证金金额
 * @property float $estimated_fee 预估手续费
 * @property float $actual_fee 实际手续费
 * @property int $position_id 关联的仓位ID
 * @property float $leverage 杠杆倍数
 * @property int $reduce_only 只减仓：0-否，1-是
 * @property int $time_in_force 有效期：1-GTC，2-IOC，3-FOK
 * @property float $stop_price 触发价格（止盈止损单）
 * @property int $trigger_type 触发类型：1-标记价格，2-最新价格
 * @property int $is_copy 0-用户开单，1-跟交易员单
 * @property int|null $copy_order_id 跟单订单id
 * @property int $is_trader 是否交易员订单：0-不是，1-是
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class TradePerpetualOrder extends Model
{
    /**
     * 订单ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 撮合引擎订单ID
     */
    public const FIELD_MATCH_ORDER_ID = 'match_order_id';
    /**
     * 保证金模式：1-全仓，2-逐仓
     */
    public const FIELD_MARGIN_MODE = 'margin_mode';
    /**
     * 方向：1-买入开多，2-卖出开空，3-买入平空，4-卖出平多
     */
    public const FIELD_SIDE = 'side';
    /**
     * 订单类型：1-限价，2-市价，3-止盈止损
     */
    public const FIELD_ORDER_TYPE = 'order_type';
    /**
     * 委托价格
     */
    public const FIELD_PRICE = 'price';
    /**
     * 委托数量（币本位）
     */
    public const FIELD_QUANTITY = 'quantity';
    /**
     * 保证金金额
     */
    public const FIELD_MARGIN_AMOUNT = 'margin_amount';
    /**
     * 冻结保证金金额
     */
    public const FIELD_FROZEN_AMOUNT = 'frozen_amount';
    /**
     * 已使用保证金金额
     */
    public const FIELD_USED_AMOUNT = 'used_amount';
    /**
     * 预估手续费
     */
    public const FIELD_ESTIMATED_FEE = 'estimated_fee';
    /**
     * 实际手续费
     */
    public const FIELD_ACTUAL_FEE = 'actual_fee';
    /**
     * 关联的仓位ID
     */
    public const FIELD_POSITION_ID = 'position_id';
    /**
     * 杠杆倍数
     */
    public const FIELD_LEVERAGE = 'leverage';
    /**
     * 只减仓：0-否，1-是
     */
    public const FIELD_REDUCE_ONLY = 'reduce_only';
    /**
     * 有效期：1-GTC，2-IOC，3-FOK
     */
    public const FIELD_TIME_IN_FORCE = 'time_in_force';
    /**
     * 触发价格（止盈止损单）
     */
    public const FIELD_STOP_PRICE = 'stop_price';
    /**
     * 触发类型：1-标记价格，2-最新价格
     */
    public const FIELD_TRIGGER_TYPE = 'trigger_type';
    /**
     * 0-用户开单，1-跟交易员单
     */
    public const FIELD_IS_COPY = 'is_copy';
    /**
     * 跟单订单id
     */
    public const FIELD_COPY_ORDER_ID = 'copy_order_id';
    /**
     * 是否交易员订单：0-不是，1-是
     */
    public const FIELD_IS_TRADER = 'is_trader';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_perpetual_order';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'currency_id', 'match_order_id', 'margin_mode', 'side', 'order_type', 'price', 'quantity', 'margin_amount', 'frozen_amount', 'used_amount', 'estimated_fee', 'actual_fee', 'position_id', 'leverage', 'reduce_only', 'time_in_force', 'stop_price', 'trigger_type', 'is_copy', 'copy_order_id', 'is_trader', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        // 订单ID
        'id' => 'integer',
        // 用户ID
        'user_id' => 'integer',
        // 币种ID
        'currency_id' => 'integer',
        // 撮合引擎订单ID
        'match_order_id' => 'integer',
        // 保证金模式：1-全仓，2-逐仓
        'margin_mode' => 'integer',
        // 方向：1-买入开多，2-卖出开空，3-买入平空，4-卖出平多
        'side' => 'integer',
        // 订单类型：1-限价，2-市价，3-止盈止损
        'order_type' => 'integer',
        // 委托价格
        'price' => 'float',
        // 委托数量（币本位）
        'quantity' => 'float',
        // 保证金金额
        'margin_amount' => 'float',
        // 冻结保证金金额
        'frozen_amount' => 'float',
        // 已使用保证金金额
        'used_amount' => 'float',
        // 预估手续费
        'estimated_fee' => 'float',
        // 实际手续费
        'actual_fee' => 'float',
        // 关联的仓位ID
        'position_id' => 'int',
        // 杠杆倍数
        'leverage' => 'float',
        // 只减仓：0-否，1-是
        'reduce_only' => 'integer',
        // 有效期：1-GTC，2-IOC，3-FOK
        'time_in_force' => 'integer',
        // 触发价格（止盈止损单）
        'stop_price' => 'float',
        // 触发类型：1-标记价格，2-最新价格
        'trigger_type' => 'integer',
        // 0-用户开单，1-跟交易员单
        'is_copy' => 'integer',
        // 跟单订单id
        'copy_order_id' => 'integer',
        // 是否交易员订单：0-不是，1-是
        'is_trader' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getMatchOrderId() : int
    {
        return $this->match_order_id;
    }
    public function setMatchOrderId($value) : object
    {
        $this->match_order_id = $value;
        return $this;
    }
    public function getMarginMode() : int
    {
        return $this->margin_mode;
    }
    public function setMarginMode($value) : object
    {
        $this->margin_mode = $value;
        return $this;
    }
    public function getSide() : int
    {
        return $this->side;
    }
    public function setSide($value) : object
    {
        $this->side = $value;
        return $this;
    }
    public function getOrderType() : int
    {
        return $this->order_type;
    }
    public function setOrderType($value) : object
    {
        $this->order_type = $value;
        return $this;
    }
    public function getPrice() : float
    {
        return $this->price;
    }
    public function setPrice($value) : object
    {
        $this->price = $value;
        return $this;
    }
    public function getQuantity() : float
    {
        return $this->quantity;
    }
    public function setQuantity($value) : object
    {
        $this->quantity = $value;
        return $this;
    }
    public function getMarginAmount() : float
    {
        return $this->margin_amount;
    }
    public function setMarginAmount($value) : object
    {
        $this->margin_amount = $value;
        return $this;
    }
    public function getLeverage() : float
    {
        return $this->leverage;
    }
    public function setLeverage($value) : object
    {
        $this->leverage = $value;
        return $this;
    }
    public function getReduceOnly() : int
    {
        return $this->reduce_only;
    }
    public function setReduceOnly($value) : object
    {
        $this->reduce_only = $value;
        return $this;
    }
    public function getTimeInForce() : int
    {
        return $this->time_in_force;
    }
    public function setTimeInForce($value) : object
    {
        $this->time_in_force = $value;
        return $this;
    }
    public function getStopPrice() : float
    {
        return $this->stop_price;
    }
    public function setStopPrice($value) : object
    {
        $this->stop_price = $value;
        return $this;
    }
    public function getTriggerType() : int
    {
        return $this->trigger_type;
    }
    public function setTriggerType($value) : object
    {
        $this->trigger_type = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
