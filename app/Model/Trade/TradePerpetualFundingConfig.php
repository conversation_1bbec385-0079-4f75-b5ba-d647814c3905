<?php

declare(strict_types=1);

namespace App\Model\Trade;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $currency_id 
 * @property float $base_rate 基础利率
 * @property float $max 资金费用上限
 * @property float $min 资金费用下限
 * @property int $interval 资金费率收取间隔时间
 * @property int $status 是否收取资金费率
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class TradePerpetualFundingConfig extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 基础利率
     */
    public const FIELD_BASE_RATE = 'base_rate';
    /**
     * 资金费用上限
     */
    public const FIELD_MAX = 'max';
    /**
     * 资金费用下限
     */
    public const FIELD_MIN = 'min';
    /**
     * 资金费率收取间隔时间
     */
    public const FIELD_INTERVAL = 'interval';
    /**
     * 是否收取资金费率
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_perpetual_funding_config';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'currency_id', 'base_rate', 'max', 'min', 'interval', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        'currency_id' => 'integer',
        // 基础利率
        'base_rate' => 'float',
        // 资金费用上限
        'max' => 'float',
        // 资金费用下限
        'min' => 'float',
        // 资金费率收取间隔时间
        'interval' => 'integer',
        // 是否收取资金费率
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getBaseRate() : float
    {
        return $this->base_rate;
    }
    public function setBaseRate($value) : object
    {
        $this->base_rate = $value;
        return $this;
    }
    public function getMax() : float
    {
        return $this->max;
    }
    public function setMax($value) : object
    {
        $this->max = $value;
        return $this;
    }
    public function getMin() : float
    {
        return $this->min;
    }
    public function setMin($value) : object
    {
        $this->min = $value;
        return $this;
    }
    public function getInterval() : int
    {
        return $this->interval;
    }
    public function setInterval($value) : object
    {
        $this->interval = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
