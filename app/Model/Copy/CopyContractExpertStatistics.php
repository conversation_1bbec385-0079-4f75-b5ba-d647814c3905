<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 合约交易专家统计表模型
 */

namespace App\Model\Copy;

use App\QueryBuilder\Model;

/**
 * @property int $id 主键ID
 * @property int $expert_id 专家ID
 * @property int $expert_user_id 专家用户ID（冗余字段）
 * @property float $total_profit 总盈利
 * @property float $total_profit_7d 7日总盈利
 * @property float $total_profit_30d 30日总盈利
 * @property float $total_profit_90d 90日总盈利
 * @property float $total_profit_180d 180日总盈利
 * @property float $total_loss 总亏损
 * @property float $total_loss_7d 7日总亏损
 * @property float $total_loss_30d 30日总亏损
 * @property float $total_loss_90d 90日总亏损
 * @property float $total_loss_180d 180日总亏损
 * @property int $profit_order_count 盈利订单数
 * @property int $profit_order_count_7d 7日盈利订单数
 * @property int $profit_order_count_30d 30日盈利订单数
 * @property int $profit_order_count_90d 90日盈利订单数
 * @property int $profit_order_count_180d 180日盈利订单数
 * @property int $loss_order_count 亏损订单数
 * @property int $loss_order_count_7d 7日亏损订单数
 * @property int $loss_order_count_30d 30日亏损订单数
 * @property int $loss_order_count_90d 90日亏损订单数
 * @property int $loss_order_count_180d 180日亏损订单数
 * @property float $average_profit 平均盈利
 * @property float $average_profit_7d 7日平均盈利
 * @property float $average_profit_30d 30日平均盈利
 * @property float $average_profit_90d 90日平均盈利
 * @property float $average_profit_180d 180日平均盈利
 * @property float $average_loss 平均亏损
 * @property float $average_loss_7d 7日平均亏损
 * @property float $average_loss_30d 30日平均亏损
 * @property float $average_loss_90d 90日平均亏损
 * @property float $average_loss_180d 180日平均亏损
 * @property float $profit 盈亏金额
 * @property float $profit_7d 7日盈亏金额
 * @property float $profit_30d 30日盈亏金额
 * @property float $profit_90d 90日盈亏金额
 * @property float $profit_180d 180日盈亏金额
 * @property float $win_rate 胜率 %
 * @property float $win_rate_7d 7日胜率 %
 * @property float $win_rate_30d 30日胜率 %
 * @property float $win_rate_90d 90日胜率 %
 * @property float $win_rate_180d 180日胜率 %
 * @property float $profit_rate 收益率 %
 * @property float $profit_rate_7d 7日收益率 %
 * @property float $profit_rate_30d 30日收益率 %
 * @property float $profit_rate_90d 90日收益率 %
 * @property float $profit_rate_180d 180日收益率 %
 * @property float $follower_profit 跟单者收益
 * @property float $follower_profit_7d 7日跟单者收益
 * @property float $follower_profit_30d 30日跟单者收益
 * @property float $follower_profit_90d 90日跟单者收益
 * @property float $follower_profit_180d 180日跟单者收益
 * @property float $max_drawdown 最大回撤
 * @property float $max_drawdown_7d 7日最大回撤
 * @property float $max_drawdown_30d 30日最大回撤
 * @property float $max_drawdown_90d 90日最大回撤
 * @property float $max_drawdown_180d 180日最大回撤
 * @property float $aum 资产管理规模
 * @property int $trade_frequency 交易频率
 * @property int $trade_frequency_7d 7日交易频率
 * @property int $trade_frequency_30d 30日交易频率
 * @property int $trade_frequency_90d 90日交易频率
 * @property int $trade_frequency_180d 180日交易频率
 * @property int $total_follower_count 累计跟单人数
 * @property \Carbon\Carbon|null $created_at 创建时间
 * @property \Carbon\Carbon|null $updated_at 更新时间
 */
final class CopyContractExpertStatistics extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 专家ID
     */
    public const FIELD_EXPERT_ID = 'expert_id';
    /**
     * 专家用户ID（冗余字段）
     */
    public const FIELD_EXPERT_USER_ID = 'expert_user_id';
    /**
     * 总盈利
     */
    public const FIELD_TOTAL_PROFIT = 'total_profit';
    /**
     * 7日总盈利
     */
    public const FIELD_TOTAL_PROFIT_7D = 'total_profit_7d';
    /**
     * 30日总盈利
     */
    public const FIELD_TOTAL_PROFIT_30D = 'total_profit_30d';
    /**
     * 90日总盈利
     */
    public const FIELD_TOTAL_PROFIT_90D = 'total_profit_90d';
    /**
     * 180日总盈利
     */
    public const FIELD_TOTAL_PROFIT_180D = 'total_profit_180d';
    /**
     * 总亏损
     */
    public const FIELD_TOTAL_LOSS = 'total_loss';
    /**
     * 7日总亏损
     */
    public const FIELD_TOTAL_LOSS_7D = 'total_loss_7d';
    /**
     * 30日总亏损
     */
    public const FIELD_TOTAL_LOSS_30D = 'total_loss_30d';
    /**
     * 90日总亏损
     */
    public const FIELD_TOTAL_LOSS_90D = 'total_loss_90d';
    /**
     * 180日总亏损
     */
    public const FIELD_TOTAL_LOSS_180D = 'total_loss_180d';
    /**
     * 盈利订单数
     */
    public const FIELD_PROFIT_ORDER_COUNT = 'profit_order_count';
    /**
     * 7日盈利订单数
     */
    public const FIELD_PROFIT_ORDER_COUNT_7D = 'profit_order_count_7d';
    /**
     * 30日盈利订单数
     */
    public const FIELD_PROFIT_ORDER_COUNT_30D = 'profit_order_count_30d';
    /**
     * 90日盈利订单数
     */
    public const FIELD_PROFIT_ORDER_COUNT_90D = 'profit_order_count_90d';
    /**
     * 180日盈利订单数
     */
    public const FIELD_PROFIT_ORDER_COUNT_180D = 'profit_order_count_180d';
    /**
     * 亏损订单数
     */
    public const FIELD_LOSS_ORDER_COUNT = 'loss_order_count';
    /**
     * 7日亏损订单数
     */
    public const FIELD_LOSS_ORDER_COUNT_7D = 'loss_order_count_7d';
    /**
     * 30日亏损订单数
     */
    public const FIELD_LOSS_ORDER_COUNT_30D = 'loss_order_count_30d';
    /**
     * 90日亏损订单数
     */
    public const FIELD_LOSS_ORDER_COUNT_90D = 'loss_order_count_90d';
    /**
     * 180日亏损订单数
     */
    public const FIELD_LOSS_ORDER_COUNT_180D = 'loss_order_count_180d';
    /**
     * 平均盈利
     */
    public const FIELD_AVERAGE_PROFIT = 'average_profit';
    /**
     * 7日平均盈利
     */
    public const FIELD_AVERAGE_PROFIT_7D = 'average_profit_7d';
    /**
     * 30日平均盈利
     */
    public const FIELD_AVERAGE_PROFIT_30D = 'average_profit_30d';
    /**
     * 90日平均盈利
     */
    public const FIELD_AVERAGE_PROFIT_90D = 'average_profit_90d';
    /**
     * 180日平均盈利
     */
    public const FIELD_AVERAGE_PROFIT_180D = 'average_profit_180d';
    /**
     * 平均亏损
     */
    public const FIELD_AVERAGE_LOSS = 'average_loss';
    /**
     * 7日平均亏损
     */
    public const FIELD_AVERAGE_LOSS_7D = 'average_loss_7d';
    /**
     * 30日平均亏损
     */
    public const FIELD_AVERAGE_LOSS_30D = 'average_loss_30d';
    /**
     * 90日平均亏损
     */
    public const FIELD_AVERAGE_LOSS_90D = 'average_loss_90d';
    /**
     * 180日平均亏损
     */
    public const FIELD_AVERAGE_LOSS_180D = 'average_loss_180d';
    /**
     * 盈亏金额
     */
    public const FIELD_PROFIT = 'profit';
    /**
     * 7日盈亏金额
     */
    public const FIELD_PROFIT_7D = 'profit_7d';
    /**
     * 30日盈亏金额
     */
    public const FIELD_PROFIT_30D = 'profit_30d';
    /**
     * 90日盈亏金额
     */
    public const FIELD_PROFIT_90D = 'profit_90d';
    /**
     * 180日盈亏金额
     */
    public const FIELD_PROFIT_180D = 'profit_180d';
    /**
     * 胜率 %
     */
    public const FIELD_WIN_RATE = 'win_rate';
    /**
     * 7日胜率 %
     */
    public const FIELD_WIN_RATE_7D = 'win_rate_7d';
    /**
     * 30日胜率 %
     */
    public const FIELD_WIN_RATE_30D = 'win_rate_30d';
    /**
     * 90日胜率 %
     */
    public const FIELD_WIN_RATE_90D = 'win_rate_90d';
    /**
     * 180日胜率 %
     */
    public const FIELD_WIN_RATE_180D = 'win_rate_180d';
    /**
     * 收益率 %
     */
    public const FIELD_PROFIT_RATE = 'profit_rate';
    /**
     * 7日收益率 %
     */
    public const FIELD_PROFIT_RATE_7D = 'profit_rate_7d';
    /**
     * 30日收益率 %
     */
    public const FIELD_PROFIT_RATE_30D = 'profit_rate_30d';
    /**
     * 90日收益率 %
     */
    public const FIELD_PROFIT_RATE_90D = 'profit_rate_90d';
    /**
     * 180日收益率 %
     */
    public const FIELD_PROFIT_RATE_180D = 'profit_rate_180d';
    /**
     * 跟单者收益
     */
    public const FIELD_FOLLOWER_PROFIT = 'follower_profit';
    /**
     * 7日跟单者收益
     */
    public const FIELD_FOLLOWER_PROFIT_7D = 'follower_profit_7d';
    /**
     * 30日跟单者收益
     */
    public const FIELD_FOLLOWER_PROFIT_30D = 'follower_profit_30d';
    /**
     * 90日跟单者收益
     */
    public const FIELD_FOLLOWER_PROFIT_90D = 'follower_profit_90d';
    /**
     * 180日跟单者收益
     */
    public const FIELD_FOLLOWER_PROFIT_180D = 'follower_profit_180d';
    /**
     * 最大回撤
     */
    public const FIELD_MAX_DRAWDOWN = 'max_drawdown';
    /**
     * 7日最大回撤
     */
    public const FIELD_MAX_DRAWDOWN_7D = 'max_drawdown_7d';
    /**
     * 30日最大回撤
     */
    public const FIELD_MAX_DRAWDOWN_30D = 'max_drawdown_30d';
    /**
     * 90日最大回撤
     */
    public const FIELD_MAX_DRAWDOWN_90D = 'max_drawdown_90d';
    /**
     * 180日最大回撤
     */
    public const FIELD_MAX_DRAWDOWN_180D = 'max_drawdown_180d';
    /**
     * 资产管理规模
     */
    public const FIELD_AUM = 'aum';
    /**
     * 交易频率
     */
    public const FIELD_TRADE_FREQUENCY = 'trade_frequency';
    /**
     * 7日交易频率
     */
    public const FIELD_TRADE_FREQUENCY_7D = 'trade_frequency_7d';
    /**
     * 30日交易频率
     */
    public const FIELD_TRADE_FREQUENCY_30D = 'trade_frequency_30d';
    /**
     * 90日交易频率
     */
    public const FIELD_TRADE_FREQUENCY_90D = 'trade_frequency_90d';
    /**
     * 180日交易频率
     */
    public const FIELD_TRADE_FREQUENCY_180D = 'trade_frequency_180d';
    /**
     * 累计跟单人数
     */
    public const FIELD_TOTAL_FOLLOWER_COUNT = 'total_follower_count';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_contract_expert_statistics';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 主键ID
        'expert_id', // 专家ID
        'expert_user_id', // 专家用户ID（冗余字段）
        'total_profit', // 总盈利
        'total_profit_7d', // 7日总盈利
        'total_profit_30d', // 30日总盈利
        'total_profit_90d', // 90日总盈利
        'total_profit_180d', // 180日总盈利
        'total_loss', // 总亏损
        'total_loss_7d', // 7日总亏损
        'total_loss_30d', // 30日总亏损
        'total_loss_90d', // 90日总亏损
        'total_loss_180d', // 180日总亏损
        'profit_order_count', // 盈利订单数
        'profit_order_count_7d', // 7日盈利订单数
        'profit_order_count_30d', // 30日盈利订单数
        'profit_order_count_90d', // 90日盈利订单数
        'profit_order_count_180d', // 180日盈利订单数
        'loss_order_count', // 亏损订单数
        'loss_order_count_7d', // 7日亏损订单数
        'loss_order_count_30d', // 30日亏损订单数
        'loss_order_count_90d', // 90日亏损订单数
        'loss_order_count_180d', // 180日亏损订单数
        'average_profit', // 平均盈利
        'average_profit_7d', // 7日平均盈利
        'average_profit_30d', // 30日平均盈利
        'average_profit_90d', // 90日平均盈利
        'average_profit_180d', // 180日平均盈利
        'average_loss', // 平均亏损
        'average_loss_7d', // 7日平均亏损
        'average_loss_30d', // 30日平均亏损
        'average_loss_90d', // 90日平均亏损
        'average_loss_180d', // 180日平均亏损
        'profit', // 盈亏金额
        'profit_7d', // 7日盈亏金额
        'profit_30d', // 30日盈亏金额
        'profit_90d', // 90日盈亏金额
        'profit_180d', // 180日盈亏金额
        'win_rate', // 胜率 %
        'win_rate_7d', // 7日胜率 %
        'win_rate_30d', // 30日胜率 %
        'win_rate_90d', // 90日胜率 %
        'win_rate_180d', // 180日胜率 %
        'profit_rate', // 收益率 %
        'profit_rate_7d', // 7日收益率 %
        'profit_rate_30d', // 30日收益率 %
        'profit_rate_90d', // 90日收益率 %
        'profit_rate_180d', // 180日收益率 %
        'follower_profit', // 跟单者收益
        'follower_profit_7d', // 7日跟单者收益
        'follower_profit_30d', // 30日跟单者收益
        'follower_profit_90d', // 90日跟单者收益
        'follower_profit_180d', // 180日跟单者收益
        'max_drawdown', // 最大回撤
        'max_drawdown_7d', // 7日最大回撤
        'max_drawdown_30d', // 30日最大回撤
        'max_drawdown_90d', // 90日最大回撤
        'max_drawdown_180d', // 180日最大回撤
        'aum', // 资产管理规模
        'trade_frequency', // 交易频率
        'trade_frequency_7d', // 7日交易频率
        'trade_frequency_30d', // 30日交易频率
        'trade_frequency_90d', // 90日交易频率
        'trade_frequency_180d', // 180日交易频率
        'total_follower_count', // 累计跟单人数
        'created_at', // 创建时间
        'updated_at' // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'expert_id' => 'integer', // 专家ID
        'expert_user_id' => 'integer', // 专家用户ID（冗余字段）
        'total_profit' => 'float', // 总盈利
        'total_profit_7d' => 'float', // 7日总盈利
        'total_profit_30d' => 'float', // 30日总盈利
        'total_profit_90d' => 'float', // 90日总盈利
        'total_profit_180d' => 'float', // 180日总盈利
        'total_loss' => 'float', // 总亏损
        'total_loss_7d' => 'float', // 7日总亏损
        'total_loss_30d' => 'float', // 30日总亏损
        'total_loss_90d' => 'float', // 90日总亏损
        'total_loss_180d' => 'float', // 180日总亏损
        'profit_order_count' => 'integer', // 盈利订单数
        'profit_order_count_7d' => 'integer', // 7日盈利订单数
        'profit_order_count_30d' => 'integer', // 30日盈利订单数
        'profit_order_count_90d' => 'integer', // 90日盈利订单数
        'profit_order_count_180d' => 'integer', // 180日盈利订单数
        'loss_order_count' => 'integer', // 亏损订单数
        'loss_order_count_7d' => 'integer', // 7日亏损订单数
        'loss_order_count_30d' => 'integer', // 30日亏损订单数
        'loss_order_count_90d' => 'integer', // 90日亏损订单数
        'loss_order_count_180d' => 'integer', // 180日亏损订单数
        'average_profit' => 'float', // 平均盈利
        'average_profit_7d' => 'float', // 7日平均盈利
        'average_profit_30d' => 'float', // 30日平均盈利
        'average_profit_90d' => 'float', // 90日平均盈利
        'average_profit_180d' => 'float', // 180日平均盈利
        'average_loss' => 'float', // 平均亏损
        'average_loss_7d' => 'float', // 7日平均亏损
        'average_loss_30d' => 'float', // 30日平均亏损
        'average_loss_90d' => 'float', // 90日平均亏损
        'average_loss_180d' => 'float', // 180日平均亏损
        'profit' => 'float', // 盈亏金额
        'profit_7d' => 'float', // 7日盈亏金额
        'profit_30d' => 'float', // 30日盈亏金额
        'profit_90d' => 'float', // 90日盈亏金额
        'profit_180d' => 'float', // 180日盈亏金额
        'win_rate' => 'float', // 胜率 %
        'win_rate_7d' => 'float', // 7日胜率 %
        'win_rate_30d' => 'float', // 30日胜率 %
        'win_rate_90d' => 'float', // 90日胜率 %
        'win_rate_180d' => 'float', // 180日胜率 %
        'profit_rate' => 'float', // 收益率 %
        'profit_rate_7d' => 'float', // 7日收益率 %
        'profit_rate_30d' => 'float', // 30日收益率 %
        'profit_rate_90d' => 'float', // 90日收益率 %
        'profit_rate_180d' => 'float', // 180日收益率 %
        'follower_profit' => 'float', // 跟单者收益
        'follower_profit_7d' => 'float', // 7日跟单者收益
        'follower_profit_30d' => 'float', // 30日跟单者收益
        'follower_profit_90d' => 'float', // 90日跟单者收益
        'follower_profit_180d' => 'float', // 180日跟单者收益
        'max_drawdown' => 'float', // 最大回撤
        'max_drawdown_7d' => 'float', // 7日最大回撤
        'max_drawdown_30d' => 'float', // 30日最大回撤
        'max_drawdown_90d' => 'float', // 90日最大回撤
        'max_drawdown_180d' => 'float', // 180日最大回撤
        'aum' => 'float', // 资产管理规模
        'trade_frequency' => 'integer', // 交易频率
        'trade_frequency_7d' => 'integer', // 7日交易频率
        'trade_frequency_30d' => 'integer', // 30日交易频率
        'trade_frequency_90d' => 'integer', // 90日交易频率
        'trade_frequency_180d' => 'integer', // 180日交易频率
        'total_follower_count' => 'integer', // 累计跟单人数
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime' // 更新时间
    ];
}
