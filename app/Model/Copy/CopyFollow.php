<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 关注模型
 */

namespace App\Model\Copy;

use App\Model\Copy\Enums\ExpertType;
use App\Model\User\User;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\Database\Model\Relations\MorphTo;

/**
 * @property int $id 主键ID
 * @property int $user_id 关注者用户ID
 * @property int $expert_id 专家ID
 * @property string $expert_type 专家模型类名（多态关联）
 * @property int $expert_user_id 专家用户ID（冗余字段）
 * @property ExpertType $type 专家类型：1-合约，2-现货
 * @property Carbon|null $created_at 创建时间
 * @property User $user 用户关联
 * @property CopyContractExpert|CopySpotExpert $expert 专家关联（多态）
 */
final class CopyFollow extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 关注者用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 专家ID
     */
    public const FIELD_EXPERT_ID = 'expert_id';
    /**
     * 专家模型类名（多态关联）
     */
    public const FIELD_EXPERT_TYPE = 'expert_type';
    /**
     * 专家用户ID（冗余字段）
     */
    public const FIELD_EXPERT_USER_ID = 'expert_user_id';
    /**
     * 专家类型：1-合约，2-现货
     */
    public const FIELD_TYPE = 'type';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_follow';

    /**
     * Indicates if the model should be timestamped.
     */
    public bool $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'user_id', // 关注者用户ID
        'expert_id', // 专家ID
        'expert_type', // 专家模型类名（多态关联）
        'expert_user_id', // 专家用户ID（冗余字段）
        'type', // 专家类型：1-合约，2-现货
        'created_at', // 创建时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'user_id' => 'integer', // 关注者用户ID
        'expert_id' => 'integer', // 专家ID
        'expert_user_id' => 'integer', // 专家用户ID（冗余字段）
        'type' => ExpertType::class, // 专家类型：1-合约，2-现货
        'created_at' => 'datetime', // 创建时间
    ];

    /**
     * 用户关联
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 专家关联（多态）
     */
    public function expert(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取关注者用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置关注者用户ID
     */
    public function setUserId(int $value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取专家ID
     */
    public function getExpertId(): int
    {
        return $this->expert_id;
    }

    /**
     * 设置专家ID
     */
    public function setExpertId(int $value): static
    {
        $this->expert_id = $value;
        return $this;
    }

    /**
     * 获取专家模型类名（多态关联）
     */
    public function getExpertType(): string
    {
        return $this->expert_type;
    }

    /**
     * 设置专家模型类名（多态关联）
     */
    public function setExpertType(string $value): static
    {
        $this->expert_type = $value;
        return $this;
    }

    /**
     * 获取专家用户ID（冗余字段）
     */
    public function getExpertUserId(): int
    {
        return $this->expert_user_id;
    }

    /**
     * 设置专家用户ID（冗余字段）
     */
    public function setExpertUserId(int $value): static
    {
        $this->expert_user_id = $value;
        return $this;
    }

    /**
     * 获取专家类型
     */
    public function getType(): ExpertType
    {
        return $this->type;
    }

    /**
     * 设置专家类型
     */
    public function setType(ExpertType $value): static
    {
        $this->type = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }
}
