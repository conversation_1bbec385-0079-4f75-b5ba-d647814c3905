<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 现货分润记录模型
 */

namespace App\Model\Copy;

use App\Model\User\User;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;

/**
 * @property int $id 主键ID
 * @property int $expert_id 专家ID
 * @property int $expert_user_id 专家用户ID（冗余字段）
 * @property int $follower_user_id 跟单者用户ID
 * @property int $copy_order_id 跟单记录ID
 * @property int $expert_order_id 专家订单ID（关联trade_spot_order）
 * @property int $follower_order_id 跟单者订单ID（关联trade_spot_order）
 * @property string $profit 盈亏金额
 * @property string $profit_sharing 分润金额
 * @property string $profit_sharing_rate 分润比例 %
 * @property int $currency_id 分润币种ID（默认USDT）
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 * @property CopySpotExpert $expert 专家关联
 * @property User $followerUser 跟单者用户关联
 * @property CopySpotOrder $copyOrder 跟单记录关联
 */
final class CopySpotProfitSharing extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 专家ID
     */
    public const FIELD_EXPERT_ID = 'expert_id';
    /**
     * 专家用户ID（冗余字段）
     */
    public const FIELD_EXPERT_USER_ID = 'expert_user_id';
    /**
     * 跟单者用户ID
     */
    public const FIELD_FOLLOWER_USER_ID = 'follower_user_id';
    /**
     * 跟单记录ID
     */
    public const FIELD_COPY_ORDER_ID = 'copy_order_id';
    /**
     * 专家订单ID（关联trade_spot_order）
     */
    public const FIELD_EXPERT_ORDER_ID = 'expert_order_id';
    /**
     * 跟单者订单ID（关联trade_spot_order）
     */
    public const FIELD_FOLLOWER_ORDER_ID = 'follower_order_id';
    /**
     * 盈亏金额
     */
    public const FIELD_PROFIT = 'profit';
    /**
     * 分润金额
     */
    public const FIELD_PROFIT_SHARING = 'profit_sharing';
    /**
     * 分润比例 %
     */
    public const FIELD_PROFIT_SHARING_RATE = 'profit_sharing_rate';
    /**
     * 分润币种ID（默认USDT）
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_spot_profit_sharing';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'expert_id', // 专家ID
        'expert_user_id', // 专家用户ID（冗余字段）
        'follower_user_id', // 跟单者用户ID
        'copy_order_id', // 跟单记录ID
        'expert_order_id', // 专家订单ID（关联trade_spot_order）
        'follower_order_id', // 跟单者订单ID（关联trade_spot_order）
        'profit', // 盈亏金额
        'profit_sharing', // 分润金额
        'profit_sharing_rate', // 分润比例 %
        'currency_id', // 分润币种ID（默认USDT）
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'expert_id' => 'integer', // 专家ID
        'expert_user_id' => 'integer', // 专家用户ID（冗余字段）
        'follower_user_id' => 'integer', // 跟单者用户ID
        'copy_order_id' => 'integer', // 跟单记录ID
        'expert_order_id' => 'integer', // 专家订单ID（关联trade_spot_order）
        'follower_order_id' => 'integer', // 跟单者订单ID（关联trade_spot_order）
        'profit' => 'decimal:8', // 盈亏金额
        'profit_sharing' => 'decimal:8', // 分润金额
        'profit_sharing_rate' => 'decimal:2', // 分润比例 %
        'currency_id' => 'integer', // 分润币种ID（默认USDT）
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 专家关联
     */
    public function expert(): BelongsTo
    {
        return $this->belongsTo(CopySpotExpert::class, 'expert_id', 'id');
    }

    /**
     * 专家用户关联
     */
    public function expertUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'expert_user_id', 'id');
    }

    /**
     * 跟单者用户关联
     */
    public function followerUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'follower_user_id', 'id');
    }

    /**
     * 跟单记录关联
     */
    public function copyOrder(): BelongsTo
    {
        return $this->belongsTo(CopySpotOrder::class, 'copy_order_id', 'id');
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取专家ID
     */
    public function getExpertId(): int
    {
        return $this->expert_id;
    }

    /**
     * 设置专家ID
     */
    public function setExpertId(int $value): static
    {
        $this->expert_id = $value;
        return $this;
    }

    /**
     * 获取专家用户ID（冗余字段）
     */
    public function getExpertUserId(): int
    {
        return $this->expert_user_id;
    }

    /**
     * 设置专家用户ID（冗余字段）
     */
    public function setExpertUserId(int $value): static
    {
        $this->expert_user_id = $value;
        return $this;
    }

    /**
     * 获取跟单者用户ID
     */
    public function getFollowerUserId(): int
    {
        return $this->follower_user_id;
    }

    /**
     * 设置跟单者用户ID
     */
    public function setFollowerUserId(int $value): static
    {
        $this->follower_user_id = $value;
        return $this;
    }

    /**
     * 获取跟单记录ID
     */
    public function getCopyOrderId(): int
    {
        return $this->copy_order_id;
    }

    /**
     * 设置跟单记录ID
     */
    public function setCopyOrderId(int $value): static
    {
        $this->copy_order_id = $value;
        return $this;
    }

    /**
     * 获取专家订单ID（关联trade_spot_order）
     */
    public function getExpertOrderId(): int
    {
        return $this->expert_order_id;
    }

    /**
     * 设置专家订单ID（关联trade_spot_order）
     */
    public function setExpertOrderId(int $value): static
    {
        $this->expert_order_id = $value;
        return $this;
    }

    /**
     * 获取跟单者订单ID（关联trade_spot_order）
     */
    public function getFollowerOrderId(): int
    {
        return $this->follower_order_id;
    }

    /**
     * 设置跟单者订单ID（关联trade_spot_order）
     */
    public function setFollowerOrderId(int $value): static
    {
        $this->follower_order_id = $value;
        return $this;
    }

    /**
     * 获取盈亏金额
     */
    public function getProfit(): string
    {
        return $this->profit;
    }

    /**
     * 设置盈亏金额
     */
    public function setProfit(string $value): static
    {
        $this->profit = $value;
        return $this;
    }

    /**
     * 获取分润金额
     */
    public function getProfitSharing(): string
    {
        return $this->profit_sharing;
    }

    /**
     * 设置分润金额
     */
    public function setProfitSharing(string $value): static
    {
        $this->profit_sharing = $value;
        return $this;
    }

    /**
     * 获取分润比例 %
     */
    public function getProfitSharingRate(): string
    {
        return $this->profit_sharing_rate;
    }

    /**
     * 设置分润比例 %
     */
    public function setProfitSharingRate(string $value): static
    {
        $this->profit_sharing_rate = $value;
        return $this;
    }

    /**
     * 获取分润币种ID（默认USDT）
     */
    public function getCurrencyId(): int
    {
        return $this->currency_id;
    }

    /**
     * 设置分润币种ID（默认USDT）
     */
    public function setCurrencyId(int $value): static
    {
        $this->currency_id = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }
}
