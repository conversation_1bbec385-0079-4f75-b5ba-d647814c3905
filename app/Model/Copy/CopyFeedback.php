<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 问题反馈模型
 */

namespace App\Model\Copy;

use App\Model\Copy\Enums\ExpertType;
use App\Model\Copy\Enums\FeedbackType;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\MorphTo;

/**
 * @property int $id 主键ID
 * @property int $expert_id 专家ID
 * @property string $expert_type 专家模型类名（多态关联）
 * @property int $expert_user_id 专家用户ID（冗余字段）
 * @property ExpertType $type 专家类型：1-合约，2-现货
 * @property FeedbackType $feedback_type 反馈类型：1-问题反馈，2-身份撤销
 * @property string $problem_type 问题类型
 * @property string $content 反馈内容
 * @property int|null $refund_account_type 资金退回账户类型
 * @property string|null $refund_amount 退回金额（仅身份撤销）
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 * @property CopyContractExpert|CopySpotExpert $expert 专家关联（多态）
 */
final class CopyFeedback extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 专家ID
     */
    public const FIELD_EXPERT_ID = 'expert_id';
    /**
     * 专家模型类名（多态关联）
     */
    public const FIELD_EXPERT_TYPE = 'expert_type';
    /**
     * 专家用户ID（冗余字段）
     */
    public const FIELD_EXPERT_USER_ID = 'expert_user_id';
    /**
     * 专家类型：1-合约，2-现货
     */
    public const FIELD_TYPE = 'type';
    /**
     * 反馈类型：1-问题反馈，2-身份撤销
     */
    public const FIELD_FEEDBACK_TYPE = 'feedback_type';
    /**
     * 问题类型
     */
    public const FIELD_PROBLEM_TYPE = 'problem_type';
    /**
     * 反馈内容
     */
    public const FIELD_CONTENT = 'content';
    /**
     * 资金退回账户类型
     */
    public const FIELD_REFUND_ACCOUNT_TYPE = 'refund_account_type';
    /**
     * 退回金额（仅身份撤销）
     */
    public const FIELD_REFUND_AMOUNT = 'refund_amount';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_feedback';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'expert_id', // 专家ID
        'expert_type', // 专家模型类名（多态关联）
        'expert_user_id', // 专家用户ID（冗余字段）
        'type', // 专家类型：1-合约，2-现货
        'feedback_type', // 反馈类型：1-问题反馈，2-身份撤销
        'problem_type', // 问题类型
        'content', // 反馈内容
        'refund_account_type', // 资金退回账户类型
        'refund_amount', // 退回金额（仅身份撤销）
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'expert_id' => 'integer', // 专家ID
        'expert_user_id' => 'integer', // 专家用户ID（冗余字段）
        'type' => ExpertType::class, // 专家类型：1-合约，2-现货
        'feedback_type' => FeedbackType::class, // 反馈类型：1-问题反馈，2-身份撤销
        'refund_account_type' => 'integer', // 资金退回账户类型
        'refund_amount' => 'decimal:8', // 退回金额（仅身份撤销）
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 专家关联（多态）
     */
    public function expert(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取专家ID
     */
    public function getExpertId(): int
    {
        return $this->expert_id;
    }

    /**
     * 设置专家ID
     */
    public function setExpertId(int $value): static
    {
        $this->expert_id = $value;
        return $this;
    }

    /**
     * 获取专家模型类名（多态关联）
     */
    public function getExpertType(): string
    {
        return $this->expert_type;
    }

    /**
     * 设置专家模型类名（多态关联）
     */
    public function setExpertType(string $value): static
    {
        $this->expert_type = $value;
        return $this;
    }

    /**
     * 获取专家用户ID（冗余字段）
     */
    public function getExpertUserId(): int
    {
        return $this->expert_user_id;
    }

    /**
     * 设置专家用户ID（冗余字段）
     */
    public function setExpertUserId(int $value): static
    {
        $this->expert_user_id = $value;
        return $this;
    }

    /**
     * 获取专家类型
     */
    public function getType(): ExpertType
    {
        return $this->type;
    }

    /**
     * 设置专家类型
     */
    public function setType(ExpertType $value): static
    {
        $this->type = $value;
        return $this;
    }

    /**
     * 获取反馈类型
     */
    public function getFeedbackType(): FeedbackType
    {
        return $this->feedback_type;
    }

    /**
     * 设置反馈类型
     */
    public function setFeedbackType(FeedbackType $value): static
    {
        $this->feedback_type = $value;
        return $this;
    }

    /**
     * 获取问题类型
     */
    public function getProblemType(): string
    {
        return $this->problem_type;
    }

    /**
     * 设置问题类型
     */
    public function setProblemType(string $value): static
    {
        $this->problem_type = $value;
        return $this;
    }

    /**
     * 获取反馈内容
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * 设置反馈内容
     */
    public function setContent(string $value): static
    {
        $this->content = $value;
        return $this;
    }

    /**
     * 获取资金退回账户类型
     */
    public function getRefundAccountType(): ?int
    {
        return $this->refund_account_type;
    }

    /**
     * 设置资金退回账户类型
     */
    public function setRefundAccountType(?int $value): static
    {
        $this->refund_account_type = $value;
        return $this;
    }

    /**
     * 获取退回金额（仅身份撤销）
     */
    public function getRefundAmount(): ?string
    {
        return $this->refund_amount;
    }

    /**
     * 设置退回金额（仅身份撤销）
     */
    public function setRefundAmount(?string $value): static
    {
        $this->refund_amount = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }
}
