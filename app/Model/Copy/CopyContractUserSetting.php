<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 合约用户跟单配置模型
 */

namespace App\Model\Copy;

use App\Model\Copy\Enums\CopyMode;
use App\Model\Copy\Enums\CopyStatus;
use App\Model\Copy\Enums\CopyType;
use App\Model\Copy\Enums\LeverageMode;
use App\Model\Copy\Enums\MarginMode;
use App\Model\User\User;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;

/**
 * @property int $id 主键ID
 * @property int $follower_user_id 跟单者用户ID
 * @property int $expert_id 专家ID
 * @property int $expert_user_id 专家用户ID（冗余字段）
 * @property CopyMode $mode 跟单模式：1-智能比例，2-多元探索
 * @property string|null $investment_amount 投资金额（USDT）（智能比例模式）
 * @property CopyType|null $copy_type 跟单方式：1-固定额度，2-倍率（多元探索模式）
 * @property string|null $fixed_amount 固定额度（USDT）（多元探索模式）
 * @property string|null $rate 倍率 %（多元探索模式）
 * @property string|null $stop_loss_rate 止损比例 %
 * @property string|null $take_profit_rate 止盈比例 %
 * @property string|null $max_follow_amount 最大跟随金额
 * @property string|null $slippage_rate 滑点比例 %
 * @property bool $auto_new_pairs 自动跟随新币对
 * @property bool $is_exclusive 是否尊享模式
 * @property array|null $copy_currencies 跟单币种配置（支持多币种）
 * @property bool $net_value_guardian 净值守护者（智能比例模式）
 * @property string|null $max_loss_amount 最大亏损金额（触发则解除跟单）（智能比例模式）
 * @property string|null $min_net_value 最小净值金额（触发则解除跟单）（智能比例模式）
 * @property bool $copy_all_positions 跟单后是否复制全部仓位（智能比例模式）
 * @property MarginMode $margin_mode 保证金模式：1-跟随专家，2-全仓，3-逐仓
 * @property LeverageMode $leverage_mode 杠杆设置：1-跟随专家，2-指定杠杆
 * @property int|null $custom_leverage 自定义杠杆倍数（仅当leverage_mode=2时有效）
 * @property CopyStatus $status 状态：1-跟单中，2-暂停
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 * @property User $followerUser 跟单者用户关联
 * @property CopyContractExpert $expert 专家关联
 */
final class CopyContractUserSetting extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 跟单者用户ID
     */
    public const FIELD_FOLLOWER_USER_ID = 'follower_user_id';
    /**
     * 专家ID
     */
    public const FIELD_EXPERT_ID = 'expert_id';
    /**
     * 专家用户ID（冗余字段）
     */
    public const FIELD_EXPERT_USER_ID = 'expert_user_id';
    /**
     * 跟单模式：1-智能比例，2-多元探索
     */
    public const FIELD_MODE = 'mode';
    /**
     * 投资金额（USDT）（智能比例模式）
     */
    public const FIELD_INVESTMENT_AMOUNT = 'investment_amount';
    /**
     * 跟单方式：1-固定额度，2-倍率（多元探索模式）
     */
    public const FIELD_COPY_TYPE = 'copy_type';
    /**
     * 固定额度（USDT）（多元探索模式）
     */
    public const FIELD_FIXED_AMOUNT = 'fixed_amount';
    /**
     * 倍率 %（多元探索模式）
     */
    public const FIELD_RATE = 'rate';
    /**
     * 止损比例 %
     */
    public const FIELD_STOP_LOSS_RATE = 'stop_loss_rate';
    /**
     * 止盈比例 %
     */
    public const FIELD_TAKE_PROFIT_RATE = 'take_profit_rate';
    /**
     * 最大跟随金额
     */
    public const FIELD_MAX_FOLLOW_AMOUNT = 'max_follow_amount';
    /**
     * 滑点比例 %
     */
    public const FIELD_SLIPPAGE_RATE = 'slippage_rate';
    /**
     * 自动跟随新币对
     */
    public const FIELD_AUTO_NEW_PAIRS = 'auto_new_pairs';
    /**
     * 是否尊享模式
     */
    public const FIELD_IS_EXCLUSIVE = 'is_exclusive';
    /**
     * 跟单币种配置（支持多币种）
     */
    public const FIELD_COPY_CURRENCIES = 'copy_currencies';
    /**
     * 净值守护者（智能比例模式）
     */
    public const FIELD_NET_VALUE_GUARDIAN = 'net_value_guardian';
    /**
     * 最大亏损金额（触发则解除跟单）（智能比例模式）
     */
    public const FIELD_MAX_LOSS_AMOUNT = 'max_loss_amount';
    /**
     * 最小净值金额（触发则解除跟单）（智能比例模式）
     */
    public const FIELD_MIN_NET_VALUE = 'min_net_value';
    /**
     * 跟单后是否复制全部仓位（智能比例模式）
     */
    public const FIELD_COPY_ALL_POSITIONS = 'copy_all_positions';
    /**
     * 保证金模式：1-跟随专家，2-全仓，3-逐仓
     */
    public const FIELD_MARGIN_MODE = 'margin_mode';
    /**
     * 杠杆设置：1-跟随专家，2-指定杠杆
     */
    public const FIELD_LEVERAGE_MODE = 'leverage_mode';
    /**
     * 自定义杠杆倍数（仅当leverage_mode=2时有效）
     */
    public const FIELD_CUSTOM_LEVERAGE = 'custom_leverage';
    /**
     * 状态：1-跟单中，2-暂停
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_contract_user_setting';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'follower_user_id', // 跟单者用户ID
        'expert_id', // 专家ID
        'expert_user_id', // 专家用户ID（冗余字段）
        'mode', // 跟单模式：1-智能比例，2-多元探索
        'investment_amount', // 投资金额（USDT）（智能比例模式）
        'copy_type', // 跟单方式：1-固定额度，2-倍率（多元探索模式）
        'fixed_amount', // 固定额度（USDT）（多元探索模式）
        'rate', // 倍率 %（多元探索模式）
        'stop_loss_rate', // 止损比例 %
        'take_profit_rate', // 止盈比例 %
        'max_follow_amount', // 最大跟随金额
        'slippage_rate', // 滑点比例 %
        'auto_new_pairs', // 自动跟随新币对
        'is_exclusive', // 是否尊享模式
        'copy_currencies', // 跟单币种配置（支持多币种）
        'net_value_guardian', // 净值守护者（智能比例模式）
        'max_loss_amount', // 最大亏损金额（触发则解除跟单）（智能比例模式）
        'min_net_value', // 最小净值金额（触发则解除跟单）（智能比例模式）
        'copy_all_positions', // 跟单后是否复制全部仓位（智能比例模式）
        'margin_mode', // 保证金模式：1-跟随专家，2-全仓，3-逐仓
        'leverage_mode', // 杠杆设置：1-跟随专家，2-指定杠杆
        'custom_leverage', // 自定义杠杆倍数（仅当leverage_mode=2时有效）
        'status', // 状态：1-跟单中，2-暂停
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'follower_user_id' => 'integer', // 跟单者用户ID
        'expert_id' => 'integer', // 专家ID
        'expert_user_id' => 'integer', // 专家用户ID（冗余字段）
        'mode' => CopyMode::class, // 跟单模式：1-智能比例，2-多元探索
        'investment_amount' => 'decimal:8', // 投资金额（USDT）（智能比例模式）
        'copy_type' => CopyType::class, // 跟单方式：1-固定额度，2-倍率（多元探索模式）
        'fixed_amount' => 'decimal:8', // 固定额度（USDT）（多元探索模式）
        'rate' => 'decimal:2', // 倍率 %（多元探索模式）
        'stop_loss_rate' => 'decimal:2', // 止损比例 %
        'take_profit_rate' => 'decimal:2', // 止盈比例 %
        'max_follow_amount' => 'decimal:8', // 最大跟随金额
        'slippage_rate' => 'decimal:2', // 滑点比例 %
        'auto_new_pairs' => 'boolean', // 自动跟随新币对
        'is_exclusive' => 'boolean', // 是否尊享模式
        'copy_currencies' => 'array', // 跟单币种配置（支持多币种）
        'net_value_guardian' => 'boolean', // 净值守护者（智能比例模式）
        'max_loss_amount' => 'decimal:8', // 最大亏损金额（触发则解除跟单）（智能比例模式）
        'min_net_value' => 'decimal:8', // 最小净值金额（触发则解除跟单）（智能比例模式）
        'copy_all_positions' => 'boolean', // 跟单后是否复制全部仓位（智能比例模式）
        'margin_mode' => MarginMode::class, // 保证金模式：1-跟随专家，2-全仓，3-逐仓
        'leverage_mode' => LeverageMode::class, // 杠杆设置：1-跟随专家，2-指定杠杆
        'custom_leverage' => 'integer', // 自定义杠杆倍数（仅当leverage_mode=2时有效）
        'status' => CopyStatus::class, // 状态：1-跟单中，2-暂停
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 跟单者用户关联
     */
    public function followerUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'follower_user_id', 'id');
    }

    /**
     * 专家关联
     */
    public function expert(): BelongsTo
    {
        return $this->belongsTo(CopyContractExpert::class, 'expert_id', 'id');
    }

    /**
     * 专家用户关联
     */
    public function expertUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'expert_user_id', 'id');
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取跟单者用户ID
     */
    public function getFollowerUserId(): int
    {
        return $this->follower_user_id;
    }

    /**
     * 设置跟单者用户ID
     */
    public function setFollowerUserId(int $value): static
    {
        $this->follower_user_id = $value;
        return $this;
    }

    /**
     * 获取专家ID
     */
    public function getExpertId(): int
    {
        return $this->expert_id;
    }

    /**
     * 设置专家ID
     */
    public function setExpertId(int $value): static
    {
        $this->expert_id = $value;
        return $this;
    }

    /**
     * 获取专家用户ID（冗余字段）
     */
    public function getExpertUserId(): int
    {
        return $this->expert_user_id;
    }

    /**
     * 设置专家用户ID（冗余字段）
     */
    public function setExpertUserId(int $value): static
    {
        $this->expert_user_id = $value;
        return $this;
    }

    /**
     * 获取跟单模式
     */
    public function getMode(): CopyMode
    {
        return $this->mode;
    }

    /**
     * 设置跟单模式
     */
    public function setMode(CopyMode $value): static
    {
        $this->mode = $value;
        return $this;
    }

    /**
     * 获取投资金额（USDT）（智能比例模式）
     */
    public function getInvestmentAmount(): ?string
    {
        return $this->investment_amount;
    }

    /**
     * 设置投资金额（USDT）（智能比例模式）
     */
    public function setInvestmentAmount(?string $value): static
    {
        $this->investment_amount = $value;
        return $this;
    }

    /**
     * 获取跟单方式（多元探索模式）
     */
    public function getCopyType(): ?CopyType
    {
        return $this->copy_type;
    }

    /**
     * 设置跟单方式（多元探索模式）
     */
    public function setCopyType(?CopyType $value): static
    {
        $this->copy_type = $value;
        return $this;
    }

    /**
     * 获取固定额度（USDT）（多元探索模式）
     */
    public function getFixedAmount(): ?string
    {
        return $this->fixed_amount;
    }

    /**
     * 设置固定额度（USDT）（多元探索模式）
     */
    public function setFixedAmount(?string $value): static
    {
        $this->fixed_amount = $value;
        return $this;
    }

    /**
     * 获取倍率 %（多元探索模式）
     */
    public function getRate(): ?string
    {
        return $this->rate;
    }

    /**
     * 设置倍率 %（多元探索模式）
     */
    public function setRate(?string $value): static
    {
        $this->rate = $value;
        return $this;
    }

    /**
     * 获取止损比例 %
     */
    public function getStopLossRate(): ?string
    {
        return $this->stop_loss_rate;
    }

    /**
     * 设置止损比例 %
     */
    public function setStopLossRate(?string $value): static
    {
        $this->stop_loss_rate = $value;
        return $this;
    }

    /**
     * 获取止盈比例 %
     */
    public function getTakeProfitRate(): ?string
    {
        return $this->take_profit_rate;
    }

    /**
     * 设置止盈比例 %
     */
    public function setTakeProfitRate(?string $value): static
    {
        $this->take_profit_rate = $value;
        return $this;
    }

    /**
     * 获取最大跟随金额
     */
    public function getMaxFollowAmount(): ?string
    {
        return $this->max_follow_amount;
    }

    /**
     * 设置最大跟随金额
     */
    public function setMaxFollowAmount(?string $value): static
    {
        $this->max_follow_amount = $value;
        return $this;
    }

    /**
     * 获取滑点比例 %
     */
    public function getSlippageRate(): ?string
    {
        return $this->slippage_rate;
    }

    /**
     * 设置滑点比例 %
     */
    public function setSlippageRate(?string $value): static
    {
        $this->slippage_rate = $value;
        return $this;
    }

    /**
     * 获取自动跟随新币对
     */
    public function getAutoNewPairs(): bool
    {
        return $this->auto_new_pairs;
    }

    /**
     * 设置自动跟随新币对
     */
    public function setAutoNewPairs(bool $value): static
    {
        $this->auto_new_pairs = $value;
        return $this;
    }

    /**
     * 获取是否尊享模式
     */
    public function getIsExclusive(): bool
    {
        return $this->is_exclusive;
    }

    /**
     * 设置是否尊享模式
     */
    public function setIsExclusive(bool $value): static
    {
        $this->is_exclusive = $value;
        return $this;
    }

    /**
     * 获取跟单币种配置（支持多币种）
     */
    public function getCopyCurrencies(): ?array
    {
        return $this->copy_currencies;
    }

    /**
     * 设置跟单币种配置（支持多币种）
     */
    public function setCopyCurrencies(?array $value): static
    {
        $this->copy_currencies = $value;
        return $this;
    }

    /**
     * 获取状态
     */
    public function getStatus(): CopyStatus
    {
        return $this->status;
    }

    /**
     * 设置状态
     */
    public function setStatus(CopyStatus $value): static
    {
        $this->status = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }
}
