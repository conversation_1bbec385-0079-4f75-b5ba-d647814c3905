<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 跟单模式枚举
 */

namespace App\Model\Copy\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum CopyMode: int
{
    use EnumConstantsTrait;

    /**
     * 智能比例跟单
     */
    #[Message('copy.copy_mode.smart_rate')]
    case SMART_RATE = 1;

    /**
     * 多元探索跟单
     */
    #[Message('copy.copy_mode.multi_explore')]
    case MULTI_EXPLORE = 2;
}
