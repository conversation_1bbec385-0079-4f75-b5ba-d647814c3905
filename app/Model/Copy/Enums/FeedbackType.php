<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 反馈类型枚举
 */

namespace App\Model\Copy\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum FeedbackType: int
{
    use EnumConstantsTrait;

    /**
     * 问题反馈
     */
    #[Message('copy.feedback_type.problem')]
    case PROBLEM = 1;

    /**
     * 身份撤销
     */
    #[Message('copy.feedback_type.revoke')]
    case REVOKE = 2;
}
