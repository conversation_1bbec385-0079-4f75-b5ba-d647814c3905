<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 保证金模式枚举
 */

namespace App\Model\Copy\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum MarginMode: int
{
    use EnumConstantsTrait;

    /**
     * 跟随专家
     */
    #[Message('copy.margin_mode.follow_expert')]
    case FOLLOW_EXPERT = 1;

    /**
     * 全仓模式
     */
    #[Message('copy.margin_mode.cross')]
    case CROSS = 2;

    /**
     * 逐仓模式
     */
    #[Message('copy.margin_mode.isolated')]
    case ISOLATED = 3;
}
