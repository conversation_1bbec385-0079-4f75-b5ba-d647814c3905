<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 跟单方式枚举
 */

namespace App\Model\Copy\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum CopyType: int
{
    use EnumConstantsTrait;

    /**
     * 固定额度
     */
    #[Message('copy.copy_type.fixed_amount')]
    case FIXED_AMOUNT = 1;

    /**
     * 跟单倍率
     */
    #[Message('copy.copy_type.rate')]
    case RATE = 2;
}
