<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆模式枚举
 */

namespace App\Model\Copy\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum LeverageMode: int
{
    use EnumConstantsTrait;

    /**
     * 跟随专家
     */
    #[Message('copy.leverage_mode.follow_expert')]
    case FOLLOW_EXPERT = 1;

    /**
     * 自定义杠杆
     */
    #[Message('copy.leverage_mode.custom')]
    case CUSTOM = 2;
}
