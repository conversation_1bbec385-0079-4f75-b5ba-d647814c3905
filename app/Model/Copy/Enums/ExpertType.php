<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 交易专家类型枚举
 */

namespace App\Model\Copy\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ExpertType: int
{
    use EnumConstantsTrait;

    /**
     * 合约交易专家
     */
    #[Message('copy.expert_type.contract')]
    case CONTRACT = 1;

    /**
     * 现货交易专家
     */
    #[Message('copy.expert_type.spot')]
    case SPOT = 2;
}
