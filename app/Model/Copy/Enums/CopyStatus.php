<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 跟单状态枚举
 */

namespace App\Model\Copy\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum CopyStatus: int
{
    use EnumConstantsTrait;

    /**
     * 跟单中
     */
    #[Message('copy.copy_status.following')]
    case FOLLOWING = 1;

    /**
     * 暂停
     */
    #[Message('copy.copy_status.paused')]
    case PAUSED = 2;
}
