<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 交易专家申请状态枚举
 */

namespace App\Model\Copy\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ExpertStatus: int
{
    use EnumConstantsTrait;

    /**
     * 待审核
     */
    #[Message('copy.expert_status.pending')]
    case PENDING = 1;

    /**
     * 审核通过
     */
    #[Message('copy.expert_status.approved')]
    case APPROVED = 2;

    /**
     * 审核拒绝
     */
    #[Message('copy.expert_status.rejected')]
    case REJECTED = 3;
}
