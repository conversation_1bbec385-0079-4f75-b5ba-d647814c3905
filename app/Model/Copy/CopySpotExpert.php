<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 现货交易专家模型
 */

namespace App\Model\Copy;

use App\Model\Copy\Enums\ExpertStatus;
use App\Model\User\User;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\Database\Model\Relations\HasMany;
use Hyperf\Database\Model\Relations\MorphMany;

/**
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property string $introduction 个人介绍
 * @property ExpertStatus $status 申请状态
 * @property string|null $review_remark 审核备注
 * @property Carbon|null $reviewed_at 审核时间
 * @property int|null $reviewed_by 审核人ID
 * @property bool $is_active 是否开启现货带单
 * @property bool $show_total_assets 是否展示总资产
 * @property bool $show_fund_composition 是否展示资金构成
 * @property bool $new_currency_auto_copy 新上线的交易对自动开启带单
 * @property bool $position_protection 未结仓位保护
 * @property string|null $min_follow_amount 最小跟单金额（USDT）
 * @property array|null $recommend_params 推荐参数配置
 * @property array|null $currency_ids 跟单币种 id 配置
 * @property string $profit_sharing_rate 分润比例 %
 * @property int $level_id 专家等级ID
 * @property bool $exclusive_mode 尊享模式
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 * @property User $user 用户关联
 * @property CopyExpertLevel $level 等级关联
 */
final class CopySpotExpert extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 个人介绍
     */
    public const FIELD_INTRODUCTION = 'introduction';
    /**
     * 申请状态
     */
    public const FIELD_STATUS = 'status';
    /**
     * 审核备注
     */
    public const FIELD_REVIEW_REMARK = 'review_remark';
    /**
     * 审核时间
     */
    public const FIELD_REVIEWED_AT = 'reviewed_at';
    /**
     * 审核人ID
     */
    public const FIELD_REVIEWED_BY = 'reviewed_by';
    /**
     * 是否开启现货带单
     */
    public const FIELD_IS_ACTIVE = 'is_active';
    /**
     * 是否展示总资产
     */
    public const FIELD_SHOW_TOTAL_ASSETS = 'show_total_assets';
    /**
     * 是否展示资金构成
     */
    public const FIELD_SHOW_FUND_COMPOSITION = 'show_fund_composition';
    /**
     * 新上线的交易对自动开启带单
     */
    public const FIELD_NEW_CURRENCY_AUTO_COPY = 'new_currency_auto_copy';
    /**
     * 未结仓位保护
     */
    public const FIELD_POSITION_PROTECTION = 'position_protection';
    /**
     * 最小跟单金额（USDT）
     */
    public const FIELD_MIN_FOLLOW_AMOUNT = 'min_follow_amount';
    /**
     * 推荐参数配置
     */
    public const FIELD_RECOMMEND_PARAMS = 'recommend_params';
    /**
     * 跟单币种 id 配置
     */
    public const FIELD_CURRENCY_IDS = 'currency_ids';
    /**
     * 分润比例 %
     */
    public const FIELD_PROFIT_SHARING_RATE = 'profit_sharing_rate';
    /**
     * 专家等级ID
     */
    public const FIELD_LEVEL_ID = 'level_id';
    /**
     * 尊享模式
     */
    public const FIELD_EXCLUSIVE_MODE = 'exclusive_mode';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_spot_expert';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'user_id', // 用户ID
        'introduction', // 个人介绍
        'status', // 申请状态
        'review_remark', // 审核备注
        'reviewed_at', // 审核时间
        'reviewed_by', // 审核人ID
        'is_active', // 是否开启现货带单
        'show_total_assets', // 是否展示总资产
        'show_fund_composition', // 是否展示资金构成
        'new_currency_auto_copy', // 新上线的交易对自动开启带单
        'position_protection', // 未结仓位保护
        'min_follow_amount', // 最小跟单金额（USDT）
        'recommend_params', // 推荐参数配置
        'currency_ids', // 跟单币种 id 配置
        'profit_sharing_rate', // 分润比例 %
        'level_id', // 专家等级ID
        'exclusive_mode', // 尊享模式
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'user_id' => 'integer', // 用户ID
        'status' => ExpertStatus::class, // 申请状态
        'reviewed_at' => 'datetime', // 审核时间
        'reviewed_by' => 'integer', // 审核人ID
        'is_active' => 'boolean', // 是否开启现货带单
        'show_total_assets' => 'boolean', // 是否展示总资产
        'show_fund_composition' => 'boolean', // 是否展示资金构成
        'new_currency_auto_copy' => 'boolean', // 新上线的交易对自动开启带单
        'position_protection' => 'boolean', // 未结仓位保护
        'min_follow_amount' => 'decimal:8', // 最小跟单金额（USDT）
        'recommend_params' => 'array', // 推荐参数配置
        'currency_ids' => 'array', // 跟单币种 id 配置
        'profit_sharing_rate' => 'decimal:2', // 分润比例 %
        'level_id' => 'integer', // 专家等级ID
        'exclusive_mode' => 'boolean', // 尊享模式
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 用户关联
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 等级关联
     */
    public function level(): BelongsTo
    {
        return $this->belongsTo(CopyExpertLevel::class, 'level_id', 'id');
    }

    /**
     * 关注关联
     */
    public function follows(): MorphMany
    {
        return $this->morphMany(CopyFollow::class, 'expert');
    }

    /**
     * 反馈关联
     */
    public function feedbacks(): MorphMany
    {
        return $this->morphMany(CopyFeedback::class, 'expert');
    }

    /**
     * 跟单记录关联
     */
    public function spotOrders(): HasMany
    {
        return $this->hasMany(CopySpotOrder::class, 'expert_id', 'id');
    }

    /**
     * 用户跟单配置关联
     */
    public function userSettings(): HasMany
    {
        return $this->hasMany(CopySpotUserSetting::class, 'expert_id', 'id');
    }

    /**
     * 尊享模式邀请关联
     */
    public function exclusiveInvitations(): MorphMany
    {
        return $this->morphMany(CopyExclusiveInvitation::class, 'expert');
    }

    /**
     * 分润记录关联
     */
    public function profitSharings(): HasMany
    {
        return $this->hasMany(CopySpotProfitSharing::class, 'expert_id', 'id');
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     */
    public function setUserId(int $value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取个人介绍
     */
    public function getIntroduction(): string
    {
        return $this->introduction;
    }

    /**
     * 设置个人介绍
     */
    public function setIntroduction(string $value): static
    {
        $this->introduction = $value;
        return $this;
    }

    /**
     * 获取申请状态
     */
    public function getStatus(): ExpertStatus
    {
        return $this->status;
    }

    /**
     * 设置申请状态
     */
    public function setStatus(ExpertStatus $value): static
    {
        $this->status = $value;
        return $this;
    }

    /**
     * 获取审核备注
     */
    public function getReviewRemark(): ?string
    {
        return $this->review_remark;
    }

    /**
     * 设置审核备注
     */
    public function setReviewRemark(?string $value): static
    {
        $this->review_remark = $value;
        return $this;
    }

    /**
     * 获取审核时间
     */
    public function getReviewedAt(): ?Carbon
    {
        return $this->reviewed_at;
    }

    /**
     * 设置审核时间
     */
    public function setReviewedAt(?Carbon $value): static
    {
        $this->reviewed_at = $value;
        return $this;
    }

    /**
     * 获取审核人ID
     */
    public function getReviewedBy(): ?int
    {
        return $this->reviewed_by;
    }

    /**
     * 设置审核人ID
     */
    public function setReviewedBy(?int $value): static
    {
        $this->reviewed_by = $value;
        return $this;
    }

    /**
     * 获取是否开启现货带单
     */
    public function getIsActive(): bool
    {
        return $this->is_active;
    }

    /**
     * 设置是否开启现货带单
     */
    public function setIsActive(bool $value): static
    {
        $this->is_active = $value;
        return $this;
    }

    /**
     * 获取是否展示总资产
     */
    public function getShowTotalAssets(): bool
    {
        return $this->show_total_assets;
    }

    /**
     * 设置是否展示总资产
     */
    public function setShowTotalAssets(bool $value): static
    {
        $this->show_total_assets = $value;
        return $this;
    }

    /**
     * 获取是否展示资金构成
     */
    public function getShowFundComposition(): bool
    {
        return $this->show_fund_composition;
    }

    /**
     * 设置是否展示资金构成
     */
    public function setShowFundComposition(bool $value): static
    {
        $this->show_fund_composition = $value;
        return $this;
    }

    /**
     * 获取新上线的交易对自动开启带单
     */
    public function getNewCurrencyAutoCopy(): bool
    {
        return $this->new_currency_auto_copy;
    }

    /**
     * 设置新上线的交易对自动开启带单
     */
    public function setNewCurrencyAutoCopy(bool $value): static
    {
        $this->new_currency_auto_copy = $value;
        return $this;
    }

    /**
     * 获取未结仓位保护
     */
    public function getPositionProtection(): bool
    {
        return $this->position_protection;
    }

    /**
     * 设置未结仓位保护
     */
    public function setPositionProtection(bool $value): static
    {
        $this->position_protection = $value;
        return $this;
    }

    /**
     * 获取最小跟单金额（USDT）
     */
    public function getMinFollowAmount(): ?string
    {
        return $this->min_follow_amount;
    }

    /**
     * 设置最小跟单金额（USDT）
     */
    public function setMinFollowAmount(?string $value): static
    {
        $this->min_follow_amount = $value;
        return $this;
    }

    /**
     * 获取推荐参数配置
     */
    public function getRecommendParams(): ?array
    {
        return $this->recommend_params;
    }

    /**
     * 设置推荐参数配置
     */
    public function setRecommendParams(?array $value): static
    {
        $this->recommend_params = $value;
        return $this;
    }

    /**
     * 获取跟单币种 id 配置
     */
    public function getCurrencyIds(): ?array
    {
        return $this->currency_ids;
    }

    /**
     * 设置跟单币种 id 配置
     */
    public function setCurrencyIds(?array $value): static
    {
        $this->currency_ids = $value;
        return $this;
    }

    /**
     * 获取分润比例 %
     */
    public function getProfitSharingRate(): string
    {
        return $this->profit_sharing_rate;
    }

    /**
     * 设置分润比例 %
     */
    public function setProfitSharingRate(string $value): static
    {
        $this->profit_sharing_rate = $value;
        return $this;
    }

    /**
     * 获取专家等级ID
     */
    public function getLevelId(): int
    {
        return $this->level_id;
    }

    /**
     * 设置专家等级ID
     */
    public function setLevelId(int $value): static
    {
        $this->level_id = $value;
        return $this;
    }

    /**
     * 获取尊享模式
     */
    public function getExclusiveMode(): bool
    {
        return $this->exclusive_mode;
    }

    /**
     * 设置尊享模式
     */
    public function setExclusiveMode(bool $value): static
    {
        $this->exclusive_mode = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }
}
