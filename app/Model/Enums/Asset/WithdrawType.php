<?php

declare(strict_types=1);

namespace App\Model\Enums\Asset;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum WithdrawType: int
{
    use EnumConstantsTrait;

    /**
     * 链上提币
     */
    #[Message('asset.enums.withdraw_type.1')]
    case CHAIN = 1;

    /**
     * 内部邮箱转账
     */
    #[Message('asset.enums.withdraw_type.2')]
    case INTERNAL_EMAIL = 2;

    /**
     * 内部手机转账
     */
    #[Message('asset.enums.withdraw_type.3')]
    case INTERNAL_PHONE = 3;

    /**
     * 内部UID转账
     */
    #[Message('asset.enums.withdraw_type.4')]
    case INTERNAL_UID = 4;

    /**
     * 获取类型名称
     */
    public function getName(): string
    {
        return match($this) {
            self::CHAIN => '链上提币',
            self::INTERNAL_EMAIL => '内部邮箱转账',
            self::INTERNAL_PHONE => '内部手机转账',
            self::INTERNAL_UID => '内部UID转账',
        };
    }

    /**
     * 获取英文名称
     */
    public function getEnglishName(): string
    {
        return match($this) {
            self::CHAIN => 'Chain Withdrawal',
            self::INTERNAL_EMAIL => 'Internal Email Transfer',
            self::INTERNAL_PHONE => 'Internal Phone Transfer',
            self::INTERNAL_UID => 'Internal UID Transfer',
        };
    }

    /**
     * 是否为内部转账
     */
    public function isInternal(): bool
    {
        return match($this) {
            self::CHAIN => false,
            self::INTERNAL_EMAIL, self::INTERNAL_PHONE, self::INTERNAL_UID => true,
        };
    }

    /**
     * 获取所有类型的名称映射
     */
    public static function getNameMapping(string $lang = 'zh-CN'): array
    {
        $mapping = [];
        foreach (self::cases() as $case) {
            $mapping[$case->value] = $lang === 'en-US' ? $case->getEnglishName() : $case->getName();
        }
        return $mapping;
    }
}
