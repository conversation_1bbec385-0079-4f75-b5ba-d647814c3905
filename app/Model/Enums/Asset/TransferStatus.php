<?php

declare(strict_types=1);

namespace App\Model\Enums\Asset;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum TransferStatus: int
{
    use EnumConstantsTrait;

    /**
     * 成功
     */
    #[Message('asset.enums.transfer_status.1')]
    case SUCCESS = 1;

    /**
     * 失败
     */
    #[Message('asset.enums.transfer_status.2')]
    case FAILED = 2;

    /**
     * 处理中
     */
    #[Message('asset.enums.transfer_status.3')]
    case PROCESSING = 3;

    /**
     * 获取状态名称
     */
    public function getName(): string
    {
        return match($this) {
            self::SUCCESS => '成功',
            self::FAILED => '失败',
            self::PROCESSING => '处理中',
        };
    }

    /**
     * 获取英文名称
     */
    public function getEnglishName(): string
    {
        return match($this) {
            self::SUCCESS => 'Success',
            self::FAILED => 'Failed',
            self::PROCESSING => 'Processing',
        };
    }

    /**
     * 获取所有状态的名称映射
     */
    public static function getNameMapping(string $lang = 'zh-CN'): array
    {
        $mapping = [];
        foreach (self::cases() as $case) {
            $mapping[$case->value] = $lang === 'en-US' ? $case->getEnglishName() : $case->getName();
        }
        return $mapping;
    }
}
