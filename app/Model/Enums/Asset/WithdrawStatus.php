<?php

declare(strict_types=1);

namespace App\Model\Enums\Asset;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum WithdrawStatus: int
{
    use EnumConstantsTrait;

    /**
     * 待审核
     */
    #[Message('asset.enums.withdraw_status.1')]
    case PENDING = 1;

    /**
     * 处理中
     */
    #[Message('asset.enums.withdraw_status.2')]
    case PROCESSING = 2;

    /**
     * 已完成
     */
    #[Message('asset.enums.withdraw_status.3')]
    case COMPLETED = 3;

    /**
     * 已拒绝
     */
    #[Message('asset.enums.withdraw_status.4')]
    case REJECTED = 4;

    /**
     * 已取消
     */
    #[Message('asset.enums.withdraw_status.5')]
    case CANCELLED = 5;

    /**
     * 获取状态名称
     */
    public function getName(): string
    {
        return match($this) {
            self::PENDING => '待审核',
            self::PROCESSING => '处理中',
            self::COMPLETED => '已完成',
            self::REJECTED => '已拒绝',
            self::CANCELLED => '已取消',
        };
    }

    /**
     * 获取英文名称
     */
    public function getEnglishName(): string
    {
        return match($this) {
            self::PENDING => 'Pending',
            self::PROCESSING => 'Processing',
            self::COMPLETED => 'Completed',
            self::REJECTED => 'Rejected',
            self::CANCELLED => 'Cancelled',
        };
    }

    /**
     * 是否为最终状态
     */
    public function isFinal(): bool
    {
        return match($this) {
            self::COMPLETED, self::REJECTED, self::CANCELLED => true,
            self::PENDING, self::PROCESSING => false,
        };
    }

    /**
     * 是否为成功状态
     */
    public function isSuccess(): bool
    {
        return $this === self::COMPLETED;
    }

    /**
     * 是否为失败状态
     */
    public function isFailed(): bool
    {
        return match($this) {
            self::REJECTED, self::CANCELLED => true,
            default => false,
        };
    }

    /**
     * 获取所有状态的名称映射
     */
    public static function getNameMapping(string $lang = 'zh-CN'): array
    {
        $mapping = [];
        foreach (self::cases() as $case) {
            $mapping[$case->value] = $lang === 'en-US' ? $case->getEnglishName() : $case->getName();
        }
        return $mapping;
    }
}
