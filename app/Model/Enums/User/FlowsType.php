<?php

/**
 * FlowsType.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/4
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Model\Enums\User;

enum FlowsType:int
{
    case RECHARGE = 1; //充值

    case WITHDRAW = 2; //提币

    case TRANSFER = 3; //划转

    case SPOT_TRADE = 4; //现货交易

    case SPOT_TRADE_FEE = 5; //现货交易手续费

    case SPOT_TRADE_REBATE = 6;//现货交易手续费返佣


    case FUTURES_TRADE = 30; //合约交易

    case FUTURES_TRADE_FEE = 31; //合约交易手续费

    case FUTURES_TRADE_REBATE = 32;//合约交易手续费返佣

    case PERPETUAL_MARGIN_FREEZE = 35; //永续合约保证金冻结

    case PERPETUAL_MARGIN_UNFREEZE = 36; //永续合约保证金解冻

    case PERPETUAL_TRADE = 37; //永续合约交易

    case PERPETUAL_TRADE_FEE = 38; //永续合约交易手续费

    case PERPETUAL_FUNDING_FEE = 39; //永续合约资金费用

    case MARGIN_TRADE = 50; //杠杆交易

    case MARGIN_TRADE_FEE = 51; //杠杆交易手续费

    case MARGIN_BORROW = 52; //杠杆借款

    case MARGIN_AUTO_BORROW = 55; //杠杆自动借款

    case MARGIN_REPAY = 53; //杠杆还款

    case MARGIN_INTEREST = 54; //杠杆利息

    case CHAIN_TRADE = 70 ; //链上交易

    case COPY_TRADE = 100 ;//跟单交易

    case INTERNAL_TRANSFER_OUT = 110; //内部转账转出

    case INTERNAL_TRANSFER_IN = 111; //内部转账转入

    case TRANSFER_OUT = 120; //划转转出

    case TRANSFER_IN = 121; //划转转入


}