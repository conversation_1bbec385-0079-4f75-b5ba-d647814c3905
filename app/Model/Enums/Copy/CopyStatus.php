<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 跟单状态枚举
 */

namespace App\Model\Enums\Copy;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum CopyStatus: int
{
    use EnumConstantsTrait;

    /**
     * 失败
     */
    #[Message('copy.enums.status.0')]
    case FAILED = 0;

    /**
     * 成功
     */
    #[Message('copy.enums.status.1')]
    case SUCCESS = 1;
} 