<?php

declare(strict_types=1);

namespace App\Model\User;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $user_id 用户ID
 * @property string $chain_id 链ID
 * @property int $currency_id 币种ID(可为空)
 * @property string $address 充值地址
 * @property int $address_type 地址类型:1=普通地址
 * @property string $public_key 公钥
 * @property string $private_key_encrypted 加密私钥
 * @property string $memo 备注标签
 * @property int $status 状态:1=正常,2=禁用
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class UserDepositAddress extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 链ID
     */
    public const FIELD_CHAIN_ID = 'chain_id';
    /**
     * 币种ID(可为空)
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 充值地址
     */
    public const FIELD_ADDRESS = 'address';
    /**
     * 地址类型:1=普通地址
     */
    public const FIELD_ADDRESS_TYPE = 'address_type';
    /**
     * 公钥
     */
    public const FIELD_PUBLIC_KEY = 'public_key';
    /**
     * 加密私钥
     */
    public const FIELD_PRIVATE_KEY_ENCRYPTED = 'private_key_encrypted';
    /**
     * 备注标签
     */
    public const FIELD_MEMO = 'memo';
    /**
     * 状态:1=正常,2=禁用
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'user_deposit_addresses';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'chain_id', 'currency_id', 'address', 'address_type', 'public_key', 'private_key_encrypted', 'memo', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        // 用户ID
        'user_id' => 'integer',
        // 币种ID(可为空)
        'currency_id' => 'integer',
        // 地址类型:1=普通地址
        'address_type' => 'integer',
        // 状态:1=正常,2=禁用
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getChainId() : string
    {
        return $this->chain_id;
    }
    public function setChainId($value) : object
    {
        $this->chain_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getAddress() : string
    {
        return $this->address;
    }
    public function setAddress($value) : object
    {
        $this->address = $value;
        return $this;
    }
    public function getAddressType() : int
    {
        return $this->address_type;
    }
    public function setAddressType($value) : object
    {
        $this->address_type = $value;
        return $this;
    }
    public function getPublicKey() : string
    {
        return $this->public_key;
    }
    public function setPublicKey($value) : object
    {
        $this->public_key = $value;
        return $this;
    }
    public function getPrivateKeyEncrypted() : string
    {
        return $this->private_key_encrypted;
    }
    public function setPrivateKeyEncrypted($value) : object
    {
        $this->private_key_encrypted = $value;
        return $this;
    }
    public function getMemo() : string
    {
        return $this->memo ?? '';
    }
    public function setMemo($value) : object
    {
        $this->memo = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
