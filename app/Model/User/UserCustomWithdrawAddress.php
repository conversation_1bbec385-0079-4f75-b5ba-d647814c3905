<?php

declare(strict_types=1);

namespace App\Model\User;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $user_id 用户id
 * @property int $currency_id 币种id
 * @property string $chain_id 链id
 * @property string $address 地址
 * @property string $remark 备注
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class UserCustomWithdrawAddress extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 用户id
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 币种id
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 链id
     */
    public const FIELD_CHAIN_ID = 'chain_id';
    /**
     * 地址
     */
    public const FIELD_ADDRESS = 'address';
    /**
     * 备注
     */
    public const FIELD_REMARK = 'remark';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'user_custom_withdraw_address';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'currency_id', 'chain_id', 'address', 'remark', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        // 用户id
        'user_id' => 'integer',
        // 币种id
        'currency_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getChainId() : string
    {
        return $this->chain_id;
    }
    public function setChainId($value) : object
    {
        $this->chain_id = $value;
        return $this;
    }
    public function getAddress() : string
    {
        return $this->address;
    }
    public function setAddress($value) : object
    {
        $this->address = $value;
        return $this;
    }
    public function getRemark() : string
    {
        return $this->remark;
    }
    public function setRemark($value) : object
    {
        $this->remark = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
