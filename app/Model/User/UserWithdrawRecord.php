<?php

declare(strict_types=1);

namespace App\Model\User;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $user_id 用户ID
 * @property int $currency_id 币种ID
 * @property int $withdraw_type 提币类型:1=链上提币,2=内部邮箱,3=内部手机,4=内部UID
 * @property float $amount 提币数量
 * @property float $fee 手续费
 * @property float $actual_amount 实际到账数量
 * @property string $chain_id 提币链ID
 * @property string $to_address 提币地址
 * @property string $memo 备注标签
 * @property string $tx_hash 交易哈希
 * @property int $confirmations 确认数
 * @property int $to_user_id 接收用户ID
 * @property string $to_email 接收邮箱
 * @property string $to_phone 接收手机号
 * @property string $to_phone_country_code 手机国家代码
 * @property string $to_uid 接收用户UID
 * @property int $status 状态:1=待审核,2=处理中,3=已完成,4=已拒绝,5=已取消
 * @property string $remark 备注说明
 * @property string $admin_remark 管理员备注
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 * @property string $completed_at 完成时间
 */
class UserWithdrawRecord extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 提币类型:1=链上提币,2=内部邮箱,3=内部手机,4=内部UID
     */
    public const FIELD_WITHDRAW_TYPE = 'withdraw_type';
    /**
     * 提币数量
     */
    public const FIELD_AMOUNT = 'amount';
    /**
     * 手续费
     */
    public const FIELD_FEE = 'fee';
    /**
     * 实际到账数量
     */
    public const FIELD_ACTUAL_AMOUNT = 'actual_amount';
    /**
     * 提币链ID
     */
    public const FIELD_CHAIN_ID = 'chain_id';
    /**
     * 提币地址
     */
    public const FIELD_TO_ADDRESS = 'to_address';
    /**
     * 备注标签
     */
    public const FIELD_MEMO = 'memo';
    /**
     * 交易哈希
     */
    public const FIELD_TX_HASH = 'tx_hash';
    /**
     * 确认数
     */
    public const FIELD_CONFIRMATIONS = 'confirmations';
    /**
     * 接收用户ID
     */
    public const FIELD_TO_USER_ID = 'to_user_id';
    /**
     * 接收邮箱
     */
    public const FIELD_TO_EMAIL = 'to_email';
    /**
     * 接收手机号
     */
    public const FIELD_TO_PHONE = 'to_phone';
    /**
     * 手机国家代码
     */
    public const FIELD_TO_PHONE_COUNTRY_CODE = 'to_phone_country_code';
    /**
     * 接收用户UID
     */
    public const FIELD_TO_UID = 'to_uid';
    /**
     * 状态:1=待审核,2=处理中,3=已完成,4=已拒绝,5=已取消
     */
    public const FIELD_STATUS = 'status';
    /**
     * 备注说明
     */
    public const FIELD_REMARK = 'remark';
    /**
     * 管理员备注
     */
    public const FIELD_ADMIN_REMARK = 'admin_remark';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * 完成时间
     */
    public const FIELD_COMPLETED_AT = 'completed_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'user_withdraw_records';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'currency_id', 'withdraw_type', 'amount', 'fee', 'actual_amount', 'chain_id', 'to_address', 'memo', 'tx_hash', 'confirmations', 'to_user_id', 'to_email', 'to_phone', 'to_phone_country_code', 'to_uid', 'status', 'remark', 'admin_remark', 'created_at', 'updated_at', 'completed_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        // 用户ID
        'user_id' => 'integer',
        // 币种ID
        'currency_id' => 'integer',
        // 提币类型:1=链上提币,2=内部邮箱,3=内部手机,4=内部UID
        'withdraw_type' => 'integer',
        // 提币数量
        'amount' => 'float',
        // 手续费
        'fee' => 'float',
        // 实际到账数量
        'actual_amount' => 'float',
        // 确认数
        'confirmations' => 'integer',
        // 接收用户ID
        'to_user_id' => 'integer',
        // 状态:1=待审核,2=处理中,3=已完成,4=已拒绝,5=已取消
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getWithdrawType() : int
    {
        return $this->withdraw_type;
    }
    public function setWithdrawType($value) : object
    {
        $this->withdraw_type = $value;
        return $this;
    }
    public function getAmount() : float
    {
        return $this->amount;
    }
    public function setAmount($value) : object
    {
        $this->amount = $value;
        return $this;
    }
    public function getFee() : float
    {
        return $this->fee;
    }
    public function setFee($value) : object
    {
        $this->fee = $value;
        return $this;
    }
    public function getActualAmount() : float
    {
        return $this->actual_amount;
    }
    public function setActualAmount($value) : object
    {
        $this->actual_amount = $value;
        return $this;
    }
    public function getChainId() : string
    {
        return $this->chain_id;
    }
    public function setChainId($value) : object
    {
        $this->chain_id = $value;
        return $this;
    }
    public function getToAddress() : string
    {
        return $this->to_address;
    }
    public function setToAddress($value) : object
    {
        $this->to_address = $value;
        return $this;
    }
    public function getMemo() : string
    {
        return $this->memo ?? '';
    }
    public function setMemo($value) : object
    {
        $this->memo = $value;
        return $this;
    }
    public function getTxHash() : string
    {
        return $this->tx_hash ?? '';
    }
    public function setTxHash($value) : object
    {
        $this->tx_hash = $value;
        return $this;
    }
    public function getConfirmations() : int
    {
        return $this->confirmations;
    }
    public function setConfirmations($value) : object
    {
        $this->confirmations = $value;
        return $this;
    }
    public function getToUserId() : int
    {
        return $this->to_user_id;
    }
    public function setToUserId($value) : object
    {
        $this->to_user_id = $value;
        return $this;
    }
    public function getToEmail() : string
    {
        return $this->to_email;
    }
    public function setToEmail($value) : object
    {
        $this->to_email = $value;
        return $this;
    }
    public function getToPhone() : string
    {
        return $this->to_phone;
    }
    public function setToPhone($value) : object
    {
        $this->to_phone = $value;
        return $this;
    }
    public function getToPhoneCountryCode() : string
    {
        return $this->to_phone_country_code;
    }
    public function setToPhoneCountryCode($value) : object
    {
        $this->to_phone_country_code = $value;
        return $this;
    }
    public function getToUid() : string
    {
        return $this->to_uid;
    }
    public function setToUid($value) : object
    {
        $this->to_uid = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getRemark() : string
    {
        return $this->remark;
    }
    public function setRemark($value) : object
    {
        $this->remark = $value;
        return $this;
    }
    public function getAdminRemark() : string
    {
        return $this->admin_remark;
    }
    public function setAdminRemark($value) : object
    {
        $this->admin_remark = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
    public function getCompletedAt() : mixed
    {
        return $this->completed_at;
    }
    public function setCompletedAt($value) : object
    {
        $this->completed_at = $value;
        return $this;
    }
}
