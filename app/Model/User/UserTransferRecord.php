<?php

declare(strict_types=1);

namespace App\Model\User;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property int $currency_id 币种ID
 * @property int $from_account_type 转出账户类型枚举：flowstype
 * @property int $to_account_type 转入账户类型枚举：flowstype
 * @property float $amount 划转金额
 * @property int $status 状态：1=成功,2=失败,3=处理中
 * @property string $remark 备注
 * @property int $isolated 逐仓资产类型：0=基础币,1=计价币,null=非逐仓
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 */
class UserTransferRecord extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 转出账户类型枚举：flowstype
     */
    public const FIELD_FROM_ACCOUNT_TYPE = 'from_account_type';
    /**
     * 转入账户类型枚举：flowstype
     */
    public const FIELD_TO_ACCOUNT_TYPE = 'to_account_type';
    /**
     * 划转金额
     */
    public const FIELD_AMOUNT = 'amount';
    /**
     * 状态：1=成功,2=失败,3=处理中
     */
    public const FIELD_STATUS = 'status';
    /**
     * 备注
     */
    public const FIELD_REMARK = 'remark';
    /**
     * 逐仓资产类型：0=基础币,1=计价币,null=非逐仓
     */
    public const FIELD_ISOLATED = 'isolated';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'user_transfer_records';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'currency_id', 'from_account_type', 'to_account_type', 'amount', 'status', 'remark', 'isolated', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        // 主键ID
        'id' => 'integer',
        // 用户ID
        'user_id' => 'integer',
        // 币种ID
        'currency_id' => 'integer',
        // 转出账户类型枚举：flowstype
        'from_account_type' => 'integer',
        // 转入账户类型枚举：flowstype
        'to_account_type' => 'integer',
        // 划转金额
        'amount' => 'float',
        // 状态：1=成功,2=失败,3=处理中
        'status' => 'integer',
        // 逐仓资产类型：0=基础币,1=计价币,null=非逐仓
        'isolated' => 'integer',
        // 创建时间
        'created_at' => 'datetime',
        // 更新时间
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getFromAccountType() : int
    {
        return $this->from_account_type;
    }
    public function setFromAccountType($value) : object
    {
        $this->from_account_type = $value;
        return $this;
    }
    public function getToAccountType() : int
    {
        return $this->to_account_type;
    }
    public function setToAccountType($value) : object
    {
        $this->to_account_type = $value;
        return $this;
    }
    public function getAmount() : float
    {
        return $this->amount;
    }
    public function setAmount($value) : object
    {
        $this->amount = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getRemark() : string
    {
        return $this->remark;
    }
    public function setRemark($value) : object
    {
        $this->remark = $value;
        return $this;
    }
    public function getIsolated() : ?int
    {
        return $this->isolated;
    }
    public function setIsolated($value) : object
    {
        $this->isolated = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
