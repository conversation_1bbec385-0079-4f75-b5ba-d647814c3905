<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户VIP等级关联模型
 */

namespace App\Model\User;

use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;

/**
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property int $vip_level_id VIP等级ID
 * @property float $current_spot_trading_volume 当前现货交易量
 * @property float $current_futures_trading_volume 当前合约交易量
 * @property float $current_total_asset 当前总资产
 * @property float $current_specific_asset_amount 当前特定币种资产
 * @property Carbon $level_achieved_at 达到等级时间
 * @property Carbon|null $level_expires_at 等级过期时间
 * @property int $is_active 是否当前等级:0=否,1=是
 * @property int $gift_received 是否领取礼包:0=否,1=是
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
final class UserVipLevel extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * VIP等级ID
     */
    public const FIELD_VIP_LEVEL_ID = 'vip_level_id';
    /**
     * 当前现货交易量
     */
    public const FIELD_CURRENT_SPOT_TRADING_VOLUME = 'current_spot_trading_volume';
    /**
     * 当前合约交易量
     */
    public const FIELD_CURRENT_FUTURES_TRADING_VOLUME = 'current_futures_trading_volume';
    /**
     * 当前总资产
     */
    public const FIELD_CURRENT_TOTAL_ASSET = 'current_total_asset';
    /**
     * 当前特定币种资产
     */
    public const FIELD_CURRENT_SPECIFIC_ASSET_AMOUNT = 'current_specific_asset_amount';
    /**
     * 达到等级时间
     */
    public const FIELD_LEVEL_ACHIEVED_AT = 'level_achieved_at';
    /**
     * 等级过期时间
     */
    public const FIELD_LEVEL_EXPIRES_AT = 'level_expires_at';
    /**
     * 是否当前等级
     */
    public const FIELD_IS_ACTIVE = 'is_active';
    /**
     * 是否领取礼包
     */
    public const FIELD_GIFT_RECEIVED = 'gift_received';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * 是否当前等级：否
     */
    public const IS_ACTIVE_NO = 0;
    /**
     * 是否当前等级：是
     */
    public const IS_ACTIVE_YES = 1;

    /**
     * 是否领取礼包：否
     */
    public const GIFT_RECEIVED_NO = 0;
    /**
     * 是否领取礼包：是
     */
    public const GIFT_RECEIVED_YES = 1;

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_user_vip_level';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 主键ID
        'user_id', // 用户ID
        'vip_level_id', // VIP等级ID
        'current_spot_trading_volume', // 当前现货交易量
        'current_futures_trading_volume', // 当前合约交易量
        'current_total_asset', // 当前总资产
        'current_specific_asset_amount', // 当前特定币种资产
        'level_achieved_at', // 达到等级时间
        'level_expires_at', // 等级过期时间
        'is_active', // 是否当前等级
        'gift_received', // 是否领取礼包
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'user_id' => 'integer', // 用户ID
        'vip_level_id' => 'integer', // VIP等级ID
        'current_spot_trading_volume' => 'decimal:8', // 当前现货交易量
        'current_futures_trading_volume' => 'decimal:8', // 当前合约交易量
        'current_total_asset' => 'decimal:8', // 当前总资产
        'current_specific_asset_amount' => 'decimal:8', // 当前特定币种资产
        'level_achieved_at' => 'datetime', // 达到等级时间
        'level_expires_at' => 'datetime', // 等级过期时间
        'is_active' => 'integer', // 是否当前等级
        'gift_received' => 'integer', // 是否领取礼包
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     */
    public function setUserId(int $value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取VIP等级ID
     */
    public function getVipLevelId(): int
    {
        return $this->vip_level_id;
    }

    /**
     * 设置VIP等级ID
     */
    public function setVipLevelId(int $value): static
    {
        $this->vip_level_id = $value;
        return $this;
    }

    /**
     * 获取当前现货交易量
     */
    public function getCurrentSpotTradingVolume(): float
    {
        return $this->current_spot_trading_volume;
    }

    /**
     * 设置当前现货交易量
     */
    public function setCurrentSpotTradingVolume(float $value): static
    {
        $this->current_spot_trading_volume = $value;
        return $this;
    }

    /**
     * 获取当前合约交易量
     */
    public function getCurrentFuturesTradingVolume(): float
    {
        return $this->current_futures_trading_volume;
    }

    /**
     * 设置当前合约交易量
     */
    public function setCurrentFuturesTradingVolume(float $value): static
    {
        $this->current_futures_trading_volume = $value;
        return $this;
    }

    /**
     * 获取当前总资产
     */
    public function getCurrentTotalAsset(): float
    {
        return $this->current_total_asset;
    }

    /**
     * 设置当前总资产
     */
    public function setCurrentTotalAsset(float $value): static
    {
        $this->current_total_asset = $value;
        return $this;
    }

    /**
     * 获取当前特定币种资产
     */
    public function getCurrentSpecificAssetAmount(): float
    {
        return $this->current_specific_asset_amount;
    }

    /**
     * 设置当前特定币种资产
     */
    public function setCurrentSpecificAssetAmount(float $value): static
    {
        $this->current_specific_asset_amount = $value;
        return $this;
    }

    /**
     * 获取达到等级时间
     */
    public function getLevelAchievedAt(): Carbon
    {
        return $this->level_achieved_at;
    }

    /**
     * 设置达到等级时间
     */
    public function setLevelAchievedAt(Carbon $value): static
    {
        $this->level_achieved_at = $value;
        return $this;
    }

    /**
     * 获取等级过期时间
     */
    public function getLevelExpiresAt(): ?Carbon
    {
        return $this->level_expires_at;
    }

    /**
     * 设置等级过期时间
     */
    public function setLevelExpiresAt(?Carbon $value): static
    {
        $this->level_expires_at = $value;
        return $this;
    }

    /**
     * 获取是否当前等级
     */
    public function getIsActive(): int
    {
        return $this->is_active;
    }

    /**
     * 设置是否当前等级
     */
    public function setIsActive(int $value): static
    {
        $this->is_active = $value;
        return $this;
    }

    /**
     * 获取是否领取礼包
     */
    public function getGiftReceived(): int
    {
        return $this->gift_received;
    }

    /**
     * 设置是否领取礼包
     */
    public function setGiftReceived(int $value): static
    {
        $this->gift_received = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 关联VIP等级
     */
    public function vipLevel(): BelongsTo
    {
        return $this->belongsTo(VipLevel::class, 'vip_level_id', 'id');
    }

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 获取用户当前激活的VIP等级
     */
    public static function getUserActiveVipLevel(int $userId): ?self
    {
        $vipLevel= static::query()
            ->where(self::FIELD_USER_ID, $userId)
            ->where(self::FIELD_IS_ACTIVE, self::IS_ACTIVE_YES)
            ->with('vipLevel')
            ->first();
        if(!$vipLevel){
            static::create([
                self::FIELD_USER_ID => $userId,
                self::FIELD_VIP_LEVEL_ID => VipLevel::query()->where(VipLevel::FIELD_LEVEL,0)->value('id'),
                self::FIELD_IS_ACTIVE => self::IS_ACTIVE_YES,
                self::FIELD_LEVEL_ACHIEVED_AT => Carbon::now()
            ]);
            return self::getUserActiveVipLevel($userId);
        }
        return $vipLevel;
    }

    /**
     * 获取用户VIP等级历史记录
     */
    public static function getUserVipLevelHistory(int $userId): \Hyperf\Database\Model\Collection
    {
        return static::query()
            ->where(self::FIELD_USER_ID, $userId)
            ->with('vipLevel')
            ->orderBy(self::FIELD_LEVEL_ACHIEVED_AT, 'desc')
            ->get();
    }

    /**
     * 检查用户是否有指定VIP等级
     */
    public static function hasVipLevel(int $userId, int $vipLevelId): bool
    {
        return static::query()
            ->where(self::FIELD_USER_ID, $userId)
            ->where(self::FIELD_VIP_LEVEL_ID, $vipLevelId)
            ->where(self::FIELD_IS_ACTIVE, self::IS_ACTIVE_YES)
            ->exists();
    }

    /**
     * 获取用户当前VIP等级的手续费率
     */
    public static function getUserFeeRates(int $userId): ?array
    {
        $userVipLevel = static::getUserActiveVipLevel($userId);

        if (!$userVipLevel || !$userVipLevel->vipLevel) {
            return null;
        }

        $vipLevel = $userVipLevel->vipLevel;

        return [
            'spot_maker_fee_rate' => $vipLevel->getSpotMakerFeeRate(),
            'spot_taker_fee_rate' => $vipLevel->getSpotTakerFeeRate(),
            'futures_maker_fee_rate' => $vipLevel->getFuturesMakerFeeRate(),
            'futures_taker_fee_rate' => $vipLevel->getFuturesTakerFeeRate(),
            'vip_level_id' => $vipLevel->getId()
        ];
    }

    /**
     * 判断用户是否为活跃VIP等级
     */
    public function isActive(): bool
    {
        return $this->is_active === self::IS_ACTIVE_YES;
    }

    /**
     * 判断VIP等级是否已过期
     */
    public function isExpired(): bool
    {
        if (!$this->level_expires_at) {
            return false;
        }

        return $this->level_expires_at->isPast();
    }

    /**
     * 判断是否已领取礼包
     */
    public function hasReceivedGift(): bool
    {
        return $this->gift_received === self::GIFT_RECEIVED_YES;
    }
}
