# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

CPX Exchange is a comprehensive cryptocurrency exchange platform built on the Hyperf framework (PHP 8.1+). The system supports spot trading, margin trading, and perpetual contracts with real-time market data processing and WebSocket communication.

## Development Commands

### Core Commands
- `composer install` - Install dependencies
- `composer dev` or `php bin/hyperf.php server:watch` - Start development server with hot reload
- `composer start` or `php bin/hyperf.php start` - Start production server
- `php bin/hyperf.php migrate` - Run database migrations
- `php bin/hyperf.php db:seed` - Seed database with initial data

### Authentication Setup
- `php bin/hyperf.php jwt:secret` - Generate JWT secrets for authentication

### Development Tools
- Hot reload is available via `composer dev`
- VSCode/Cursor debugging is configured (F5 to debug)
- `dump()` function available for debugging (outputs to browser via dump server)

## Architecture Overview

### Core Modules
- **Admin Module** (`app/Http/Admin/`) - Backend management interface with role-based permissions
- **API Module** (`app/Http/Api/`) - Public REST API endpoints for client applications
- **Market Data Module** (`app/MarketData/`) - Real-time market data processing and aggregation
- **Process Module** (`app/Process/`) - Background processes for trading, risk management, and monitoring
- **Service Layer** (`app/Service/`) - Core business logic services

### Trading System
The platform supports three trading types:
1. **Spot Trading** - Basic buy/sell orders with order book matching
2. **Margin Trading** - Leveraged trading with risk management and auto-liquidation
3. **Perpetual Contracts** - Derivatives trading with funding rates and position management

### Match Engine Architecture
- High-performance C++ match engine via CryptoExchange extension
- Redis Stream-based order processing pipeline
- Real-time WebSocket broadcasting for market data
- Process-based architecture for horizontal scaling

## Key Technologies

### Framework & Core
- **Hyperf 3.1** - High-performance PHP framework based on Swoole
- **PHP 8.1+** with strict types enabled
- **Swoole 5.0+** for async/coroutine support
- **MySQL 8.0+** primary database
- **Redis 4.0+** for caching and message queues

### External Libraries
- **apielf/hyperf-query-builder** - Enhanced query building with filtering/sorting
- **mineadmin/** packages - Admin panel framework integration
- **firebase/php-jwt** - JWT authentication
- **overtrue/easy-sms** - SMS services integration

## Development Guidelines

### Code Standards
- All PHP files must start with `<?php` and `declare(strict_types=1);`
- Use PascalCase for class names, camelCase for methods/properties
- All methods must have return type declarations
- All parameters must have type declarations
- Classes should be final unless designed for inheritance

### Controller Structure
- **Admin controllers**: Extend `AbstractController`, use permission middleware
- **API controllers**: Extend `AbstractController`, use token middleware
- Controllers should delegate business logic to Service classes
- Use `success()` and `error()` methods for responses

### Model Guidelines
- Extend `App\QueryBuilder\Model` (custom base class)
- Use PHPDoc annotations for all properties
- Define field constants for type safety
- Use enums for status/type fields with limited values
- Models are typically final classes

### Service Layer
- **Admin services**: Located in `app/Http/Admin/Service/{Module}/`
- **API services**: Located in `app/Http/Api/Service/{Module}/`, extend `BaseService`
- Services contain business logic and should handle exceptions appropriately
- Use dependency injection for service dependencies

### Query Building
- Use `App\QueryBuilder\QueryBuilder` for list endpoints
- Supports automatic filtering by ID, created_at, updated_at ranges
- Use `pagex()` method for optional pagination (page_size=-1 gets all records)
- Filter syntax: `?filter[field]=value&sort=-created_at&page=1&page_size=15`

### Authentication
- **Admin**: AccessTokenMiddleware + PermissionMiddleware + OperationMiddleware
- **API**: TokenMiddleware for user authentication
- Dual JWT configurations: 'default' (admin) and 'api' (users)
- Token blacklisting supported for secure logout

### Database Migrations
- Use descriptive comments for all fields
- Specify string lengths explicitly
- Add indexes for frequently queried fields
- Include table-level comments

### Exception Handling
- Use `BusinessException` for business logic errors
- Include appropriate error codes from `ResultCode` class
- Use transactions only for multi-operation scenarios
- Catch and re-throw exceptions with meaningful messages

### Request Validation
- Use `BaseFormRequest` subclasses for validation
- Method-specific validation: `{methodName}Rules()` pattern
- Validation rules auto-generated from DDL where possible
- Only add custom rules for complex validations (email, array, etc.)

## Important Patterns

### Enum Usage
- Status/type fields use backed enums in `app/Model/{Module}/Enums/`
- Database stores as tinyint with descriptive comments
- Enums include internationalization via `#[Message]` attributes
- Auto-casting in models via `$casts` property

### Resource Transformers
- Use API Resource classes for data structure transformation
- Located in `app/Http/{Admin|Api}/Resource/{Module}/`
- Handle pagination automatically in base controller

### Background Processing
- Match engine processes for order execution
- Risk monitoring processes for margin/perpetual trading
- Market data aggregation processes for real-time updates
- Commission monitoring for conditional orders

### WebSocket Integration
- Real-time market data streaming
- User-specific order/position updates
- Heartbeat mechanism for connection management
- Redis pub/sub for message distribution

## Testing & Quality

### Available Tools
- PHPUnit for unit testing
- Hyperf testing utilities
- Faker for test data generation
- Mock support for dependency isolation

### Logging
- Use `logger('channel/file.log')->info()` helper
- Avoid excessive logging in normal operations
- Log important business events, errors, and security actions
- Separate log channels for different concerns

## Security Considerations

- Input validation on all user data
- JWT token blacklisting for secure logout
- Permission-based access control for admin functions
- Database queries use Eloquent ORM (no raw SQL)
- Sensitive fields encrypted in database
- Rate limiting and CORS protection

## Performance Features

- Model caching with Redis integration
- Connection pooling for database/Redis
- Coroutine-based async processing
- Process-based load balancing
- Query result caching where appropriate

## Common Gotchas

- Always use absolute paths, not relative paths
- Models extend custom `App\QueryBuilder\Model`, not Hyperf's base Model
- Services go in HTTP module directories, not global `app/Service/`
- Use `pagex()` for flexible pagination, `page()` for forced pagination
- Transaction scope should be minimal - only for multi-operation scenarios
- Enum classes must be in module-specific `Enums` subdirectories

## File Generation

The project includes AI-powered CRUD generation tools in `app/AIGenerate/` with templates and prompts for rapid development. These tools can generate complete CRUD functionality including controllers, services, models, and validation based on database DDL.