-- Redis Lua脚本：扣减用户资金
-- 参数说明：
-- KEYS[1]: 用户资产键 user:assets:{user_id}:{account_type}:{currency_id}
-- ARGV[1]: 扣减金额
-- ARGV[2]: 资产字段名(默认available)

local asset_key = KEYS[1]
local amount = ARGV[1]
local asset_field = ARGV[2] or 'available'

-- 检查金额是否有效
if tonumber(amount) <= 0 then
    return {false, 'invalid_amount', amount}
end

-- 获取当前资产状态
local current_asset = redis.call('HGET', asset_key, asset_field) or '0'

-- 检查余额是否足够（包含容错处理）
local tolerance = '0.********'
if (tonumber(current_asset) - tonumber(amount)) < (0 - tonumber(tolerance)) then
    return {false, 'insufficient_balance', current_asset, amount}
end

-- 执行资金扣减操作
redis.call('HINCRBYFLOAT', asset_key, asset_field, '-' .. amount)

-- 获取操作后状态
local after_asset = redis.call('HGET', asset_key, asset_field)

-- 返回操作结果
return {true, current_asset, after_asset}
