-- Redis Lua脚本：资金转账（从一个用户转到另一个用户）
-- 参数说明：
-- KEYS[1]: 源用户资产键 user:assets:{from_user_id}:{account_type}:{currency_id}
-- KEYS[2]: 目标用户资产键 user:assets:{to_user_id}:{account_type}:{currency_id}
-- ARGV[1]: 转账金额
-- ARGV[2]: 源资产字段名(默认available)
-- ARGV[3]: 目标资产字段名(默认available)

local from_asset_key = KEYS[1]
local to_asset_key = KEYS[2]
local amount = ARGV[1]
local from_field = ARGV[2] or 'available'
local to_field = ARGV[3] or 'available'

-- 检查金额是否有效
if tonumber(amount) <= 0 then
    return {false, 'invalid_amount', amount}
end

-- 获取当前资产状态
local from_current = redis.call('HGET', from_asset_key, from_field) or '0'
local to_current = redis.call('HGET', to_asset_key, to_field) or '0'

-- 检查余额是否足够（包含容错处理）
local tolerance = '0.********'
if (tonumber(from_current) - tonumber(amount)) < (0 - tonumber(tolerance)) then
    return {false, 'insufficient_balance', from_current, amount}
end

-- 执行资金转账操作
redis.call('HINCRBYFLOAT', from_asset_key, from_field, '-' .. amount)
redis.call('HINCRBYFLOAT', to_asset_key, to_field, amount)

-- 获取操作后状态
local from_after = redis.call('HGET', from_asset_key, from_field)
local to_after = redis.call('HGET', to_asset_key, to_field)

-- 返回操作结果
return {true, from_current, from_after, to_current, to_after}
