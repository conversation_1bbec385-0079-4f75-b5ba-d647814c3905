-- Redis Lua脚本：批量获取用户资产
-- 参数说明：
-- KEYS[1]: 用户资产索引键 user:assets:index:{user_id}
-- ARGV[1]: 账户类型过滤（可选，传0表示不过滤）
-- ARGV[2]: 是否只返回有余额的资产（1=是，0=否）

local index_key = KEYS[1]
local account_type_filter = tonumber(ARGV[1]) or 0
local only_with_balance = tonumber(ARGV[2]) or 0

-- 获取用户所有资产索引
local asset_indexes = redis.call('SMEMBERS', index_key)

if #asset_indexes == 0 then
    return {}
end

local results = {}

for i, asset_index in ipairs(asset_indexes) do
    -- 解析索引：account_type:currency_id
    local parts = {}
    for part in string.gmatch(asset_index, "([^:]+)") do
        table.insert(parts, part)
    end
    
    if #parts == 2 then
        local account_type = tonumber(parts[1])
        local currency_id = tonumber(parts[2])
        
        -- 账户类型过滤
        if account_type_filter == 0 or account_type == account_type_filter then
            -- 构建资产键
            local asset_key = 'user:assets:' .. string.match(index_key, 'user:assets:index:(%d+)') .. ':' .. account_type .. ':' .. currency_id
            
            -- 获取资产数据
            local asset_data = redis.call('HMGET', asset_key, 
                'available', 'frozen', 'locked', 'margin_quote', 'borrowed_amount', 'interest_amount')
            
            local available = tonumber(asset_data[1]) or 0
            local frozen = tonumber(asset_data[2]) or 0
            local locked = tonumber(asset_data[3]) or 0
            local margin_quote = tonumber(asset_data[4]) or 0
            local borrowed_amount = tonumber(asset_data[5]) or 0
            local interest_amount = tonumber(asset_data[6]) or 0
            
            -- 余额过滤
            if only_with_balance == 0 or available > 0 then
                table.insert(results, {
                    account_type = account_type,
                    currency_id = currency_id,
                    available = tostring(available),
                    frozen = tostring(frozen),
                    locked = tostring(locked),
                    margin_quote = tostring(margin_quote),
                    borrowed_amount = tostring(borrowed_amount),
                    interest_amount = tostring(interest_amount)
                })
            end
        end
    end
end

return results
