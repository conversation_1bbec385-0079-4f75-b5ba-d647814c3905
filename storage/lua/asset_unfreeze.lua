-- Redis Lua脚本：解冻用户资金
-- 参数说明：
-- KEYS[1]: 用户资产键 user:assets:{user_id}:{account_type}:{currency_id}
-- ARGV[1]: 解冻金额
-- ARGV[2]: 冻结字段名(默认frozen)
-- ARGV[3]: 目标字段名(默认available)

local asset_key = KEYS[1]
local amount = ARGV[1]
local frozen_field = ARGV[2] or 'frozen'
local target_field = ARGV[3] or 'available'

-- 检查金额是否有效
if tonumber(amount) <= 0 then
    return {false, 'invalid_amount', amount}
end

-- 获取当前资产状态
local current_frozen = redis.call('HGET', asset_key, frozen_field) or '0'
local current_target = redis.call('HGET', asset_key, target_field) or '0'

-- 检查冻结余额是否足够（包含容错处理）
local tolerance = '0.********'
if (tonumber(current_frozen) - tonumber(amount)) < (0 - tonumber(tolerance)) then
    return {false, 'insufficient_frozen_balance', current_frozen, amount}
end

-- 执行资金解冻操作
redis.call('HINCRBYFLOAT', asset_key, frozen_field, '-' .. amount)
redis.call('HINCRBYFLOAT', asset_key, target_field, amount)

-- 获取操作后状态
local after_frozen = redis.call('HGET', asset_key, frozen_field)
local after_target = redis.call('HGET', asset_key, target_field)

-- 返回操作结果
return {true, current_frozen, after_frozen, current_target, after_target}
