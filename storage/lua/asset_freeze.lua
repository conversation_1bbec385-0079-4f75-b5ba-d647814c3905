-- Redis Lua脚本：冻结用户资金
-- 参数说明：
-- KEYS[1]: 用户资产键 user:assets:{user_id}:{account_type}:{currency_id}
-- ARGV[1]: 冻结金额
-- ARGV[2]: 资产字段名(默认available)
-- ARGV[3]: 冻结字段名(默认frozen)

local asset_key = KEYS[1]
local amount = ARGV[1]
local asset_field = ARGV[2] or 'available'
local frozen_field = ARGV[3] or 'frozen'

-- 检查金额是否有效
if tonumber(amount) <= 0 then
    return {false, 'invalid_amount', amount}
end

-- 获取当前资产状态
local current_asset = redis.call('HGET', asset_key, asset_field) or '0'
local current_frozen = redis.call('HGET', asset_key, frozen_field) or '0'

-- 检查余额是否足够（包含容错处理）
local tolerance = '0.********'
if (tonumber(current_asset) - tonumber(amount)) < (0 - tonumber(tolerance)) then
    return {false, 'insufficient_balance', current_asset, amount}
end

-- 执行资金冻结操作
redis.call('HINCRBYFLOAT', asset_key, asset_field, '-' .. amount)
redis.call('HINCRBYFLOAT', asset_key, frozen_field, amount)

-- 获取操作后状态
local after_asset = redis.call('HGET', asset_key, asset_field)
local after_frozen = redis.call('HGET', asset_key, frozen_field)

-- 返回操作结果
return {true, current_asset, after_asset, current_frozen, after_frozen}
