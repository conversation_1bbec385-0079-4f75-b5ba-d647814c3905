#### 6.1.33 合约-跟单者交易分析精灵统计接口（雷达图）

**GET** `/api/copy/expert/contract/follower-analysis-radar`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "radar_data": {
      "indicators": [
        {
          "name": "盈利因子",
          "max": 5,
          "description": "总盈利与总亏损比例"
        },
        {
          "name": "胜率",
          "max": 100,
          "description": "胜率百分比"
        },
        {
          "name": "平均盈亏比",
          "max": 5,
          "description": "平均盈利与平均亏损比例"
        }
      ],
      "series": [
        {
          "name": "跟单专家参数",
          "data": [3.2, 75.5, 2.3] // 专家的数据
        },
        {
          "name": "跟单者参数",
          "data": [2.8, 68.2, 2.0] // 跟单者的数据
        }
      ]
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - 默认查询最近 30 天的数据
- 查询跟单者的交易数据并计算各项指标：
  - 跟单者评分：基于跟单收益、风控能力等计算
  - 跟单者排名：在该专家跟单者中的排名
- 生成跟单者雷达图数据：
  - 定义雷达图指标（跟单收益率、风控能力、跟单频率等）
  - 计算各指标的数值和满分值
  - 生成跟单者的雷达图数据
- 提供对比数据：
  - 专家自身的交易数据作为对比基准
  - 其他跟单者的平均水平
- 返回跟单者雷达图所需的完整数据格式