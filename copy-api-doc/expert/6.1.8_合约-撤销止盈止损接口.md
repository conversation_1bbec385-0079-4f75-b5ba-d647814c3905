#### 6.1.8 合约-撤销止盈止损接口

**DELETE** `/api/copy/expert/contract/position/{id}/stop-orders`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (仓位 ID)

**Body 参数**：

```json
{
  "type": 1 // 1-止盈, 2-止损, 3-止盈止损
}
```

**验证规则**:

- type: required|integer|in:1,2,3

响应示例：

```json
{
  "code": 200,
  "message": "撤销成功",
  "data": null
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 验证仓位 ID 是否存在且属于当前专家
- 根据 type 参数确定撤销类型：
  - 1：仅撤销止盈订单
  - 2：仅撤销止损订单
  - 3：同时撤销止盈止损订单
- 调用合约交易系统 API 撤销对应的止盈止损订单
- 记录操作日志
- 返回撤销结果