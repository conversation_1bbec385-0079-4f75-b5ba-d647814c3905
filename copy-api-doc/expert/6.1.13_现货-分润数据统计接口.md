#### 6.1.13 现货-分润数据统计接口

**GET** `/api/copy/expert/spot/profit-sharing/statistics`

**中间件**: TokenMiddleware

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_profit_sharing": "1800.00000000", // 累计已分润
    "pending_profit_sharing": "95.00000000", // 预计待分润
    "yesterday_profit_sharing": "65.00000000", // 昨日分润
    "current_profit_sharing_rate": "8.00" // 当前分润比例 %
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 查询现货专家的分润统计数据：
  - 计算累计已分润金额：所有已结算的现货分润总和
  - 计算预计待分润金额：当前未结算的预计现货分润
  - 计算昨日分润金额：昨天的现货分润收入
  - 获取当前分润比例设置
- 统计现货分润来源分析：
  - 按跟单者统计现货分润贡献
  - 按时间段统计现货分润趋势
  - 按币种统计现货分润分布
- 计算现货分润相关指标：
  - 平均日分润金额
  - 分润增长趋势
  - 分润稳定性指标
- 返回完整的现货分润统计数据