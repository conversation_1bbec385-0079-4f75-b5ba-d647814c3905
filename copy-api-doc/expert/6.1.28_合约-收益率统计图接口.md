#### 6.1.28 合约-收益率统计图接口

**GET** `/api/copy/expert/contract/profit-rate-chart`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "date": "2025-01-01", // 日期
        "profit_rate": "2.50" // 收益率 %
      },
      {
        "date": "2025-01-02",
        "profit_rate": "3.20"
      },
      {
        "date": "2025-01-03",
        "profit_rate": "1.80"
      }
    ]
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - 默认查询最近 30 天的数据
- 查询专家的历史交易数据：
  - 按日期聚合已平仓订单的盈亏数据
  - 计算每日的累计收益率
- 计算收益率统计：
  - 以专家初始资金为基准计算收益率
  - 计算累计收益率和日收益率
  - 处理数据缺失的日期（补零或插值）
- 生成图表数据：
  - 按日期排序生成时间序列数据
  - 格式化收益率为百分比形式
  - 提供平滑的收益率曲线数据
- 返回图表所需的数据格式