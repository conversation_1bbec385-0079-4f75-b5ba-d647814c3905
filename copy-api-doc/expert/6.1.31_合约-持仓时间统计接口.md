#### 6.1.31 合约-持仓时间统计接口

**GET** `/api/copy/expert/contract/position-time-stats`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "average_hold_time": "4.5", // 平均持仓时间（小时）
    "max_hold_time": "24.8", // 最长持仓时间（小时）
    "chart_data": [
      {
        "time_range": "0-5小时", // 持仓时段
        "profit_count": 15, // 盈利笔数
        "loss_count": 3, // 亏损笔数
        "avg_profit": "125.50000000", // 平均盈利
        "avg_loss": "-45.20000000" // 平均亏损
      },
      {
        "time_range": "5-10小时",
        "profit_count": 12,
        "loss_count": 5,
        "avg_profit": "180.30000000",
        "avg_loss": "-65.80000000"
      },
      {
        "time_range": "10-15小时",
        "profit_count": 8,
        "loss_count": 2,
        "avg_profit": "220.10000000",
        "avg_loss": "-85.50000000"
      },
      {
        "time_range": "15-20小时",
        "profit_count": 5,
        "loss_count": 1,
        "avg_profit": "350.00000000",
        "avg_loss": "-120.00000000"
      },
      {
        "time_range": "20小时以上",
        "profit_count": 3,
        "loss_count": 0,
        "avg_profit": "500.00000000",
        "avg_loss": "0.00000000"
      }
    ]
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - 默认查询最近 30 天的数据
- 查询专家的历史合约持仓数据：
  - 统计各持仓时间段的交易记录
  - 计算持仓时长分布
- 按持仓时间分组统计：
  - 0-1 小时、1-5 小时、5-10 小时、10-15 小时、15-20 小时、20 小时以上
  - 统计各时间段的盈利笔数和亏损笔数
  - 计算各时间段的平均盈利和平均亏损
- 生成持仓时间分析数据：
  - 按时间段排序
  - 包含时间范围、盈亏统计、平均盈亏金额
- 返回持仓时间统计柱状图数据