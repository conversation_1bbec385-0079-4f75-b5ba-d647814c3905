#### 6.1.45 交易专家等级接口

**GET** `/api/copy/expert/levels`

**中间件**: TokenMiddleware

**Query 参数**：

- `type`: integer|nullable|in:1,2 (专家类型：1-合约专家，2-现货专家)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "current_level": {
      "id": 2,
      "type": 1,
      "level": 2,
      "name": "中级专家",
      "icon": "https://test.com/icon2.png"
    },
    "all_levels": [
      {
        "id": 1,
        "type": 1,
        "level": 1,
        "name": "初级专家",
        "icon": "https://test.com/icon1.png",
        "condition_amount": "1000.00000000",
        "condition_follow_amount": "5000.00000000",
        "condition_follow_count": 10,
        "max_follow_count": 100,
        "max_profit_rate": "10.00"
      },
      {
        "id": 2,
        "type": 1,
        "level": 2,
        "name": "中级专家",
        "icon": "https://test.com/icon2.png",
        "condition_amount": "5000.00000000",
        "condition_follow_amount": "20000.00000000",
        "condition_follow_count": 50,
        "max_follow_count": 200,
        "max_profit_rate": "15.00"
      }
    ]
  }
}
```

**接口逻辑**：

- 验证当前用户是否为交易专家（合约或现货）
- 根据 type 参数过滤专家类型：
  - 如果指定 type，则只返回对应类型的等级
  - 如果不指定 type，则返回所有类型的等级
- 查询当前专家的等级信息：
  - 获取专家当前等级 ID
  - 查询当前等级的详细信息
- 查询所有可用等级列表：
  - 按等级从低到高排序
  - 包含等级条件、权益、限制等信息
- 计算专家升级条件：
  - 检查当前专家是否满足更高等级的条件
  - 计算距离下一等级的差距
- 返回等级信息：
  - 当前等级详情
  - 所有等级列表
  - 升级条件和进度