#### 6.1.40 合约-尊享模式邀请链接列表接口

**GET** `/api/copy/expert/contract/exclusive-invites`

**中间件**: TokenMiddleware

**Query 参数**：

- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "expert_id": 1,
        "expert_type": "App\\Model\\Copy\\CopyContractExpert",
        "name": "VIP客户邀请",
        "invite_code": "INV123456789",
        "invite_url": "https://example.com/copy/invite/INV123456789",
        "profit_sharing_rate": "15.00",
        "max_use_count": 10,
        "used_count": 3,
        "expire_at": "2025-12-31 23:59:59",
        "is_active": 1,
        "created_at": "2025-01-01 16:00:00"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 5,
      "total_pages": 1
    }
  }
}
```