#### 6.1.7 合约-带单订单数据接口

**GET** `/api/copy/expert/contract/orders`

**中间件**: TokenMiddleware

**Query 参数**：

- `type`: required|string|in:current_detail,current_summary,history (当前带单-明细/当前带单-汇总/历史带单)
- `currency_id`: integer|nullable (币种 ID，为空表示全部币种)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例（当前带单-明细）：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "currency_symbol": "BTCUSDT", // 币种
        "margin_mode": "isolated", // 保证金模式
        "direction": "long", // 方向
        "leverage": 10, // 杠杆
        "open_time": "2025-01-01 10:00:00", // 开仓时间
        "avg_price": "45000.00000000", // 持仓均价
        "current_price": "46000.00000000", // 当前价
        "quantity": "0.10000000", // 数量
        "entry_price": "44800.00000000", // 订单入场价
        "margin": "450.00000000", // 保证金
        "unrealized_pnl": "120.00000000", // 未实现盈亏
        "order_id": "ORD123456789" // 订单编号
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 5,
      "total_pages": 1
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 根据 type 参数确定查询类型：
  - current_detail：查询当前持仓中的带单订单明细
  - current_summary：查询当前持仓中的带单订单汇总
  - history：查询历史已平仓的带单订单
- 根据 currency_id 过滤币种（为空则查询所有币种）
- 查询专家的合约订单数据，包含订单基本信息、价格信息、盈亏信息
- 计算未实现盈亏（仅当前持仓订单）
- 获取当前市场价格用于计算浮动盈亏
- 分页返回订单数据