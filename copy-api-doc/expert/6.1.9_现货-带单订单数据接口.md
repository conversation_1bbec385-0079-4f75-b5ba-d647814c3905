#### 6.1.9 现货-带单订单数据接口

**GET** `/api/copy/expert/spot/orders`

**中间件**: TokenMiddleware

**Query 参数**：

- `type`: required|string|in:current_detail,current_summary,history (当前带单-明细/当前带单-汇总/历史带单)
- `currency_id`: integer|nullable (币种 ID，为空表示全部币种)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例（当前带单-明细）：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "currency_pair": "BTCUSDT", // 币对
        "profit": "150.00000000", // 收益
        "buy_price": "44000.00000000", // 买入价
        "buy_time": "2025-01-01 10:00:00", // 买入时间
        "quantity": "0.10000000", // 数量
        "take_profit_price": "48000.00000000", // 止盈价
        "stop_loss_price": "42000.00000000" // 止损价
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 8,
      "total_pages": 1
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 根据 type 参数确定查询类型：
  - current_detail：查询当前持仓中的现货带单订单明细
  - current_summary：查询当前持仓中的现货带单订单汇总
  - history：查询历史已完成的现货带单订单
- 根据 currency_id 过滤币种（为空则查询所有币种）
- 查询专家的现货订单数据，包含订单基本信息、价格信息、盈亏信息
- 计算未实现盈亏（仅当前持仓订单）
- 获取当前市场价格用于计算浮动盈亏
- 分页返回现货订单数据