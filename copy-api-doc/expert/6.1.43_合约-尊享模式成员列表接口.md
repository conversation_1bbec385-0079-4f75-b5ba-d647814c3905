#### 6.1.43 合约-尊享模式成员列表接口

**GET** `/api/copy/expert/contract/exclusive-members`

**中间件**: TokenMiddleware

**Query 参数**：

- `invite_id`: integer|nullable (邀请链接 ID，为空表示查看所有尊享成员)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "follower_user": {
          "id": 100,
          "username": "vip_user001",
          "display_name": "VIP用户001",
          "avatar": "https://test.com/avatar.png"
        },
        "invite": {
          "id": 1,
          "name": "VIP客户邀请",
          "invite_code": "INV123456789"
        },
        "profit_sharing_rate": "15.00", // 分润比例 %
        "copy_account_balance": "5000.********", // 合约跟单账户资金
        "total_copy_count": 35, // 累计跟单笔数
        "copy_profit": "850.********", // 跟单收益
        "copy_net_profit": "722.********", // 跟单净利润
        "joined_at": "2025-01-01 10:00:00" // 加入时间
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 15,
      "total_pages": 1
    }
  }
}
```