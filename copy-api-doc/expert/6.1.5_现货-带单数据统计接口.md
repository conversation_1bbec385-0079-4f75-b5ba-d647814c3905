#### 6.1.5 现货-带单数据统计接口

**GET** `/api/copy/expert/spot/statistics`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable (0-全部, 7-7 天, 30-30 天, 90-90 天, 180-180 天)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "profit_rate": "12.30", // 收益率 %
    "total_profit": "800.00000000", // 总收益
    "total_follower_count": 120, // 累计跟单人数
    "follower_profit": "8000.00000000", // 跟单者收益
    "win_rate": "68.50", // 胜率 %
    "profit_order_count": 35, // 盈利笔数
    "loss_order_count": 16 // 亏损笔数
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 根据时间过滤条件查询专家的历史现货带单订单数据
- 计算收益率：(总收益 / 初始资金) \* 100%
- 计算总收益：所有已平仓订单的盈亏总和
- 统计累计跟单人数：历史所有跟单过该专家的用户数量
- 计算跟单者收益：所有跟单者的总收益金额
- 计算胜率：盈利笔数 / 总交易笔数 \* 100%
- 统计盈利笔数和亏损笔数
- 返回统计数据