#### 6.1.46 合约-粉丝列表接口

**GET** `/api/copy/expert/contract/fans`

**中间件**: TokenMiddleware

**Query 参数**：

- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "fan_user": {
          "id": 200,
          "username": "fan_user001",
          "display_name": "粉丝用户001",
          "avatar": "https://test.com/avatar.png"
        },
        "followed_at": "2025-01-01 12:00:00", // 关注时间
        "is_copying": true // 是否正在跟单
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 200,
      "total_pages": 10
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 查询专家的粉丝列表：
  - 查询关注该专家的所有用户
  - 关联查询粉丝的用户基本信息
  - 获取粉丝的关注时间
- 检查粉丝的跟单状态：
  - 判断粉丝是否正在跟单该专家
  - 获取跟单配置的启用状态
- 按关注时间倒序排列粉丝列表
- 分页返回粉丝数据：
  - 包含粉丝用户信息
  - 关注时间
  - 是否正在跟单状态
- 统计粉丝总数和分页信息