#### 6.1.25 合约-合约跟单账号余额历史接口

**GET** `/api/copy/expert/contract/balance-history`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "type": "transfer_in", // 类型：transfer_in-转入，transfer_out-转出，profit-收益，loss-亏损，fee-手续费
        "amount": "500.00000000", // 金额
        "direction": "in", // 流向：in-流入，out-流出
        "balance_after": "2500.00000000", // 操作后余额
        "description": "从现货账户转入", // 描述
        "created_at": "2025-01-01 10:00:00" // 时间
      },
      {
        "id": 2,
        "type": "profit",
        "amount": "150.00000000",
        "direction": "in",
        "balance_after": "2650.00000000",
        "description": "交易盈利",
        "created_at": "2025-01-01 14:30:00"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 45,
      "total_pages": 3
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析时间过滤条件：
  - 根据 days 参数确定查询时间范围
  - 支持自定义开始和结束日期
- 查询合约跟单账户余额变动记录：
  - 按时间倒序排列
  - 包含转入、转出、盈利、亏损、手续费等类型
- 计算余额变动详情：
  - 记录每次变动的金额和方向
  - 计算变动后的账户余额
  - 生成变动描述信息
- 分类统计余额变动：
  - 按变动类型进行分类统计
  - 计算各类型的总金额
- 分页返回余额历史记录
- 提供余额变动趋势分析数据