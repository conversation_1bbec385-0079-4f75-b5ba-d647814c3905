#### 6.1.21 合约-问题反馈/身份撤销接口

**POST** `/api/copy/expert/contract/feedback`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "feedback_type": 1, // 1-问题反馈, 2-身份撤销
  "problem_type": "技术问题", // 问题类型
  "content": "遇到了技术问题，需要帮助", // 问题描述
  "refund_account_type": 1 // 身份撤销时必填，资金退回账户类型：1-现货账户，2-合约账户
}
```

**验证规则**:

- feedback_type: required|integer|in:1,2
- problem_type: required|string|max:50
- content: required|string|max:1000
- refund_account_type: required_if:feedback_type,2|integer|in:1,2

响应示例：

```json
{
  "code": 200,
  "message": "提交成功",
  "data": {
    "id": 1,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "expert_user_id": 1,
    "type": 1,
    "feedback_type": 1,
    "problem_type": "技术问题",
    "content": "遇到了技术问题，需要帮助",
    "refund_account_type": null,
    "refund_amount": null,
    "created_at": "2025-01-01 16:00:00"
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 根据 feedback_type 确定处理类型：
  - 问题反馈（type=1）：记录用户反馈的问题
  - 身份撤销（type=2）：处理专家身份撤销申请
- 验证参数完整性：
  - 检查问题类型和内容的有效性
  - 身份撤销时验证退款账户类型
- 处理身份撤销逻辑：
  - 计算需要退还的资金金额
  - 验证退款账户的有效性
  - 停用专家身份和相关功能
- 创建反馈记录：
  - 记录反馈类型、问题描述、处理状态
  - 关联专家信息和用户信息
- 发送通知给管理员处理
- 返回反馈提交结果