#### 6.1.34 合约-交易指标接口

**GET** `/api/copy/expert/contract/trading-indicators`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "last_trade_time": "2025-01-01 14:30:00", // 最近交易时间
    "trade_count": 45, // 交易次数
    "daily_avg_trade_count": "3.2", // 日均交易次数
    "total_profit": "2500.00000000", // 总盈利
    "max_profit": "500.00000000", // 最大盈利
    "max_loss": "-200.00000000", // 最大亏损
    "max_drawdown": "350.00000000", // 最大回撤
    "long_short_ratio": "1.8", // 多空比（多头交易数/空头交易数）
    "long_trade_count": 28, // 多头交易次数
    "short_trade_count": 17, // 空头交易次数
    "avg_profit_per_trade": "55.56000000", // 平均每笔盈利
    "profit_factor": "3.2" // 盈利因子
  }
}
```