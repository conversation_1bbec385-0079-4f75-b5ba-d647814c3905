#### 6.1.16 合约-移除跟随者接口

**DELETE** `/api/copy/expert/contract/followers`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "ids": [100, 101, 102] // 跟随者用户ID数组
}
```

**验证规则**:

- ids: required|array|min:1
- ids.\*: required|integer|exists:cpx_user,id

响应示例：

```json
{
  "code": 200,
  "message": "移除成功",
  "data": {
    "removed_count": 3 // 成功移除的跟随者数量
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 验证参数有效性：
  - 检查用户 ID 数组是否为空
  - 验证每个用户 ID 是否存在
  - 确认这些用户是否为当前专家的跟随者
- 执行移除操作：
  - 停用跟随者的跟单配置
  - 平仓跟随者当前的跟单持仓
  - 清理跟单关系记录
  - 发送通知给被移除的跟随者
- 记录操作日志：
  - 记录移除操作的时间和原因
  - 记录被移除跟随者的信息
- 统计移除结果：
  - 计算成功移除的跟随者数量
  - 处理移除失败的情况
- 返回移除操作结果