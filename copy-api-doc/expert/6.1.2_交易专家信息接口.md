#### 6.1.2 交易专家信息接口

**GET** `/api/copy/expert/info`

Query 参数：

- `type`: 1-合约交易专家, 2-现货交易专家

响应示例（合约交易专家）：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "introduction": "专业合约交易，稳健收益",
    "transfer_from_account": 1,
    "transfer_amount": "100.********",
    "status": 1,
    "review_remark": null,
    "reviewed_at": null,
    "reviewed_by": null,
    "is_active": 1,
    "show_total_assets": 0,
    "position_protection": 1,
    "min_follow_amount": null,
    "recommend_params": null,
    "profit_sharing_rate": "0.0000",
    "level_id": 1,
    "exclusive_mode": 0,
    "exclusive_profit_rate": null,
    "created_at": "2025-01-01 00:00:00",
    "updated_at": "2025-01-01 00:00:00",
    "user": {
      "id": 1,
      "account": "**********",
      "username": "test",
      "display_name": "test",
      "email": "<EMAIL>",
      "phone_country_code": "+86",
      "phone": "**********",
      "avatar": "https://test.com/avatar.png",
      "agent_id": 1,
      "language": "zh-CN",
      "created_at": "2025-01-01 00:00:00",
      "updated_at": "2025-01-01 00:00:00"
    },
    "level": {
      "id": 1,
      "type": 1,
      "level": 1,
      "name": "初级专家",
      "icon": "https://test.com/icon.png",
      "max_follow_count": 100,
      "max_profit_rate": 10
    },
    "statistics": {
      // 当前合约跟单账户资金
      // 当前跟单人数
      // 当前带单正在进行中的订单数
    }
  }
}
```

响应示例（现货交易专家）：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "introduction": "专业合约交易，稳健收益",
    "status": 1,
    "review_remark": null,
    "reviewed_at": null,
    "reviewed_by": null,
    "is_active": 1,
    "show_total_assets": 0,
    "position_protection": 1,
    "min_follow_amount": null,
    "recommend_params": null,
    "profit_sharing_rate": "0.0000",
    "level_id": 1,
    "exclusive_mode": 0,
    "exclusive_profit_rate": null,
    "created_at": "2025-01-01 00:00:00",
    "updated_at": "2025-01-01 00:00:00",
    "user": {
      "id": 1,
      "account": "**********",
      "username": "test",
      "display_name": "test",
      "email": "<EMAIL>",
      "phone_country_code": "+86",
      "phone": "**********",
      "avatar": "https://test.com/avatar.png",
      "agent_id": 1,
      "language": "zh-CN",
      "created_at": "2025-01-01 00:00:00",
      "updated_at": "2025-01-01 00:00:00"
    },
    "level": {
      "id": 1,
      "type": 2,
      "level": 1,
      "name": "初级专家",
      "icon": "https://test.com/icon.png",
      "max_follow_count": 100,
      "max_profit_rate": 10
    },
    "statistics": {
      // 当前现货账户资金
      // 当前跟单人数
      // 当前带单正在进行中的订单数
    }
  }
}
```

**接口逻辑**：

- 根据 type 参数查询专家信息及相关关联数据
- 查询用户信息、等级信息
- 统计当前跟单账户资金、跟单人数、进行中订单数、粉丝数量
- 返回完整的专家信息