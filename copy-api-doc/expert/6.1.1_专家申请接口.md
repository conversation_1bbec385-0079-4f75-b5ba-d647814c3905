#### 6.1.1 专家申请接口

**POST** `/api/copy/expert/apply`

请求参数：

```json
{
  "type": 1, // 1-合约交易专家, 2-现货交易专家
  "display_name": "展示名称", // 展示名称，可选，更新到 cpx_user 表中
  "introduction": "专业合约交易，稳健收益",
  "transfer_from_account": "1", // 划转资金来源账户，枚举：\App\Model\Enums\User\AccountType::class
  "transfer_amount": "100.********" // type=1时必填，合约专家需要划转资金
}
```

响应示例（需要审核）：

```json
{
  "code": 200,
  "message": "申请提交成功，等待审核",
  "data": {
    "id": 1,
    "user_id": 1,
    "introduction": "专业合约交易，稳健收益",
    "transfer_from_account": 1,
    "transfer_amount": "100.********",
    "status": 1,
    "review_remark": null,
    "reviewed_at": null,
    "reviewed_by": null,
    "is_active": 1,
    "show_total_assets": 0,
    "position_protection": 1,
    "min_follow_amount": null,
    "recommend_params": null,
    "profit_sharing_rate": "0.0000",
    "level_id": 1,
    "exclusive_mode": 0,
    "exclusive_profit_rate": null,
    "created_at": "2025-01-01 00:00:00",
    "updated_at": "2025-01-01 00:00:00",
    "user": {
      "id": 1,
      "account": "**********",
      "username": "test",
      "display_name": "test",
      "email": "<EMAIL>",
      "phone_country_code": "+86",
      "phone": "**********",
      "avatar": "https://test.com/avatar.png",
      "agent_id": 1,
      "language": "zh-CN",
      "created_at": "2025-01-01 00:00:00",
      "updated_at": "2025-01-01 00:00:00"
    }
  }
}
```

响应示例（不需要审核）：

```json
{
  "code": 200,
  "message": "申请成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "introduction": "专业合约交易，稳健收益",
    "status": 1,
    "review_remark": null,
    "reviewed_at": null,
    "reviewed_by": null,
    "is_active": 1,
    "show_total_assets": 0,
    "position_protection": 1,
    "min_follow_amount": null,
    "recommend_params": null,
    "profit_sharing_rate": "0.0000",
    "level_id": 1,
    "exclusive_mode": 0,
    "exclusive_profit_rate": null,
    "created_at": "2025-01-01 00:00:00",
    "updated_at": "2025-01-01 00:00:00",
    "user": {
      "id": 1,
      "account": "**********",
      "username": "test",
      "display_name": "test",
      "email": "<EMAIL>",
      "phone_country_code": "+86",
      "phone": "**********",
      "avatar": "https://test.com/avatar.png",
      "agent_id": 1,
      "language": "zh-CN",
      "created_at": "2025-01-01 00:00:00",
      "updated_at": "2025-01-01 00:00:00"
    }
  }
}
```

接口逻辑：

- 合约交易专家申请

  - 检查用户状态是否为正常状态，如果不是则返回异常
  - 检查用户是否完成 kyc 认证，如果没有则返回异常
  - 检查用户是否已经是合约交易专家，如果是则返回异常
  - 检查用户申请状态，如果是待审核则返回异常
  - 验证划转资金金额是否大于等于系统设置中“合约专家最小划转资金”
  - 资金划转（TODO: 待补充统一资金划转方法），检查划转是否成功
  - 带单币种默认系统中的所有币种
  - 最小跟单金额从系统设置中获取
  - 根据等级条件匹配专家等级
  - 创建合约交易专家表记录（判断系统设置中“合约专家申请是否需要审核”，不需要审核时 status 直接设置为审核通过）
  - 更新用户表中 display_name 字段
  - 返回创建的专家数据

- 现货交易专家申请
  - 检查用户状态是否为正常状态，如果不是则返回异常
  - 检查用户是否完成 kyc 认证，如果没有则返回异常
  - 检查用户是否已经是现货交易专家，如果是则返回异常
  - 检查用户申请状态，如果是待审核则返回异常
  - 带单币种默认系统中的所有币种
  - 最小跟单金额从系统设置中获取
  - 根据等级条件匹配专家等级
  - 创建现货交易专家表记录（判断系统设置中“现货专家申请是否需要审核”，不需要审核时 status 直接设置为审核通过）
  - 更新用户表中 display_name 字段
  - 返回创建的专家数据