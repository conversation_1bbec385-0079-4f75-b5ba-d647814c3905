#### 6.1.35 合约-币对交易表现接口

**GET** `/api/copy/expert/contract/currency-performance`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "currency_symbol": "BTCUSDT", // 币对
        "trade_count": 25, // 交易次数
        "profit_count": 18, // 盈利次数
        "loss_count": 7, // 亏损次数
        "win_rate": "72.00", // 胜率 %
        "total_profit": "1500.00000000", // 总盈利
        "total_loss": "-300.00000000", // 总亏损
        "net_profit": "1200.00000000", // 净盈利
        "profit_rate": "15.50", // 收益率 %
        "avg_profit": "83.33000000", // 平均盈利
        "avg_loss": "-42.86000000", // 平均亏损
        "max_profit": "350.00000000", // 最大盈利
        "max_loss": "-120.00000000", // 最大亏损
        "profit_factor": "5.00" // 盈利因子
      },
      {
        "currency_symbol": "ETHUSDT",
        "trade_count": 18,
        "profit_count": 12,
        "loss_count": 6,
        "win_rate": "66.67",
        "total_profit": "800.00000000",
        "total_loss": "-200.00000000",
        "net_profit": "600.00000000",
        "profit_rate": "12.30",
        "avg_profit": "66.67000000",
        "avg_loss": "-33.33000000",
        "max_profit": "200.00000000",
        "max_loss": "-80.00000000",
        "profit_factor": "4.00"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 8,
      "total_pages": 1
    }
  }
}
```