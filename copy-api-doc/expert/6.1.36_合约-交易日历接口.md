#### 6.1.36 合约-交易日历接口

**GET** `/api/copy/expert/contract/trading-calendar`

**中间件**: TokenMiddleware

**Query 参数**：

- `year`: integer|required (年份)
- `month`: integer|required|min:1|max:12 (月份)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "calendar_data": [
      {
        "date": "2025-01-01", // 日期
        "trade_count": 3, // 交易次数
        "profit_count": 2, // 盈利次数
        "loss_count": 1, // 亏损次数
        "net_profit": "150.00000000", // 净盈利
        "profit_rate": "2.50" // 收益率 %
      },
      {
        "date": "2025-01-02",
        "trade_count": 2,
        "profit_count": 1,
        "loss_count": 1,
        "net_profit": "50.00000000",
        "profit_rate": "0.80"
      },
      {
        "date": "2025-01-03",
        "trade_count": 0,
        "profit_count": 0,
        "loss_count": 0,
        "net_profit": "0.00000000",
        "profit_rate": "0.00"
      }
    ]
  }
}
```