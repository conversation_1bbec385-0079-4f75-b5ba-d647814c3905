#### 6.1.44 合约-尊享模式成员分润比例调整接口

**PUT** `/api/copy/expert/contract/exclusive-members/{id}/profit-rate`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (尊享成员 ID)

**Body 参数**：

```json
{
  "profit_sharing_rate": "20.00" // 新的分润比例 %
}
```

**验证规则**:

- profit_sharing_rate: required|numeric|min:0|max:100

响应示例：

```json
{
  "code": 200,
  "message": "调整成功",
  "data": {
    "id": 1,
    "follower_user_id": 100,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "invite_id": 1,
    "profit_sharing_rate": "20.00",
    "adjustment_count": 2, // 调整次数
    "updated_at": "2025-01-01 18:00:00"
  }
}
```