#### 6.1.19 合约-交易专家列表展示状态获取接口

**GET** `/api/copy/expert/contract/display-status`

**中间件**: TokenMiddleware

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "display_status": true, // 展示状态：true-可展示，false-不可展示
    "rules": {
      "is_active": {
        "status": true, // 是否满足
        "description": "开启合约带单",
        "current_value": "是"
      },
      "currency_count": {
        "status": true,
        "description": "至少设置1个带单币对",
        "current_value": "已设置",
        "required_value": 1,
        "actual_value": 3
      },
      "order_count": {
        "status": false,
        "description": "至少存在1笔已平仓带单订单",
        "current_value": "否",
        "required_value": 1,
        "actual_value": 0
      },
      "copy_account_assets": {
        "status": true,
        "description": "合约带单账户资金 >= 100 USDT",
        "current_value": "2500.********",
        "required_value": "100.********"
      }
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 检查专家列表展示状态的各项条件：
  - is_active：检查是否开启合约带单
  - currency_count：检查是否至少设置 1 个带单币对
  - order_count：检查是否至少存在 1 笔已平仓带单订单
  - copy_account_assets：检查合约带单账户资金是否 >= 100 USDT
- 计算每个条件的状态：
  - status：true 表示满足条件，false 表示不满足
  - description：条件描述
  - current_value：当前实际值
  - required_value：要求的最小值
  - actual_value：实际数值（如适用）
- 综合判断展示状态：
  - 所有条件都满足时，display_status 为 true
  - 任一条件不满足时，display_status 为 false
- 返回详细的状态检查结果