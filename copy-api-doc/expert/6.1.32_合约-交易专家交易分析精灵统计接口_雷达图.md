#### 6.1.32 合约-交易专家交易分析精灵统计接口（雷达图）

**GET** `/api/copy/expert/contract/expert-analysis-radar`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "expert_score": "85.5", // 专家评分
    "expert_ranking": 15, // 专家排名
    "total_experts": 500, // 总专家数
    "radar_data": {
      "indicators": [
        {
          "name": "盈利因子",
          "max": 5,
          "description": "总盈利与总亏损比例"
        },
        {
          "name": "胜率",
          "max": 100,
          "description": "胜率百分比"
        },
        {
          "name": "平均盈亏比",
          "max": 5,
          "description": "平均盈利与平均亏损比例"
        }
      ],
      "series": [
        {
          "name": "专家平均能力参考值",
          "data": [2.5, 65.0, 1.8] // 对应indicators的值
        },
        {
          "name": "当前专家参数",
          "data": [3.2, 75.5, 2.3]
        }
      ]
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - 默认查询最近 30 天的数据
- 查询专家的交易数据并计算各项指标：
  - 专家评分：综合收益率、胜率、回撤等指标计算
  - 专家排名：在同等级专家中的排名
- 生成雷达图数据：
  - 定义雷达图指标（收益率、胜率、风控能力等）
  - 计算各指标的数值和满分值
  - 生成当前专家的雷达图数据
- 提供对比数据：
  - 同等级专家平均水平
  - 全平台专家平均水平
- 返回雷达图所需的完整数据格式