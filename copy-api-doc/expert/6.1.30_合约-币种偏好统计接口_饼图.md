#### 6.1.30 合约-币种偏好统计接口（饼图）

**GET** `/api/copy/expert/contract/currency-preference`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "currency_symbol": "BTCUSDT",
        "percentage": "45.5", // 占比 %
        "trade_count": 25, // 交易次数
        "total_volume": "50000.00000000" // 总交易量
      },
      {
        "currency_symbol": "ETHUSDT",
        "percentage": "35.2",
        "trade_count": 18,
        "total_volume": "38000.00000000"
      },
      {
        "currency_symbol": "ADAUSDT",
        "percentage": "19.3",
        "trade_count": 12,
        "total_volume": "20000.00000000"
      }
    ]
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - 默认查询最近 30 天的数据
- 查询专家的历史合约交易数据：
  - 统计各币种的交易次数和交易量
  - 计算各币种的交易占比
- 计算币种偏好统计：
  - 按交易量计算各币种的百分比占比
  - 统计各币种的交易笔数
  - 计算各币种的总交易量
- 生成饼图数据：
  - 按占比从高到低排序
  - 格式化为饼图所需的数据格式
  - 包含币种名称、占比、交易次数、交易量
- 返回币种偏好饼图数据