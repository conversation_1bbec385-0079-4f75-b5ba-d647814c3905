#### 6.1.26 合约-带单币对接口

**GET** `/api/copy/expert/contract/currencies`

**中间件**: TokenMiddleware

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "symbol": "BTCUSDT", // 交易对symbol
        "base_currency": "BTC",
        "quote_currency": "USDT",
        "is_enabled": true // 是否启用带单
      },
      {
        "id": 2,
        "symbol": "ETHUSDT",
        "base_currency": "ETH",
        "quote_currency": "USDT",
        "is_enabled": true
      },
      {
        "id": 3,
        "symbol": "ADAUSDT",
        "base_currency": "ADA",
        "quote_currency": "USDT",
        "is_enabled": false
      }
    ]
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 查询系统支持的合约交易币种列表
- 获取专家当前的带单币种配置：
  - 查询专家已启用的带单币种
  - 获取币种的启用/禁用状态
- 组装币种信息：
  - 包含币种基本信息（symbol、base_currency、quote_currency）
  - 标记每个币种的启用状态
  - 按币种名称排序
- 返回完整的币种列表和状态信息
- 支持专家查看和管理带单币种配置