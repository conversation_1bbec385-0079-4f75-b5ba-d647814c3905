#### 6.1.20 合约-个人设置接口

**PUT** `/api/copy/expert/contract/settings`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "is_active": 1, // 是否开启合约带单：1-是，0-否
  "show_total_assets": 1, // 是否公共展示总资产：1-是，0-否
  "show_expert_rating": 1, // 是否展示专家评分及排名：1-是，0-否
  "introduction": "专业合约交易，稳健收益", // 带单介绍
  "position_protection": 1, // 未结仓位保护：1-开启，0-关闭
  "min_follow_amount": "100.00000000", // 最小跟单金额
  "recommend_params": {
    "fixed_amount": {
      "amount": "100.00",
      "stop_loss_rate": "10.00",
      "take_profit_rate": "20.00",
      "max_copy_amount": "1000.00"
    },
    "multiplier": {
      "multiplier": "0.1",
      "stop_loss_rate": "10.00",
      "take_profit_rate": "20.00",
      "max_copy_amount": "1000.00"
    }
  },
  "currency_ids": [1, 2, 3], // 币种ID数组
  "profit_sharing_rate": "10.00" // 分润比例 %
}
```

**验证规则**:

- is_active: required|integer|in:0,1
- show_total_assets: required|integer|in:0,1
- show_expert_rating: required|integer|in:0,1
- introduction: required|string|max:500
- position_protection: required|integer|in:0,1
- min_follow_amount: required|numeric|min:0
- recommend_params: nullable|array
- currency_ids: required|array|min:1
- currency_ids.\*: required|integer|exists:currency,id
- profit_sharing_rate: required|numeric|min:0|max:100

响应示例：

```json
{
  "code": 200,
  "message": "设置成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "is_active": 1,
    "show_total_assets": 1,
    "show_expert_rating": 1,
    "introduction": "专业合约交易，稳健收益",
    "position_protection": 1,
    "min_follow_amount": "100.00000000",
    "recommend_params": {
      "fixed_amount": {
        "amount": "100.00",
        "stop_loss_rate": "10.00",
        "take_profit_rate": "20.00",
        "max_copy_amount": "1000.00"
      },
      "multiplier": {
        "multiplier": "0.1",
        "stop_loss_rate": "10.00",
        "take_profit_rate": "20.00",
        "max_copy_amount": "1000.00"
      }
    },
    "currency_ids": [1, 2, 3],
    "profit_sharing_rate": "10.00",
    "updated_at": "2025-01-01 15:30:00"
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 验证参数有效性：
  - 检查分润比例是否在允许范围内
  - 验证最小跟单金额是否符合系统要求
  - 检查币种 ID 是否有效且支持合约交易
  - 验证推荐参数的合理性
- 检查业务规则：
  - 验证专家等级是否支持设置的最大分润比例
  - 检查是否至少选择一个带单币种
  - 验证推荐参数中的止损止盈比例合理性
- 更新专家设置：
  - 更新基本设置（开启状态、展示设置等）
  - 更新带单介绍和风控设置
  - 更新推荐参数和币种配置
  - 更新分润比例
- 记录设置变更日志
- 返回更新后的设置信息