#### 6.1.43 现货-币对交易表现接口

**GET** `/api/copy/expert/spot/currency-performance`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "currency_symbol": "BTCUSDT", // 币对
        "profit_order_count": 15, // 盈利订单数
        "loss_order_count": 5, // 亏损订单数
        "win_rate": "75.00", // 胜率 %
        "total_profit": "800.00000000", // 总盈利
        "total_loss": "-200.00000000", // 总亏损
        "net_profit": "600.00000000" // 净盈利
      },
      {
        "currency_symbol": "ETHUSDT",
        "profit_order_count": 12,
        "loss_order_count": 3,
        "win_rate": "80.00",
        "total_profit": "600.00000000",
        "total_loss": "-100.00000000",
        "net_profit": "500.00000000"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 6,
      "total_pages": 1
    }
  }
}
```