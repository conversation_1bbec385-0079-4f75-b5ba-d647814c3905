#### 6.1.17 现货-我的跟随者接口

**GET** `/api/copy/expert/spot/followers`

**中间件**: TokenMiddleware

**Query 参数**：

- `asset_filter`: string|nullable|in:all,zero,low (资产过滤：all-所有跟单者，zero-资产为 0，low-资产低于 100USDT)
- `exclusive_filter`: string|nullable|in:all,exclusive,normal (尊享模式：all-全部，exclusive-尊享模式，normal-普通模式)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "follower_user": {
          "id": 100,
          "username": "follower001",
          "display_name": "跟单用户001",
          "avatar": "https://test.com/avatar.png"
        },
        "spot_account_balance": "1800.********", // 现货账户资金
        "total_copy_count": 18, // 累计跟单笔数
        "copy_profit": "220.********", // 跟单收益
        "copy_net_profit": "202.********", // 跟单净利润
        "profit_sharing_rate": "8.00", // 分润比例 %
        "is_exclusive": 0 // 是否尊享模式：1-是，0-否
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 65,
      "total_pages": 4
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 解析筛选条件：
  - asset_filter：按资产金额筛选跟随者
  - exclusive_filter：按尊享模式筛选
- 查询专家的现货跟随者列表：
  - 查询正在跟单该现货专家的用户
  - 关联查询跟随者的用户信息
  - 获取跟随者的现货跟单配置信息
- 计算跟随者统计数据：
  - 现货账户资金余额
  - 累计现货跟单笔数统计
  - 现货跟单收益和净利润计算
  - 分润比例（普通模式或尊享模式）
- 应用筛选条件：
  - 按资产金额过滤（全部/资产为 0/资产低于 100USDT）
  - 按尊享模式状态过滤
- 分页返回现货跟随者列表数据