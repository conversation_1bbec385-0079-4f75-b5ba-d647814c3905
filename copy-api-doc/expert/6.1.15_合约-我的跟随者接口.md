#### 6.1.15 合约-我的跟随者接口

**GET** `/api/copy/expert/contract/followers`

**中间件**: TokenMiddleware

**Query 参数**：

- `asset_filter`: string|nullable|in:all,zero,low (资产过滤：all-所有跟单者，zero-资产为 0，low-资产低于 50USDT)
- `mode_filter`: string|nullable|in:all,smart,multi (跟单模式：all-全部，smart-智能比例，multi-多元探索)
- `exclusive_filter`: string|nullable|in:all,exclusive,normal (尊享模式：all-全部，exclusive-尊享模式，normal-普通模式)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "follower_user": {
          "id": 100,
          "username": "follower001",
          "display_name": "跟单用户001",
          "avatar": "https://test.com/avatar.png"
        },
        "copy_account_balance": "2500.********", // 合约跟单账户资金
        "total_copy_count": 25, // 累计跟单笔数
        "copy_profit": "350.********", // 跟单收益
        "copy_net_profit": "315.********", // 跟单净利润
        "profit_sharing_rate": "10.00", // 分润比例 %
        "mode": 1, // 跟单模式：1-智能比例，2-多元探索
        "is_exclusive": 0 // 是否尊享模式：1-是，0-否
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 85,
      "total_pages": 5
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 解析筛选条件：
  - asset_filter：按资产金额筛选跟随者
  - mode_filter：按跟单模式筛选（智能比例/多元探索）
  - exclusive_filter：按尊享模式筛选
- 查询专家的跟随者列表：
  - 查询正在跟单该专家的用户
  - 关联查询跟随者的用户信息
  - 获取跟随者的跟单配置信息
- 计算跟随者统计数据：
  - 合约跟单账户资金余额
  - 累计跟单笔数统计
  - 跟单收益和净利润计算
  - 分润比例（普通模式或尊享模式）
- 应用筛选条件：
  - 按资产金额过滤（全部/资产为 0/资产低于 50USDT）
  - 按跟单模式过滤
  - 按尊享模式状态过滤
- 分页返回跟随者列表数据