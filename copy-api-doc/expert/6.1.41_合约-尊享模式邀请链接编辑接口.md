#### 6.1.41 合约-尊享模式邀请链接编辑接口

**PUT** `/api/copy/expert/contract/exclusive-invites/{id}`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (邀请链接 ID)

**Body 参数**：

```json
{
  "name": "VIP客户邀请（更新）", // 邀请链接名称
  "profit_sharing_rate": "18.00", // 分润比例 %
  "max_use_count": 15, // 最大使用次数，null表示无限制
  "expire_at": "2025-12-31 23:59:59", // 过期时间，null表示永不过期
  "is_active": 1 // 是否启用：1-启用，0-禁用
}
```

**验证规则**:

- name: required|string|max:100
- profit_sharing_rate: required|numeric|min:0|max:100
- max_use_count: nullable|integer|min:1
- expire_at: nullable|date_format:Y-m-d H:i:s
- is_active: required|integer|in:0,1

响应示例：

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "name": "VIP客户邀请（更新）",
    "invite_code": "INV123456789",
    "invite_url": "https://example.com/copy/invite/INV123456789",
    "profit_sharing_rate": "18.00",
    "max_use_count": 15,
    "used_count": 3,
    "expire_at": "2025-12-31 23:59:59",
    "is_active": 1,
    "updated_at": "2025-01-01 17:00:00"
  }
}
```