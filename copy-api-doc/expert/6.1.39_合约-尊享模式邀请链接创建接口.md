#### 6.1.39 合约-尊享模式邀请链接创建接口

**POST** `/api/copy/expert/contract/exclusive-invites`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "name": "VIP客户邀请", // 邀请链接名称
  "profit_sharing_rate": "15.00", // 分润比例 %
  "max_use_count": 10, // 最大使用次数，null表示无限制
  "expire_at": "2025-12-31 23:59:59" // 过期时间，null表示永不过期
}
```

**验证规则**:

- name: required|string|max:100
- profit_sharing_rate: required|numeric|min:0|max:100
- max_use_count: nullable|integer|min:1
- expire_at: nullable|date_format:Y-m-d H:i:s

响应示例：

```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "name": "VIP客户邀请",
    "invite_code": "INV123456789", // 邀请码
    "invite_url": "https://example.com/copy/invite/INV123456789", // 邀请链接
    "profit_sharing_rate": "15.00",
    "max_use_count": 10,
    "used_count": 0,
    "expire_at": "2025-12-31 23:59:59",
    "is_active": 1,
    "created_at": "2025-01-01 16:00:00"
  }
}
```