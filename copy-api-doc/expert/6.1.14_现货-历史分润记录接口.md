#### 6.1.14 现货-历史分润记录接口

**GET** `/api/copy/expert/spot/profit-sharing/history`

**中间件**: TokenMiddleware

**Query 参数**：

- `type`: required|string|in:by_time,by_follower (按时间/按跟随者)
- `days`: integer|nullable (天数过滤，仅 type=by_follower 时有效)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例（按时间）：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "date": "2025-01-01", // 时间
        "profit_sharing_amount": "95.50000000" // 分润数量
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 25,
      "total_pages": 2
    }
  }
}
```

**接口逻辑**：

- 验证当前用户是否为现货交易专家
- 根据 type 参数确定查询类型：
  - by_time：按时间维度查询现货分润记录
  - by_follower：按跟随者维度查询现货分润记录
- 解析时间过滤条件：
  - 根据 days 参数或自定义日期范围确定查询时间
  - by_follower 类型支持 days 过滤
- 查询现货分润历史记录：
  - by_time：按日期聚合现货分润数据，显示每日分润金额
  - by_follower：按跟随者聚合现货分润数据，显示每个跟随者的分润贡献
- 计算现货分润统计：
  - 分润金额、分润比例、分润来源
  - 跟随者信息和贡献度
- 分页返回现货分润历史记录