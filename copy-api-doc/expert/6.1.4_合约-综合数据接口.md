#### 6.1.4 合约-综合数据接口

**GET** `/api/copy/expert/contract/overview`

**中间件**: TokenMiddleware

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "current_follower_count": 85, // 当前跟单人数
    "max_follower_count": 100, // 交易专家等级对应最大跟单人数
    "aum": "50000.00000000", // 资产管理规模（AUM）
    "total_assets": "2500.00000000", // 总资产（合约跟单账户资金）
    "last_trade_time": "2025-01-01 12:30:00", // 最近交易时间
    "profit_sharing_rate": "10.00", // 分润比例 %
    "fan_count": 200 // 粉丝数量
  }
}
```

**接口逻辑**：

- 验证当前用户是否为合约交易专家
- 查询当前跟单人数：统计正在跟单该专家的用户数量
- 获取最大跟单人数：从专家等级配置中获取
- 计算资产管理规模（AUM）：所有跟单者的跟单账户资金总和
- 获取总资产：专家合约跟单账户当前资金
- 查询最近交易时间：最新一笔带单订单的创建时间
- 获取分润比例：专家当前设置的分润比例
- 统计粉丝数量：关注该专家的用户总数
- 返回综合数据