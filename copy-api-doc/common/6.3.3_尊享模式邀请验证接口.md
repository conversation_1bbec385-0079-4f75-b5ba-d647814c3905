#### 6.3.3 尊享模式邀请验证接口

**GET** `/api/copy/common/exclusive-invite/{code}/verify`

**中间件**: TokenMiddleware

**Path 参数**：

- `code`: required|string (邀请码)

响应示例（有效邀请码）：

```json
{
  "code": 200,
  "message": "邀请码有效",
  "data": {
    "id": 1,
    "expert": {
      "id": 1,
      "user": {
        "id": 1,
        "username": "expert001",
        "display_name": "专业交易员",
        "avatar": "https://test.com/avatar.png"
      },
      "level": {
        "id": 1,
        "level": 1,
        "name": "初级专家",
        "icon": "https://test.com/icon.png"
      },
      "type": 1 // 专家类型：1-合约专家，2-现货专家
    },
    "name": "VIP客户邀请",
    "profit_sharing_rate": "15.00", // 分润比例 %
    "max_use_count": 10, // 最大使用次数
    "used_count": 3, // 已使用次数
    "expire_at": "2025-12-31 23:59:59", // 过期时间
    "is_valid": true // 是否有效
  }
}
```

响应示例（无效邀请码）：

```json
{
  "code": 400,
  "message": "邀请码无效或已过期",
  "data": {
    "is_valid": false
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 验证邀请码格式的有效性：
  - 检查邀请码是否为空
  - 验证邀请码格式是否正确
- 查询邀请码信息：
  - 根据邀请码查询对应的邀请记录
  - 获取邀请码的基本信息（名称、分润比例等）
  - 获取邀请码的使用限制（最大使用次数、已使用次数）
  - 获取邀请码的有效期信息
- 验证邀请码的有效性：
  - 检查邀请码是否存在
  - 验证邀请码是否在有效期内
  - 检查邀请码使用次数是否已达上限
  - 验证专家是否处于可邀请状态
- 获取专家信息：
  - 查询邀请码对应的专家信息
  - 获取专家的用户信息和等级信息
  - 获取专家类型（合约或现货）
- 返回验证结果：
  - 有效时返回邀请码详细信息
  - 无效时返回错误信息和原因
