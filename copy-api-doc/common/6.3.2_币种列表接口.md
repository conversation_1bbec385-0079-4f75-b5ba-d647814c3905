#### 6.3.2 币种列表接口

**GET** `/api/copy/common/currencies`

**中间件**: TokenMiddleware

**Query 参数**：

- `type`: string|nullable|in:contract,spot (交易类型：contract-合约，spot-现货，为空表示全部)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "symbol": "BTCUSDT",
        "base_currency": "BTC",
        "quote_currency": "USDT",
        "type": "both", // 类型：contract-仅合约，spot-仅现货，both-合约现货都支持
        "is_active": true
      },
      {
        "id": 2,
        "symbol": "ETHUSDT",
        "base_currency": "ETH",
        "quote_currency": "USDT",
        "type": "both",
        "is_active": true
      },
      {
        "id": 3,
        "symbol": "ADAUSDT",
        "base_currency": "ADA",
        "quote_currency": "USDT",
        "type": "spot",
        "is_active": true
      }
    ]
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 根据 type 参数过滤币种类型：
  - contract：只返回支持合约交易的币种
  - spot：只返回支持现货交易的币种
  - both：返回同时支持合约和现货的币种
  - 不指定：返回所有币种
- 查询系统支持的交易币种列表：
  - 获取币种基本信息（symbol、base_currency、quote_currency）
  - 获取币种的交易类型支持情况
  - 获取币种的启用状态
- 过滤活跃币种：
  - 只返回 is_active 为 true 的币种
  - 按币种名称排序
- 格式化币种数据：
  - 包含币种 ID 和交易对符号
  - 包含基础货币和计价货币
  - 包含支持的交易类型
  - 包含启用状态
- 返回完整的币种列表数据
- 支持前端币种选择和配置