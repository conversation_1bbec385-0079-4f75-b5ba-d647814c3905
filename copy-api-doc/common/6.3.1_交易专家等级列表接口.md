#### 6.3.1 交易专家等级列表接口

**GET** `/api/copy/common/expert-levels`

**中间件**: TokenMiddleware

**Query 参数**：

- `type`: integer|nullable|in:1,2 (专家类型：1-合约专家，2-现货专家，为空表示全部)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "type": 1,
        "level": 1,
        "name": "初级专家",
        "icon": "https://test.com/icon1.png",
        "condition_amount": "1000.00000000", // 条件：资产金额
        "condition_follow_amount": "5000.00000000", // 条件：跟单金额
        "condition_follow_count": 10, // 条件：跟单人数
        "max_follow_count": 100, // 最大跟单人数
        "max_profit_rate": "10.00" // 最大分润比例 %
      },
      {
        "id": 2,
        "type": 1,
        "level": 2,
        "name": "中级专家",
        "icon": "https://test.com/icon2.png",
        "condition_amount": "5000.00000000",
        "condition_follow_amount": "20000.00000000",
        "condition_follow_count": 50,
        "max_follow_count": 200,
        "max_profit_rate": "15.00"
      },
      {
        "id": 3,
        "type": 1,
        "level": 3,
        "name": "高级专家",
        "icon": "https://test.com/icon3.png",
        "condition_amount": "20000.00000000",
        "condition_follow_amount": "100000.00000000",
        "condition_follow_count": 200,
        "max_follow_count": 500,
        "max_profit_rate": "20.00"
      }
    ]
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 根据 type 参数过滤专家等级类型：
  - 如果指定 type，则只返回对应类型的等级（1-合约，2-现货）
  - 如果不指定 type，则返回所有类型的等级
- 查询系统配置的专家等级列表：
  - 按等级从低到高排序
  - 获取等级的基本信息（名称、图标等）
  - 获取等级的升级条件（资金、跟单金额、跟单人数）
  - 获取等级的权益（最大跟单人数、最大分润比例）
- 格式化等级数据：
  - 包含等级 ID、类型、等级数值
  - 包含等级名称和图标
  - 包含升级条件和权益信息
- 返回完整的等级列表数据
- 支持前端展示等级体系和升级条件