#### 6.2.7 合约-交易专家详情接口

**GET** `/api/copy/user/contract/expert/{id}/detail`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "user": {
      "id": 1,
      "username": "expert001",
      "display_name": "专业交易员",
      "avatar": "https://test.com/avatar.png"
    },
    "level": {
      "id": 1,
      "level": 1,
      "name": "初级专家",
      "icon": "https://test.com/icon.png"
    },
    "introduction": "专业合约交易，稳健收益",
    "entry_days": 180, // 入驻天数
    "current_follower_count": 85, // 当前跟单人数
    "max_follower_count": 100, // 最大跟单人数
    "fan_count": 200, // 粉丝数量
    "is_followed": true, // 是否已关注
    "is_copying": false, // 是否正在跟单
    "show_total_assets": 1, // 是否展示总资产
    "show_expert_rating": 1, // 是否展示专家评分
    "total_assets": "2500.00000000", // 总资产（仅当show_total_assets=1时显示）
    "expert_rating": "85.50", // 专家评分（仅当show_expert_rating=1时显示）
    "expert_ranking": 15, // 专家排名（仅当show_expert_rating=1时显示）
    "profit_sharing_rate": "10.00", // 分润比例 %
    "min_follow_amount": "100.00000000", // 最小跟单金额
    "recommend_params": {
      "fixed_amount": {
        "amount": "100.00",
        "stop_loss_rate": "10.00",
        "take_profit_rate": "20.00",
        "max_copy_amount": "1000.00"
      },
      "multiplier": {
        "multiplier": "0.1",
        "stop_loss_rate": "10.00",
        "take_profit_rate": "20.00",
        "max_copy_amount": "1000.00"
      }
    },
    "currency_symbols": ["BTCUSDT", "ETHUSDT", "ADAUSDT"], // 带单币种
    "exclusive_mode": 0 // 是否开启尊享模式
  }
}
```