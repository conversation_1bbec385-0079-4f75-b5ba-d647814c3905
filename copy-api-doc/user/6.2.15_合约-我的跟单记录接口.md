#### 6.2.15 合约-我的跟单记录接口

**GET** `/api/copy/user/contract/my-copy-orders`

**中间件**: TokenMiddleware

**Query 参数**：

- `expert_id`: integer|nullable (交易专家 ID)
- `status`: string|nullable|in:open,closed (订单状态：open-持仓中，closed-已平仓)
- `currency_id`: integer|nullable (币种 ID)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "expert": {
          "id": 1,
          "user": {
            "id": 1,
            "username": "expert001",
            "display_name": "专业交易员",
            "avatar": "https://test.com/avatar.png"
          }
        },
        "currency_symbol": "BTCUSDT", // 币种
        "direction": "long", // 方向
        "leverage": 10, // 杠杆
        "open_time": "2025-01-01 10:00:00", // 开仓时间
        "close_time": "2025-01-01 14:30:00", // 平仓时间
        "entry_price": "44800.00000000", // 入场价
        "exit_price": "46200.00000000", // 出场价
        "quantity": "0.05000000", // 数量
        "margin": "225.00000000", // 保证金
        "realized_pnl": "70.00000000", // 已实现盈亏
        "profit_sharing_fee": "7.00000000", // 分润费用
        "net_profit": "63.00000000", // 净利润
        "status": "closed", // 状态：open-持仓中，closed-已平仓
        "copy_order_id": "COPY123456789" // 跟单订单编号
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 45,
      "total_pages": 3
    }
  }
}
```