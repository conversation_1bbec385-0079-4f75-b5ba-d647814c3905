#### 6.2.13 合约-跟单配置修改接口

**PUT** `/api/copy/user/contract/copy-settings/{id}`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (跟单配置 ID)

**Body 参数**：

```json
{
  "mode": 2, // 跟单模式：1-智能比例，2-多元探索
  "copy_type": "multiplier", // 跟单类型：fixed_amount-固定金额，multiplier-跟单倍率
  "amount": null, // 固定金额
  "multiplier": "0.3", // 跟单倍率
  "stop_loss_rate": "12.00", // 止损比例 %
  "take_profit_rate": "25.00", // 止盈比例 %
  "max_copy_amount": "3000.00000000", // 最大跟单金额
  "currency_ids": [1, 2], // 跟单币种ID数组
  "is_active": 1 // 是否启用：1-启用，0-停用
}
```

**验证规则**:

- mode: required|integer|in:1,2
- copy_type: required|string|in:fixed_amount,multiplier
- amount: required_if:copy_type,fixed_amount|numeric|min:0
- multiplier: required_if:copy_type,multiplier|numeric|min:0|max:1
- stop_loss_rate: required|numeric|min:0|max:100
- take_profit_rate: required|numeric|min:0|max:100
- max_copy_amount: required|numeric|min:0
- currency_ids: required|array|min:1
- currency_ids.\*: required|integer|exists:currency,id
- is_active: required|integer|in:0,1

响应示例：

```json
{
  "code": 200,
  "message": "修改成功",
  "data": {
    "id": 1,
    "user_id": 100,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "mode": 2,
    "copy_type": "multiplier",
    "amount": null,
    "multiplier": "0.3",
    "stop_loss_rate": "12.00",
    "take_profit_rate": "25.00",
    "max_copy_amount": "3000.00000000",
    "currency_ids": [1, 2],
    "is_active": 1,
    "updated_at": "2025-01-01 20:00:00"
  }
}
```