#### 6.2.12 合约-交易专家交易分析精灵统计接口（雷达图）

**GET** `/api/copy/user/contract/expert/{id}/expert-analysis-radar`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "expert_score": "85.5", // 专家评分
    "expert_ranking": 15, // 专家排名
    "radar_data": {
      "indicators": [
        {
          "name": "盈利因子",
          "max": 5
        },
        {
          "name": "胜率",
          "max": 100
        },
        {
          "name": "平均盈亏比",
          "max": 5
        }
      ],
      "series": [
        {
          "name": "专家平均能力参考值",
          "data": [2.5, 65.0, 1.8]
        },
        {
          "name": "当前专家参数",
          "data": [3.2, 75.5, 2.3]
        }
      ]
    }
  }
}
```