#### 6.2.10 合约-跟单设置接口

**POST** `/api/copy/user/contract/copy-settings`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "expert_id": 1, // 交易专家ID
  "mode": 1, // 跟单模式：1-智能比例，2-多元探索
  "copy_type": "fixed_amount", // 跟单类型：fixed_amount-固定金额，multiplier-跟单倍率
  "amount": "500.********", // 固定金额（copy_type=fixed_amount时必填）
  "multiplier": "0.2", // 跟单倍率（copy_type=multiplier时必填）
  "stop_loss_rate": "10.00", // 止损比例 %
  "take_profit_rate": "20.00", // 止盈比例 %
  "max_copy_amount": "2000.********", // 最大跟单金额
  "currency_ids": [1, 2, 3], // 跟单币种ID数组
  "transfer_from_account": 1, // 划转资金来源账户
  "transfer_amount": "1000.********" // 划转金额
}
```

**验证规则**:

- expert_id: required|integer|exists:copy_contract_experts,id
- mode: required|integer|in:1,2
- copy_type: required|string|in:fixed_amount,multiplier
- amount: required_if:copy_type,fixed_amount|numeric|min:0
- multiplier: required_if:copy_type,multiplier|numeric|min:0|max:1
- stop_loss_rate: required|numeric|min:0|max:100
- take_profit_rate: required|numeric|min:0|max:100
- max_copy_amount: required|numeric|min:0
- currency_ids: required|array|min:1
- currency_ids.\*: required|integer|exists:currency,id
- transfer_from_account: required|integer
- transfer_amount: required|numeric|min:0

响应示例：

```json
{
  "code": 200,
  "message": "跟单设置成功",
  "data": {
    "id": 1,
    "user_id": 100,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "mode": 1,
    "copy_type": "fixed_amount",
    "amount": "500.********",
    "multiplier": null,
    "stop_loss_rate": "10.00",
    "take_profit_rate": "20.00",
    "max_copy_amount": "2000.********",
    "currency_ids": [1, 2, 3],
    "is_active": 1,
    "created_at": "2025-01-01 19:00:00"
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 验证专家 ID 的有效性：
  - 检查专家是否存在且为合约专家
  - 验证专家是否处于可跟单状态
  - 检查专家的跟单人数是否已满
- 验证跟单参数的有效性：
  - 检查跟单模式（智能比例/多元探索）
  - 验证跟单类型和金额/倍率设置
  - 检查止损止盈比例的合理性
  - 验证最大跟单金额限制
  - 检查币种 ID 的有效性
- 检查用户资金状况：
  - 验证合约跟单账户余额是否充足
  - 检查是否满足最小跟单金额要求
- 创建或更新跟单配置：
  - 如果已存在配置则更新，否则创建新配置
  - 设置跟单参数和风控设置
  - 激活跟单状态
- 记录跟单关系和操作日志
- 返回跟单配置结果