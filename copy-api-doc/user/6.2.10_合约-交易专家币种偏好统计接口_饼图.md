#### 6.2.10 合约-交易专家币种偏好统计接口（饼图）

**GET** `/api/copy/user/contract/expert/{id}/currency-preference`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "chart_data": [
      {
        "currency_symbol": "BTCUSDT",
        "percentage": "45.5" // 占比 %
      },
      {
        "currency_symbol": "ETHUSDT",
        "percentage": "35.2"
      },
      {
        "currency_symbol": "ADAUSDT",
        "percentage": "19.3"
      }
    ]
  }
}
```