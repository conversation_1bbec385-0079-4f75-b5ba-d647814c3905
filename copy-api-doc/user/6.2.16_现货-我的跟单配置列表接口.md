#### 6.2.16 现货-我的跟单配置列表接口

**GET** `/api/copy/user/spot/my-copy-settings`

**中间件**: TokenMiddleware

**Query 参数**：

- `status`: string|nullable|in:active,inactive (状态过滤：active-启用，inactive-停用)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "expert": {
          "id": 1,
          "user": {
            "id": 1,
            "username": "spot_expert001",
            "display_name": "现货专家",
            "avatar": "https://test.com/avatar.png"
          },
          "level": {
            "id": 1,
            "level": 1,
            "name": "初级专家",
            "icon": "https://test.com/icon.png"
          }
        },
        "copy_type": "fixed_amount",
        "amount": "300.********",
        "multiplier": null,
        "stop_loss_rate": "8.00",
        "take_profit_rate": "15.00",
        "max_copy_amount": "1500.********",
        "currency_symbols": ["BTCUSDT", "ETHUSDT"],
        "is_active": 1,
        "spot_account_balance": "1200.********", // 现货账户余额
        "total_copy_count": 18, // 累计跟单笔数
        "copy_profit": "220.********", // 跟单收益
        "copy_net_profit": "202.********", // 跟单净利润
        "created_at": "2025-01-01 19:00:00"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 2,
      "total_pages": 1
    }
  }
}
```