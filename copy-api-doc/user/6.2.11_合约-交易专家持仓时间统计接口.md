#### 6.2.11 合约-交易专家持仓时间统计接口

**GET** `/api/copy/user/contract/expert/{id}/position-time-stats`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤)
- `start_date`: date|nullable (开始日期，格式：Y-m-d)
- `end_date`: date|nullable (结束日期，格式：Y-m-d)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "average_hold_time": "4.5", // 平均持仓时间（小时）
    "max_hold_time": "24.8", // 最长持仓时间（小时）
    "chart_data": [
      {
        "time_range": "0-5小时", // 持仓时段
        "profit_count": 15, // 盈利笔数
        "loss_count": 3 // 亏损笔数
      },
      {
        "time_range": "5-10小时",
        "profit_count": 12,
        "loss_count": 5
      }
    ]
  }
}
```