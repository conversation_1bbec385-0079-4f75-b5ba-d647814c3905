#### 6.2.4 现货-交易专家列表接口

**GET** `/api/copy/user/spot/experts`

**中间件**: TokenMiddleware

**Query 参数**：

- `days`: integer|nullable|in:0,7,30,90,180 (时间过滤：0-全部，7-7 天，30-30 天，90-90 天，180-180 天)
- `profit_min`: numeric|nullable (收益最小值，与 days 相关)
- `profit_rate_min`: numeric|nullable (收益率最小值 %，与 days 相关)
- `profit_rate_max`: numeric|nullable (收益率最大值 %，与 days 相关)
- `win_rate_min`: numeric|nullable (胜率最小值 %，与 days 相关)
- `follower_profit_min`: numeric|nullable (跟单者收益最小值，与 days 相关)
- `entry_days_min`: integer|nullable (入驻天数最小值)
- `aum_min`: numeric|nullable (资产管理规模最小值)
- `level_ids`: array|nullable (专家等级 ID 数组)
- `currency_ids`: array|nullable (跟单币种 ID 数组)
- `hide_full`: boolean|nullable (隐藏满员专家)
- `assets_public`: boolean|nullable (仅显示资产公开的专家)
- `is_followed`: boolean|nullable (仅显示已关注的专家)
- `sort_by`: string|nullable|in:profit,win_rate,profit_rate,follower_profit,aum (排序字段)
- `sort_order`: string|nullable|in:asc,desc (排序方向)
- `page`: integer|min:1 (页码)
- `page_size`: integer|min:1|max:100 (每页数量)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "user": {
          "id": 1,
          "username": "spot_expert001",
          "display_name": "现货专家",
          "avatar": "https://test.com/avatar.png"
        },
        "level": {
          "id": 1,
          "level": 1,
          "name": "初级专家",
          "icon": "https://test.com/icon.png"
        },
        "current_follower_count": 65, // 当前跟单人数
        "max_follower_count": 100, // 最大跟单人数
        "statistics": {
          "profit_rate": "12.30", // 收益率 %（与days相关）
          "total_profit": "800.00000000", // 总收益（与days相关）
          "win_rate": "68.50", // 胜率 %（与days相关）
          "follower_profit": "8000.00000000", // 跟单者收益（与days相关）
          "aum": "30000.00000000" // 资产管理规模
        },
        "profit_rate_chart": [
          // 收益率统计折线图（与days相关）
          {
            "date": "2025-01-01",
            "profit_rate": "1.8"
          },
          {
            "date": "2025-01-02",
            "profit_rate": "2.5"
          }
        ],
        "is_followed": false, // 是否已关注
        "show_total_assets": 1, // 是否展示总资产
        "profit_sharing_rate": "8.00" // 分润比例 %
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 120,
      "total_pages": 6
    }
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 根据筛选条件构建查询：
  - 时间过滤：根据 days 参数过滤专家数据统计时间范围
  - 收益过滤：根据 profit_min、profit_rate_min/max 等参数过滤
  - 胜率过滤：根据 win_rate_min 参数过滤
  - 其他条件：跟单者收益、入驻天数、AUM、等级、币种等
- 查询符合条件的现货交易专家列表
- 获取专家基本信息：用户信息、等级信息
- 计算专家统计数据：收益率、总收益、胜率、跟单者收益、AUM 等
- 生成收益率统计折线图数据
- 检查当前用户是否已关注该专家
- 根据排序条件对结果进行排序
- 分页返回现货专家列表数据