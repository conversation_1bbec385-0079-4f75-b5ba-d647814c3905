#### 6.2.11 现货-跟单设置接口

**POST** `/api/copy/user/spot/copy-settings`

**中间件**: TokenMiddleware

**Body 参数**：

```json
{
  "expert_id": 1, // 交易专家ID
  "copy_type": "fixed_amount", // 跟单类型：fixed_amount-固定金额，multiplier-跟单倍率
  "amount": "300.********", // 固定金额（copy_type=fixed_amount时必填）
  "multiplier": "0.15", // 跟单倍率（copy_type=multiplier时必填）
  "stop_loss_rate": "8.00", // 止损比例 %
  "take_profit_rate": "15.00", // 止盈比例 %
  "max_copy_amount": "1500.********", // 最大跟单金额
  "currency_ids": [1, 2, 3], // 跟单币种ID数组
  "transfer_from_account": 1, // 划转资金来源账户
  "transfer_amount": "800.********" // 划转金额
}
```

**验证规则**:

- expert_id: required|integer|exists:copy_spot_experts,id
- copy_type: required|string|in:fixed_amount,multiplier
- amount: required_if:copy_type,fixed_amount|numeric|min:0
- multiplier: required_if:copy_type,multiplier|numeric|min:0|max:1
- stop_loss_rate: required|numeric|min:0|max:100
- take_profit_rate: required|numeric|min:0|max:100
- max_copy_amount: required|numeric|min:0
- currency_ids: required|array|min:1
- currency_ids.\*: required|integer|exists:currency,id
- transfer_from_account: required|integer
- transfer_amount: required|numeric|min:0

响应示例：

```json
{
  "code": 200,
  "message": "跟单设置成功",
  "data": {
    "id": 1,
    "user_id": 100,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopySpotExpert",
    "copy_type": "fixed_amount",
    "amount": "300.********",
    "multiplier": null,
    "stop_loss_rate": "8.00",
    "take_profit_rate": "15.00",
    "max_copy_amount": "1500.********",
    "currency_ids": [1, 2, 3],
    "is_active": 1,
    "created_at": "2025-01-01 19:00:00"
  }
}
```