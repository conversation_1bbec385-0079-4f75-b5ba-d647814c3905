#### 6.2.9 关注/取消关注交易专家接口

**POST** `/api/copy/user/expert/{id}/follow`

**中间件**: TokenMiddleware

**Path 参数**：

- `id`: required|integer (交易专家 ID)

**Body 参数**：

```json
{
  "expert_type": 1, // 专家类型：1-合约专家，2-现货专家
  "action": "follow" // 操作：follow-关注，unfollow-取消关注
}
```

**验证规则**:

- expert_type: required|integer|in:1,2
- action: required|string|in:follow,unfollow

响应示例：

```json
{
  "code": 200,
  "message": "关注成功",
  "data": {
    "id": 1,
    "user_id": 100,
    "expert_id": 1,
    "expert_type": "App\\Model\\Copy\\CopyContractExpert",
    "followed_at": "2025-01-01 18:00:00"
  }
}
```

**接口逻辑**：

- 验证用户身份和权限
- 验证专家 ID 的有效性：
  - 检查专家是否存在
  - 验证专家是否处于可关注状态
  - 检查专家类型（合约或现货）
- 根据 action 参数执行操作：
  - follow：执行关注操作
  - unfollow：执行取消关注操作
- 关注操作逻辑：
  - 检查是否已经关注该专家
  - 创建关注关系记录
  - 更新专家的粉丝数量统计
- 取消关注操作逻辑：
  - 检查是否已关注该专家
  - 删除关注关系记录
  - 更新专家的粉丝数量统计
  - 如果正在跟单，提示用户先停止跟单
- 记录操作日志
- 返回操作结果