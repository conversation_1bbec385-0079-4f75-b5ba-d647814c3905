# 交易专家端接口

### 1、交易专家申请接口

### 2、交易专家信息接口（支持过滤条件：类型（合约/现货））

- 交易专家信息
- 用户信息
- 专家等级
- 合约跟单账户资金/现货账户资金
- 当前跟单人数
- 当前带单进行中订单数量
- 粉丝数量

### 3、合约-带单数据统计接口（支持过滤条件：全部、指定天数、指定日期范围）

- 收益率
- 总收益
- 最大回撤
- 累计跟单人数
- 跟单者收益
- 交易频率（交易专家入驻后的交易总笔数/交易专家入驻天数；每 1 笔历史带单对应 2 笔交易订单）
- 胜率
- 盈利笔数
- 亏损笔数

### 4、合约-综合数据接口

- 当前跟单人数
- 交易专家等级对应最大跟单人数
- 资产管理规模（AUM）
- 总资产（合约跟单账户资金）
- 最近交易时间
- 分润比例
- 粉丝数量

### 5、现货-带单数据统计接口（支持过滤条件：全部、指定天数、指定日期范围）

- 收益率
- 总收益
- 累计跟单人数
- 跟单者收益
- 胜率
- 盈利笔数
- 亏损笔数

### 6、现货-综合数据接口

- 当前跟单人数
- 交易专家等级对应最大跟单人数
- 资产管理规模（AUM）
- 总资产（现货账户）
- 最近交易时间
- 分润比例
- 交易频率（交易专家入驻后的交易总笔数/交易专家入驻天数；每 1 笔历史带单对应 2 笔交易订单）
- 粉丝数量

### 7、合约-带单订单数据接口（支持过滤条件：当前带单-明细/当前带单-汇总/历史带单，全部币种/单个币种）

#### 当前带单-明细（以订单为单位）

- 仓位（币种、保证金模式、方向、杠杆、开仓时间）
- 持仓均价
- 当前价
- 数量
- 订单入场价
- 保证金
- 未实现盈亏
- 订单编号

#### 当前带单-汇总（以仓位为单位）

- 仓位（币种、保证金模式、方向、杠杆、开仓时间）
- 持仓均价
- 当前价
- 数量
- 止盈价
- 止损价
- 保证金
- 未实现盈亏

#### 历史带单（以仓位为单位）

- 仓位（币种、保证金模式、方向、杠杆、开仓时间）
- 订单入场价
- 平仓均价
- 数量
- 已实现盈亏
- 持仓均价
- 净利润

### 8、合约-撤销止盈止损接口（针对仓位）

- path 参数
  - id:仓位 id
- body 参数：
  ```json
  {
    "type": 1 // 1:止盈  2：止损  3：止盈止损
  }
  ```

### 9、现货-带单订单数据接口（支持过滤条件：当前带单-明细/当前带单-汇总/历史带单，全部币种/单个币种）

#### 当前带单-明细

- 币对
- 收益
- 买入价
- 买入时间
- 数量
- 止盈价
- 止损价

#### 当前带单-汇总（聚合当前同币种所有未平仓订单）

- 币对
- 收益
- 买入均价
- 数量

#### 历史带单

- 币对
- 收益
- 买入价
- 买入时间
- 卖出价
- 卖出时间
- 数量

### 10、现货-撤销止盈止损接口（针对订单）

- path 参数
  - id:订单 ID
- body 参数
  ```json
  {
    "type": 1 // 1:止盈  2：止损  3：止盈止损
  }
  ```

### 11、合约-分润数据统计接口

- 累计已分润
- 预计待分润
- 昨日分润
- 当前分润比例

### 12、合约-历史分润记录接口（支持过滤条件：按时间、按跟随者）

#### 按时间

- 时间
- 分润数量

#### 按跟随者（支持过滤条件：全部、天数、指定日期）

- 用户
- 分润数量

### 13、现货-分润数据统计接口

- 累计已分润
- 预计待分润
- 昨日分润
- 当前分润比例

### 14、现货-历史分润记录接口（支持过滤条件：按时间、按跟随者）

#### 按时间

- 时间
- 分润数量

#### 按跟随者

- 用户
- 分润数量

### 15、合约-我的跟随者接口（支持过滤条件：资产（所有跟单者、资产为 0、资产低于 50USDT）,跟单模式（全部、多元探索、智能比例），是否为尊享模式跟者）

- 跟随者
- 合约跟单账户资金
- 累计跟单笔数
- 跟单收益
- 跟单净利润
- 分润比例

### 16、合约-移除跟随着接口

- body 参数
  ```json
  {
    "ids": [1] // 跟随者用户ID
  }
  ```

### 17、现货-我的跟随者接口（支持过滤条件：资产（所有跟单者、资产为 0、资产低于 100USDT），是否为尊享模式跟者）

- 跟随者
- 现货账户资金
- 累计跟单笔数
- 跟单收益
- 跟单净利润
- 分润比例

### 18、现货-移除跟随着接口

- body 参数
  ```json
  {
    "ids": [1] // 跟随者用户ID
  }
  ```

### 19、合约-交易专家列表展示状态获取接口

- 展示状态
- 展示规则
  - 开启合约带单：是/否（合约带单）
  - 至少设置 1 个带单币对：已设置/未设置
  - 至少存在 1 笔已平仓带单订单：是/否
  - 合约带单账户资金 >=100 USDT：是/否
  - 当前合约带单账户资金：100

### 20、合约-个人设置接口

- body 参数
  ```json
  {
    "is_active": 1, // 是否开启合约带单
    "show_total_assets": 1, // 是否公共展示总资产
    "show_expert_rating": 1, // 是否展示专家评分及排名：1-是，0-否
    "introduction": "带单介绍",
    "position_protection": 1, // 未结仓位保护
    "min_follow_amount": 100, // 最小跟单金额
    "recommend_params": {
      // 推荐参数设置
      "fixed_amount": {
        // 固定额度
        "amount": "100.00", // 固定额度
        "stop_loss_rate": "10.00", // 止损比例
        "take_profit_rate": "20.00", // 止盈比例
        "max_copy_amount": "1000.00" // 最大跟单
      },
      "multiplier": {
        // 跟单倍率
        "multiplier": "0.1", // 固定倍率
        "stop_loss_rate": "10.00", // 止损比例
        "take_profit_rate": "20.00", // 止盈比例
        "max_copy_amount": "1000.00" // 最大跟单
      }
    },
    "currency_ids": [1, 2], // 币种ID
    "profit_sharing_rate": 10 // 分润比例
  }
  ```

### 21、合约-问题反馈/身份撤销接口

- body 参数
  ```json
  {
    "feedback_type": 1, // 1:问题反馈  2：身份撤销
    "problem_type": "技术问题", // 问题类型
    "content": "问题描述",
    "refund_account_type": 1 // 身份撤销时（feedback_type=2）必填，资金退回账户类型：1-现货账户，2-合约账户
  }
  ```

### 22、现货-交易专家列表展示状态获取接口

- 展示状态
- 展示规则
  - 开启现货带单：是/否
  - 至少设置 1 个带单币对：已设置/未设置
  - 至少存在 1 笔已平仓带单订单：是/否
  - 现货账户资金 >=100 USDT：是/否
  - 当前现货账户资金：100

### 23、现货-个人设置接口

- body 参数
  ```json
  {
    "is_active": 1, // 是否开启现货带单
    "show_total_assets": 1, // 是否公共展示总资产
    "show_fund_composition": 1, // 是否公开展示现货账户资金构成比例
    "introduction": "带单介绍",
    "new_currency_auto_copy": 1, // 新上线的交易对自动开启带单
    "position_protection": 1, // 未结仓位保护
    "min_follow_amount": 100, // 最小跟单金额
    "recommend_params": {
      // 推荐参数设置
      "fixed_amount": {
        // 固定额度
        "amount": "100.00", // 固定额度
        "stop_loss_rate": "10.00", // 止损比例
        "take_profit_rate": "20.00", // 止盈比例
        "max_copy_amount": "1000.00" // 最大跟单
      },
      "multiplier": {
        // 跟单倍率
        "multiplier": "0.1", // 固定倍率
        "stop_loss_rate": "10.00", // 止损比例
        "take_profit_rate": "20.00", // 止盈比例
        "max_copy_amount": "1000.00" // 最大跟单
      }
    },
    "currency_ids": [1, 2], // 币种ID
    "profit_sharing_rate": 10 // 分润比例
  }
  ```

### 24、现货-问题反馈/身份撤销接口

- body 参数
  ```json
  {
    "feedback_type": 1, // 1:问题反馈  2：身份撤销
    "problem_type": "技术问题", // 问题类型
    "content": "问题描述"
  }
  ```

### 25、合约-合约跟单账号余额历史接口（支持过滤条件：全部、指定天数、指定日期范围）

- 类型
- 金额
- 流向
- 时间

### 26、合约-带单币对接口

- 交易对 ID
- 交易对 symbol

### 27、现货-带单币对接口

- 交易对 ID
- 交易对 symbol

### 28、合约-收益率统计图接口（支持过滤条件：指定天数、指定日期范围）

- xAxis：日期
- yAxis：收益率

### 29、合约-总收益统计图接口（支持过滤条件：指定天数、指定日期范围）

- xAxis：日期
- yAxis：总收益

### 30、合约-币种偏好统计接口（饼图）（支持过滤条件：指定天数、指定日期范围）

- BTCUSDT-50%
- ETHUSDT-50%

### 31、合约-持仓时间统计接口（支持过滤条件：指定天数、指定日期范围）

- 平均持仓时间
- 最长持仓时间
- 统计数据
  - xAxis:盈亏
  - yAxis:持仓时段（固定分为五段，根据平均持仓时间、最长持仓时间动态分段）

### 32、合约-交易专家交易分析精灵统计接口（雷达图）（支持过滤条件：指定天数、指定日期范围）

- 专家评分
- 专家排名
- 统计数据
  - 指标：盈利因子（总盈利与总亏损比例）- 最大值：5、胜率 - 最大值：100%、平均盈亏比 - 最大值：5
  - series：
    - 专家平均能力参考值
    - 当前专家参数

### 33、合约-跟单者交易分析精灵统计接口（雷达图）（支持过滤条件：指定天数、指定日期范围）

- 统计数据
  - 指标：盈利因子（总盈利与总亏损比例）- 最大值：5、胜率 - 最大值：100%、平均盈亏比 - 最大值：5
  - series：
    - 跟单专家参数
    - 跟单者参数

### 34、合约-交易指标接口（支持过滤条件：指定天数、指定日期范围）

- 最近交易时间
- 交易次数
- 日均交易次数
- 总盈利
- 最大盈利
- 最大亏损
- 最大回撤
- 多空比

### 35、合约-币对交易表现接口（支持过滤条件：指定天数、指定日期范围）

- 币对
- 盈利订单数
- 亏损订单数

### 36、合约-交易日历接口（过滤条件：月份）

- 日历统计（日：盈亏）
- 月汇总
  - 净利润
  - 交易笔数
  - 胜率
- 周净利润汇总（若本周未结束，统计数据仅包含已过去的天数）
  - 第一周：利润
  - 第二周：利润
  - 第三周：利润
  - 第四周：利润
  - 第五周：利润

### 37、合约-交易专家尊享模式开启关闭接口

- body 参数
  ```json
  {
    "exclusive_mode": 1 // 1:开启  0：关闭
  }
  ```

### 38、现货-收益率统计图接口（支持过滤条件：指定天数、指定日期范围）

- xAxis：日期
- yAxis：收益率

### 39、现货-总收益统计图接口（支持过滤条件：指定天数、指定日期范围）

- xAxis：日期
- yAxis：总收益

### 40、现货-币种偏好统计接口（饼图）（支持过滤条件：指定天数、指定日期范围）

- BTCUSDT-50%
- ETHUSDT-50%

### 41、现货-持仓时间统计接口（散点图）（支持过滤条件：指定天数、指定日期范围）（每笔订单的盈亏与持仓时间）

- xAxis:盈亏持仓时间
- yAxis:盈亏

### 42、现货-交易量统计接口（支持过滤条件：指定天数、指定日期范围）

- xAxis:日期时间
- yAxis:交易量（现货所有历史带单的交易量总和(全部折合为 USDT)）

### 43、现货-资产构成统计接口

- USDT：50%
- BTC：50%

### 44、现货-币对交易表现接口（支持过滤条件：指定天数、指定日期范围）

- 币对
- 盈利订单数
- 亏损订单数

### 45、现货-交易专家尊享模式开启关闭接口

- body 参数
  ```json
  {
    "exclusive_mode": 1 // 1:开启  0：关闭
  }
  ```

### 46、交易专家等级接口（过滤条件：合约/现货）

- 当前等级
- 所有等级及条件

### 47、交易专家尊享模式邀请链接创建接口

- body 参数
  ```json
  {
    "type": 1, // 1:合约 2：现货
    "title": "", // 要求链接标题
    "invite_code": "SXKD88", // 要求码
    "max_count": 10, // 最大邀请人数（留空为不限制）
    "profit_sharing_rate": 20, // 分润比例（可空）
    "period": 7 // 有效天数（自动计算过期时间）
  }
  ```

### 48、交易专家尊享模式邀请链接编辑接口

- path 参数
  - id:邀请链接 ID
- body 参数
  ```json
  {
    "title": "", // 要求链接标题
    "max_count": 10, // 最大邀请人数（留空为不限制）
    "profit_sharing_rate": 20, // 分润比例（可空）
    "period": 7 // 有效天数（自动计算过期时间）
  }
  ```

### 49、交易专家尊享模式邀请链接删除接口

- path 参数
  - id:邀请链接 ID

### 50、交易专家尊享模式邀请成员列表接口（过滤条件：合约/现货）

- 跟单者信息
- 邀请链接信息
- 分润比例
- 加入时间

### 51、交易专家尊享模式邀请成员编辑接口

- path 参数
  - id:列表 id
- body 参数
  ```json
  {
    "profit_sharing_rate": 20 // 单独设置分润比例
  }
  ```

### 52、交易专家尊享模式邀请成员删除接口

- path 参数
  - id:列表 ID

### 53、粉丝列表接口

- 粉丝用户信息
- 关注时间

# 用户端接口

### 1、合约-交易专家列表接口

- ## Query 参数

  - days: 0:全部 7:7 天 30:30 天 90:90 天 180:180 天
  - 收益（盈利） (>=) （与 days 相关）
  - 收益率 (10,50)（与 days 相关）
  - 胜率 (>=)（与 days 相关）
  - 交易频率 (>=)（与 days 相关）
  - 跟单者收益 (>=)（与 days 相关）
  - 入驻天数 (>=)
  - 跟单者收益 (>=)（与 days 相关）
  - 资产管理规模 (>=)
  - 最大回撤 (>=)（与 days 相关）
  - 专家等级 (多选)
  - 跟单币种 (多选)
  - 隐藏满员
  - 资产公开
  - 是否关注
  - 支持排序
    - 盈亏（与 days 相关）
    - 胜率（与 days 相关）
    - 收益率（与 days 相关）
    - 跟单者收益（与 days 相关）
    - 最大回撤（与 days 相关）
    - 资产管理规模
  - 分页
    - page
    - page_size

- 列表数据
  - 交易专家信息（用户信息、等级、跟单人数/总跟单人数）
  - 交易专家统计信息（与 days 相关）
  - 交易专家收益率统计折线图表（与 days 相关）

### 2、合约-交易专家收益率统计图接口（支持过滤条件：指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - xAxis：日期
  - yAxis：收益率

### 3、合约-交易专家总收益统计图接口（支持过滤条件：指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - xAxis：日期
  - yAxis：总收益

### 4、合约-交易专家币种偏好统计接口（饼图）（支持过滤条件：指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - BTCUSDT-50%
  - ETHUSDT-50%

### 5、合约-交易专家持仓时间统计接口（支持过滤条件：指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - 平均持仓时间
  - 最长持仓时间
  - 统计数据
    - xAxis:盈亏
    - yAxis:持仓时段（固定分为五段，根据平均持仓时间、最长持仓时间动态分段）

### 6、合约-交易专家交易专家交易分析精灵统计接口（雷达图）（支持过滤条件：指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - 专家评分
  - 专家排名
  - 统计数据
    - 指标：盈利因子（总盈利与总亏损比例）- 最大值：5、胜率 - 最大值：100%、平均盈亏比 - 最大值：5
    - series：
      - 专家平均能力参考值
      - 当前专家参数

### 7、合约-交易专家跟单者交易分析精灵统计接口（雷达图）（支持过滤条件：指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - 统计数据
    - 指标：盈利因子（总盈利与总亏损比例）- 最大值：5、胜率 - 最大值：100%、平均盈亏比 - 最大值：5
    - series：
      - 跟单专家参数
      - 跟单者参数

### 8、合约-交易专家交易指标接口（支持过滤条件：指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - 最近交易时间
  - 交易次数
  - 日均交易次数
  - 总盈利
  - 最大盈利
  - 最大亏损
  - 最大回撤
  - 多空比

### 9、合约-交易专家币对交易表现接口（支持过滤条件：指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - 币对
  - 盈利订单数
  - 亏损订单数

### 10、合约-交易专家交易日历接口（过滤条件：月份）

- path 参数
  - id:交易专家 ID
- 响应数据
  - 日历统计（日：盈亏）
  - 月汇总
    - 净利润
    - 交易笔数
    - 胜率
  - 周净利润汇总（若本周未结束，统计数据仅包含已过去的天数）
    - 第一周：利润
    - 第二周：利润
    - 第三周：利润
    - 第四周：利润
    - 第五周：利润

### 11、合约-交易专家带单订单数据接口（支持过滤条件：当前带单-明细/当前带单-汇总/历史带单，全部币种/单个币种）

- path 参数
  - id:交易专家 ID

#### 当前带单-明细（以订单为单位）

- 仓位（币种、保证金模式、方向、杠杆、开仓时间）
- 持仓均价
- 当前价
- 数量
- 订单入场价
- 保证金
- 未实现盈亏
- 订单编号

#### 当前带单-汇总（以仓位为单位）

- 仓位（币种、保证金模式、方向、杠杆、开仓时间）
- 持仓均价
- 当前价
- 数量
- 止盈价
- 止损价
- 保证金
- 未实现盈亏

#### 历史带单（以仓位为单位）

- 仓位（币种、保证金模式、方向、杠杆、开仓时间）
- 订单入场价
- 平仓均价
- 数量
- 已实现盈亏
- 持仓均价
- 净利润

### 12、合约-交易专家跟随者接口（支持过滤条件：资产（所有跟单者、资产为 0、资产低于 50USDT）,跟单模式（全部、多元探索、智能比例），是否为尊享模式跟者）

- path 参数
  - id:交易专家 ID
- 列表数据
  - 跟随者
  - 合约跟单账户资金
  - 累计跟单笔数
  - 跟单收益
  - 跟单净利润

### 13、合约-交易专家带单数据统计接口（支持过滤条件：全部、指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - 收益率
  - 总收益
  - 最大回撤
  - 累计跟单人数
  - 跟单者收益
  - 交易频率（交易专家入驻后的交易总笔数/交易专家入驻天数；每 1 笔历史带单对应 2 笔交易订单）
  - 胜率
  - 盈利笔数
  - 亏损笔数

### 14、合约-综合数据接口

- path 参数
  - id:交易专家 ID
- 响应数据
  - 当前跟单人数
  - 交易专家等级对应最大跟单人数
  - 资产管理规模（AUM）
  - 总资产（合约跟单账户资金）
  - 最近交易时间
  - 分润比例
  - 粉丝数量

### 15、合约-交易专家带单币对接口

- path 参数
  - id:交易专家 ID
- 列表数据
  - 交易对 ID
  - 交易对 symbol

### 16、现货-交易专家列表接口

- ## Query 参数

  - days: 0:全部 7:7 天 30:30 天 90:90 天 180:180 天
  - 收益（盈利） (>=) （与 days 相关）
  - 收益率 (10,50)（与 days 相关）
  - 胜率 (>=)（与 days 相关）
  - 交易频率 (>=)（与 days 相关）
  - 入驻天数 (>=)
  - 专家等级 (多选)
  - 跟单币种 (多选)
  - 隐藏满员
  - 资产公开
  - 是否关注
  - 支持排序
    - 总收益（与 days 相关）
    - 胜率（与 days 相关）
    - 收益率（与 days 相关）
    - 跟单者收益（与 days 相关）
    - 累计跟单人数
    - 资产管理规模
  - 分页
    - page
    - page_size

- 列表数据
  - 交易专家信息（用户信息、等级、跟单人数/总跟单人数）
  - 交易专家统计信息（与 days 相关）
  - 交易专家收益率统计折线图表（与 days 相关）

### 17、现货-交易专家收益率统计图接口（支持过滤条件：指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - xAxis：日期
  - yAxis：收益率

### 18、现货-交易专家总收益统计图接口（支持过滤条件：指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - xAxis：日期
  - yAxis：总收益

### 19、现货-交易专家币种偏好统计接口（饼图）（支持过滤条件：指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - BTCUSDT-50%
  - ETHUSDT-50%

### 20、现货-交易专家持仓时间统计接口（散点图）（支持过滤条件：指定天数、指定日期范围）（每笔订单的盈亏与持仓时间）

- path 参数
  - id:交易专家 ID
- 响应数据
  - xAxis:盈亏持仓时间
  - yAxis:盈亏

### 21、现货-交易专家交易量统计接口（支持过滤条件：指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - xAxis:日期时间
  - yAxis:交易量（现货所有历史带单的交易量总和(全部折合为 USDT)）

### 22、现货-交易专家资产构成统计接口

- path 参数
  - id:交易专家 ID
- 响应数据
  - USDT：50%
  - BTC：50%

### 23、现货-交易专家币对交易表现接口（支持过滤条件：指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - 币对
  - 盈利订单数
  - 亏损订单数

### 24、现货-交易专家带单数据统计接口（支持过滤条件：全部、指定天数、指定日期范围）

- path 参数
  - id:交易专家 ID
- 响应数据
  - 收益率
  - 总收益
  - 累计跟单人数
  - 跟单者收益
  - 胜率
  - 盈利笔数
  - 亏损笔数

### 25、现货-交易专家综合数据接口

- path 参数
  - id:交易专家 ID
- 响应数据
  - 当前跟单人数
  - 交易专家等级对应最大跟单人数
  - 资产管理规模（AUM）
  - 总资产（现货账户）
  - 最近交易时间
  - 分润比例
  - 交易频率（交易专家入驻后的交易总笔数/交易专家入驻天数；每 1 笔历史带单对应 2 笔交易订单）
  - 粉丝数量

### 26、现货-交易专家带单订单数据接口（支持过滤条件：当前带单-明细/当前带单-汇总/历史带单，全部币种/单个币种）

- path 参数
  - id:交易专家 ID

#### 当前带单-明细

- 币对
- 收益
- 买入价
- 买入时间
- 数量
- 止盈价
- 止损价

#### 当前带单-汇总（聚合当前同币种所有未平仓订单）

- 币对
- 收益
- 买入均价
- 数量

#### 历史带单

- 币对
- 收益
- 买入价
- 买入时间
- 卖出价
- 卖出时间
- 数量

### 27、现货-交易专家跟随者接口（支持过滤条件：资产（所有跟单者、资产为 0、资产低于 100USDT），是否为尊享模式跟者）

- path 参数
  - id:交易专家 ID
- 列表数据
  - 跟随者
  - 现货账户资金
  - 累计跟单笔数
  - 跟单收益
  - 跟单净利润

### 28、现货-交易专家带单币对接口

- path 参数
  - id:交易专家 ID
- 列表数据
  - 交易对 ID
  - 交易对 symbol

### 29、关注/取消关注交易专家接口

- path 参数
  - id:交易专家 ID
- body 参数
  ```json
  {
    "is_follow": true
  }
  ```
