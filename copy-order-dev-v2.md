### 2.7 徽章分类表

```sql
CREATE TABLE `copy_badge_category` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` varchar(50) NOT NULL COMMENT '分类名称',
    `icon` varchar(255) NULL COMMENT '分类图标',
    `description` text NULL COMMENT '分类描述',
    `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
    `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否启用：1-是，0-否',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_sort` (`sort`)
) COMMENT='徽章分类表';
```

### 2.8 徽章表

```sql
CREATE TABLE `copy_badge` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `category_id` bigint unsigned NOT NULL COMMENT '分类ID',
    `type` tinyint NOT NULL COMMENT '类型：1-合约，2-现货',
    `name` varchar(50) NOT NULL COMMENT '徽章名称',
    `icon` varchar(255) NOT NULL COMMENT '徽章图标',
    `description` text NOT NULL COMMENT '徽章描述',
    `condition_config` json NOT NULL COMMENT '获取条件配置',
    `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
    `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否启用：1-是，0-否',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_category_id` (`category_id`),
    INDEX `idx_type` (`type`),
    INDEX `idx_sort` (`sort`)
) COMMENT='徽章表';
```

### 2.9 专家徽章表

```sql
CREATE TABLE `copy_expert_badge` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_type` varchar(255) NOT NULL COMMENT '专家模型类名（多态关联）',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
    `badge_id` bigint unsigned NOT NULL COMMENT '徽章ID',
    `is_wearing` tinyint NOT NULL DEFAULT '0' COMMENT '是否佩戴：1-是，0-否',
    `is_current` tinyint NOT NULL DEFAULT '1' COMMENT '是否为现有徽章：1-是，0-否',
    `achieved_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_expert_badge` (`expert_id`, `badge_id`),
    INDEX `idx_badge_id` (`badge_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_type` (`type`)
) COMMENT='专家徽章表';
```

## 6. API 接口设计

### 6.1 交易专家申请模块

#### 6.1.1 申请成为交易专家

**POST** `/api/copy/expert/apply`

请求参数：

```json
{
  "type": 1, // 1-合约交易专家, 2-现货交易专家
  "introduction": "专业合约交易，稳健收益",
  "transfer_amount": "100.00000000" // 仅合约专家需要
}
```

响应示例：

```json
{
  "code": 200,
  "message": "申请提交成功",
  "data": {
    "id": 1,
    "need_review": true,
    "status": 1,
    "review_message": "预计1-3个工作日内完成审核"
  }
}
```

错误响应：

```json
{
  "code": 400,
  "message": "申请失败",
  "errors": {
    "balance": "合约账户余额不足，需要至少100USDT"
  }
}
```

#### 6.1.2 更新交易专家设置

**PUT** `/api/copy/expert/settings/{type}`

请求参数：

```json
{
  "is_active": true,
  "show_total_assets": false,
  "show_expert_rating": true,
  "position_protection": true,
  "min_follow_amount": "80.00",
  "show_fund_composition": false, // 仅现货
  "auto_new_pairs": true // 仅现货
}
```

#### 6.1.3 更新推荐参数

**PUT** `/api/copy/expert/recommend-params/{type}`

请求参数：

```json
{
  "recommend_params": {
    "fixed_amount": {
      "enabled": true,
      "amount": "100.00",
      "stop_loss_rate": "10.00",
      "take_profit_rate": "20.00",
      "max_copy_amount": "1000.00"
    },
    "multiplier": {
      "enabled": false,
      "multiplier": "0.1",
      "stop_loss_rate": "10.00",
      "take_profit_rate": "20.00",
      "max_copy_amount": "1000.00"
    }
  }
}
```

#### 6.1.4 设置带单币种

**PUT** `/api/copy/expert/currencies/{type}`

请求参数：

```json
{
  "currency_ids": [1, 2, 3, 5, 8], // 币种ID数组
  "action": "set" // set-设置, add-添加, remove-移除
}
```

#### 6.1.5 问题反馈

**POST** `/api/copy/expert/feedback`

请求参数：

```json
{
  "type": 1, // 1-合约，2-现货
  "feedback_type": 1, // 1-问题反馈，2-身份撤销
  "problem_type": "technical_issue", // 系统配置的问题类型
  "content": "详细描述",
  "refund_account_type": 1 // 1-现货账户，2-合约账户（仅合约专家撤销时需要）
}
```

### 6.2 跟单管理模块

#### 6.2.1 创建跟单

**POST** `/api/copy/follow`

请求参数（智能比例模式）：

```json
{
  "expert_id": 1,
  "expert_type": "App\\Model\\Copy\\ContractExpert",
  "mode": 1, // 1-智能比例，2-多元探索
  "investment_amount": "500.00",
  "stop_loss_ratio": "0.1",
  "take_profit_ratio": "0.2",
  "max_follow_amount": "100.00",
  "slippage_ratio": "0.02"
}
```

请求参数（多元探索模式）：

```json
{
  "expert_id": 1,
  "expert_type": "App\\Model\\Copy\\SpotExpert",
  "mode": 2,
  "investment_amount": "1000.00",
  "copy_type": 1, // 1-固定额度，2-倍率
  "fixed_amount": "50.00", // copy_type=1时必填
  "multiplier": "0.5", // copy_type=2时必填
  "copy_currencies": [1, 2, 3], // 跟单币种ID
  "auto_new_pairs": true,
  "net_value_guardian": true,
  "max_loss_amount": "200.00",
  "min_net_value": "300.00",
  "copy_all_positions": false,
  "margin_mode": 1, // 1-跟随专家，2-全仓，3-逐仓
  "leverage_mode": 2, // 1-跟随专家，2-指定杠杆
  "custom_leverage": 10
}
```

#### 6.2.2 多元探索高级设置

**POST** `/api/copy/advanced-settings`

请求参数：

```json
{
  "expert_id": 1,
  "expert_type": "contract", // contract或spot
  "settings": [
    {
      "currency_id": 1,
      "copy_type": 1,
      "fixed_amount": "100.00",
      "stop_loss_ratio": "0.15",
      "take_profit_ratio": "0.3",
      "max_follow_amount": "500.00"
    },
    {
      "currency_id": 2,
      "copy_type": 2,
      "multiplier": "0.2",
      "stop_loss_ratio": "0.1",
      "take_profit_ratio": "0.25",
      "max_follow_amount": "300.00"
    }
  ]
}
```

#### 6.2.3 修改跟单设置

**PUT** `/api/copy/follow/{id}`

请求参数：

```json
{
  "investment_amount": "800.00",
  "status": 2, // 1-跟单中，2-已暂停
  "net_value_guardian": true,
  "max_loss_amount": "150.00"
}
```

#### 6.2.4 停止跟单

**DELETE** `/api/copy/follow/{id}`

请求参数：

```json
{
  "close_positions": true, // 是否平仓所有跟单仓位
  "reason": "risk_control" // 停止原因
}
```

### 6.3 尊享模式管理

#### 6.3.1 开启/关闭尊享模式

**PUT** `/api/copy/expert/exclusive-mode/{type}`

请求参数：

```json
{
  "exclusive_mode": true,
  "exclusive_profit_ratio": "0.3" // 30%分润比例
}
```

#### 6.3.2 创建邀请链接

**POST** `/api/copy/expert/invitation`

请求参数：

```json
{
  "expert_type": 1, // 1-合约，2-现货
  "title": "VIP跟单群",
  "max_uses": 50,
  "expires_days": 30,
  "profit_ratio": "0.25"
}
```

响应示例：

```json
{
  "code": 200,
  "data": {
    "id": 1,
    "code": "ABC123XYZ",
    "invitation_url": "https://cpx.com/copy/invite/ABC123XYZ",
    "expires_at": "2024-02-01 00:00:00"
  }
}
```

#### 6.3.3 调整分润比例

**PUT** `/api/copy/expert/profit-ratio/{type}`

请求参数：

```json
{
  "profit_ratio": "0.35",
  "apply_to": "new" // new-仅新订单, all-所有订单
}
```

错误响应（超过次数限制）：

```json
{
  "code": 429,
  "message": "今日调整次数已达上限（3次）",
  "data": {
    "next_available_time": "2024-01-02 00:00:00"
  }
}
```

### 6.4 数据统计接口

#### 6.4.1 获取专家综合统计

**GET** `/api/copy/expert/statistics/{type}`

查询参数：

- `period`: 7d, 30d, 90d, 180d, all
- `expert_id`: 专家 ID（可选，默认当前用户）

响应示例：

```json
{
  "code": 200,
  "data": {
    "profit_rate": "25.68",
    "trade_count": 156,
    "follower_count": 89,
    "total_profit": "12580.50",
    "win_rate": "68.5",
    "period": "30d"
  }
}
```

#### 6.4.2 获取当前带单列表

**GET** `/api/copy/expert/positions/{type}`

查询参数：

- `currency_id`: 币种 ID（可选）
- `side`: 1-买入，2-卖出（可选）
- `page`: 页码
- `per_page`: 每页数量

响应示例：

```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": 1,
        "symbol": "BTCUSDT",
        "side": 1,
        "side_text": "买入",
        "quantity": "0.5",
        "entry_price": "42000.00",
        "current_price": "43500.00",
        "unrealized_pnl": "750.00",
        "pnl_rate": "3.57",
        "margin": "2100.00",
        "leverage": 10,
        "stop_loss": "40000.00",
        "take_profit": "45000.00",
        "created_at": "2024-01-01 10:00:00"
      }
    ],
    "pagination": {
      "total": 5,
      "per_page": 20,
      "current_page": 1,
      "last_page": 1
    }
  }
}
```

#### 6.4.3 获取分润数据

**GET** `/api/copy/expert/profit-sharing/{type}`

查询参数：

- `date_from`: 开始日期
- `date_to`: 结束日期
- `group_by`: date, follower

响应示例：

```json
{
  "code": 200,
  "data": {
    "summary": {
      "total_settled": "5680.50",
      "pending_amount": "320.80",
      "yesterday_amount": "150.20",
      "current_ratio": "0.15"
    },
    "details": [
      {
        "date": "2024-01-01",
        "amount": "320.50",
        "count": 15
      }
    ]
  }
}
```

#### 6.4.4 获取跟随者列表

**GET** `/api/copy/expert/followers/{type}`

查询参数：

- `asset_filter`: all, zero, less_50
- `exclusive_mode`: 1, 0
- `sort_by`: asset, profit, follow_time
- `order`: asc, desc

响应示例：

```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "user_id": 100,
        "display_name": "User100",
        "avatar": "avatar_url",
        "account_balance": "1580.50",
        "total_orders": 45,
        "total_profit": "280.30",
        "net_profit": "238.25",
        "profit_ratio": "0.15",
        "is_exclusive": true,
        "follow_time": "2024-01-01 10:00:00"
      }
    ],
    "pagination": {...}
  }
}
```

### 6.5 关注管理接口

#### 6.5.1 关注/取消关注专家

**POST** `/api/copy/follow-expert`

请求参数：

```json
{
  "expert_id": 1,
  "expert_type": 1, // 1-合约，2-现货
  "action": "follow" // follow-关注, unfollow-取消关注
}
```

#### 6.5.2 获取关注列表

**GET** `/api/copy/following`

查询参数：

- `type`: 1-合约，2-现货
- `page`: 页码
- `per_page`: 每页数量

### 6.6 徽章系统接口

#### 6.6.1 获取专家徽章

**GET** `/api/copy/expert/badges/{expert_id}`

查询参数：

- `type`: 1-合约，2-现货

响应示例：

```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "交易先锋",
      "icon": "badge_icon_url",
      "description": "累计交易超过1000笔",
      "achieved_at": "2024-01-01",
      "type": 1
    }
  ]
}
```

## 7. 业务逻辑设计

### 7.1 推荐参数处理

推荐参数服务主要功能：

- 验证和更新专家推荐参数
- 支持固定额度和倍率两种模式的参数验证
- 参数验证规则：
  - 金额类参数必须为数值且大于 0
  - 倍率范围：0.01-50

### 7.2 多态关联处理

多态关联服务实现：

- 通过专家 ID 和类型（完整类名）获取专家实例
- 创建多态关联时自动填充：
  - expert_id：专家 ID
  - expert_type：专家类名（如 `App\Model\Copy\ContractExpert`）
  - expert_user_id：专家用户 ID（冗余字段）
  - type：专家类型值（1-合约，2-现货）

### 7.3 高级设置处理

多元探索模式高级设置服务：

- 允许针对不同币种设置不同的跟单参数
- 根据专家类型自动选择对应的设置表（合约/现货）
- 使用事务确保数据一致性
- 支持批量更新多个币种的设置

## 8. 业务流程详解

### 8.1 交易专家申请流程

#### 8.1.1 合约交易专家申请流程

```mermaid
graph TD
    A[用户提交申请] --> B{检查资格}
    B -->|不符合| C[返回错误提示]
    B -->|符合| D[检查账户余额]
    D -->|余额不足| E[提示充值]
    D -->|余额充足| F[创建申请记录]
    F --> G[划转100USDT到跟单账户]
    G --> H{需要审核?}
    H -->|是| I[等待管理员审核]
    I --> J{审核结果}
    J -->|通过| K[更新用户表contract_expert_id]
    J -->|拒绝| L[退回资金并通知用户]
    H -->|否| K
    K --> M[激活交易专家身份]
```

#### 8.1.2 现货交易专家申请流程

```mermaid
graph TD
    A[用户提交申请] --> B{检查资格}
    B -->|不符合| C[返回错误提示]
    B -->|符合| D[创建申请记录]
    D --> E{需要审核?}
    E -->|是| F[等待管理员审核]
    F --> G{审核结果}
    G -->|通过| H[更新用户表spot_expert_id]
    G -->|拒绝| I[通知用户]
    E -->|否| H
    H --> J[激活交易专家身份]
```

### 8.2 跟单模式详解

#### 8.2.1 智能比例跟单

**核心计算公式：**

```
跟单金额 = (专家下单金额 / 专家账户余额) × 跟单者投资金额
```

**模式优势：**

1. 仅需输入投资金额即可一键跟单，无需设置复杂参数
2. 使用相同资金比例确保精确跟单
3. 可帮助跟随者了解专家每笔订单的信心程度
4. 投资额的追加和减少都是针对单个专家

**使用流程：**

1. 选择交易专家后点击“跟单”
2. 选择跟单模式：智能比例跟单
3. 输入投资金额（不低于专家设置的最小跟单金额）
4. 设置风险项：止盈比例、止损比例、最大跟随金额、滑点比例
5. 确认跟单

**注意事项：**

- 最多跟随 50 名交易专家
- 默认跟随专家杠杆和保证金模式
- 减少投资额时最低需预留 50 USDT
- 投资金额仅针对当前专家

#### 8.2.2 多元探索跟单

**模式特点：**

- 允许用一笔资金同时复制多个交易专家的策略
- 无需每跟随一个专家都事先投入资金
- 使用同一个资金池，可同时跟随无限多个专家

**跟单方式：**

1. **固定额度：**每笔跟单使用固定保证金，不受专家下单金额影响
2. **跟单倍率：**跟单数量是专家下单数量的固定倍数

**使用流程：**

1. 点击“跟单”后选择多元探索跟单
2. 输入投资金额（最低 50 USDT，作为共用资金池）
3. 选择跟单方式：固定额度或倍率
4. 选择跟单币种
5. 设置风险参数
6. 确认跟单

**高级设置：**

- 支持针对不同币种设置不同参数
- 可设置保证金模式和杠杆倍数
- 自动跟随新上币对选项
- 适合风险偏好稳定的用户

**跟单倍率模式：**

- 跟单数量 = 专家下单数量 × 设定倍率
- 需要实时计算所需保证金
- 适合追求高收益的用户

**高级设置功能：**

- 针对不同币种设置不同参数
- 支持币种级别的风控设置
- 可自定义每个币种的跟单策略

### 8.3 分润机制设计

#### 8.3.1 分润计算规则

**触发条件：**

- 跟单订单实现盈利
- 订单状态为已完成
- 专家跟随者数量 ≥ 系统设定最小值

**计算公式：**

```
分润金额 = 跟单盈利 × 分润比例
```

**分润比例来源：**

1. 普通模式：根据专家等级确定
2. 尊享模式：专家自定义（0%-99%）

#### 8.3.2 分润结算流程

```mermaid
graph LR
    A[跟单订单平仓] --> B{是否盈利}
    B -->|否| C[结束]
    B -->|是| D[计算分润金额]
    D --> E[创建分润记录]
    E --> F[加入待结算队列]
    F --> G[定时任务批量结算]
    G --> H[转账到专家账户]
    H --> I[更新分润状态]
```

### 8.4 等级升降机制

#### 8.4.1 等级判定逻辑

**升级条件（同时满足）：**

- 条件一：带单金额达标
- 条件二：跟单者资金达标 OR 跟单人数达标

**降级条件：**

- 条件一不满足 OR 条件二完全不满足

#### 8.4.2 定时任务执行

等级调整任务实现：

- 每周一早上 6 点执行
- 分别处理合约和现货交易专家
- 计算新等级并更新分润比例
- 记录等级变更日志
- 使用事务确保数据一致性

### 8.5 尊享模式实现

#### 8.5.1 白名单邀请机制

**邀请链接生成：**

```php
<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */
namespace App\Http\Api\Service\Copy;

use App\Exception\BusinessException;
use App\Http\Api\Service\BaseService;
use App\Http\Common\ResultCode;
use App\Model\Copy\ExpertInvitation;
use Hyperf\Utils\Str;

final class InvitationService extends BaseService
{
    /**
     * 生成邀请链接
     *
     * @param array $params 参数
     * @return ExpertInvitation
     * @throws BusinessException
     */
    public function generateInvitation(array $params): ExpertInvitation
    {
        $code = $this->generateUniqueCode();

        try {
            return ExpertInvitation::create([
                'expert_id' => $params['expert_id'],
                'expert_type' => $params['expert_type'],
                'expert_user_id' => $params['expert_user_id'],
                'title' => $params['title'] ?? null,
                'code' => $code,
                'max_uses' => $params['max_uses'],
                'expires_at' => $params['expires_at'],
                'profit_ratio' => $params['profit_ratio']
            ]);
        } catch (\Throwable $e) {
            throw new BusinessException(ResultCode::FAIL, '生成邀请链接失败');
        }
    }

    /**
     * 生成唯一邀请码
     */
    private function generateUniqueCode(): string
    {
        do {
            $code = Str::random(8);
        } while (ExpertInvitation::where('code', $code)->exists());

        return $code;
    }
}
```

**邀请验证流程：**

1. 检查邀请码有效性
2. 验证使用次数限制
3. 检查过期时间
4. 创建跟单关系
5. 记录邀请使用记录

#### 8.5.2 分润比例调整限制

分润比例调整服务：

- 每日最大调整次数：3 次
- 通过查询当日调整记录判断是否可调整
- 记录每次调整的旧比例和新比例
- 调整仅对新订单生效

## 9. 数据统计与展示

### 9.1 交易专家数据统计

#### 9.1.1 综合统计指标

专家数据统计服务功能：

- 支持多周期统计：7 日、30 日、90 日、180 日、全部
- 统计指标包括：
  - 收益率：(期末资产 - 期初资产) / 期初资产 × 100%
  - 交易笔数
  - 累计跟单人数
  - 总收益
  - 胜率
- 数据实时统计，支持缓存优化

#### 9.1.2 实时数据更新

**Redis 缓存策略：**

实时数据服务实现：

- 缓存时间：60 秒
- 缓存键格式：`expert:{type}:{id}:positions`
- 缓存当前持仓数据，包括：
  - 交易对符号
  - 买卖方向
  - 持仓数量
  - 入场价格
  - 当前价格
  - 未实现盈亏
- 使用 Redis setex 自动过期

### 9.2 跟单者数据管理

#### 9.2.1 批量操作实现

```php
<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */
namespace App\Http\Api\Service\Copy;

use App\Exception\BusinessException;
use App\Http\Api\Service\BaseService;
use App\Http\Common\ResultCode;
use App\Model\Copy\CopyUserSetting;
use App\Model\Copy\CopyFollowStatus;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

final class FollowerManagementService extends BaseService
{
    #[Inject]
    private readonly NotificationService $notificationService;

    /**
     * 批量移除跟单者
     *
     * @param int $expertId 专家ID
     * @param string $expertType 专家类型
     * @param array $followerIds 跟单者ID列表
     * @throws BusinessException
     */
    public function batchRemoveFollowers(int $expertId, string $expertType, array $followerIds): void
    {
        Db::beginTransaction();
        try {
            // 停止所有进行中的跟单
            CopyUserSetting::where('expert_id', $expertId)
                ->where('expert_type', $expertType)
                ->whereIn('follower_user_id', $followerIds)
                ->update(['status' => CopyFollowStatus::STOPPED->value]);

            // 平仓所有未完成订单
            $this->closeAllOpenPositions($expertId, $expertType, $followerIds);

            // 发送通知
            foreach ($followerIds as $followerId) {
                $this->notificationService->notifyFollowerRemoval($followerId, $expertId, $expertType);
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw new BusinessException(ResultCode::FAIL, '批量移除跟单者失败');
        }
    }

    /**
     * 平仓所有未完成订单
     */
    private function closeAllOpenPositions(int $expertId, string $expertType, array $followerIds): void
    {
        // TODO: 实现平仓逻辑
    }
}
```

## 10. 异常处理与容错机制

### 10.1 下单失败处理

跟单订单处理服务主要功能：

1. **订单执行流程**

   - 获取所有活跃跟单者
   - 遍历执行跟单订单
   - 分类处理各种异常

2. **异常处理策略**

   - 余额不足：记录日志，发送通知
   - 订单执行失败：自动重试机制
   - 其他异常：记录详细错误信息

3. **重试机制**

   - 使用高优先级队列
   - 延迟 5 秒后重试
   - 最多重试 3 次

4. **日志记录**
   - 记录跟单者 ID、专家订单 ID
   - 记录异常信息和堆栈跟踪
   - 区分不同级别的日志

### 10.2 净值守护者触发

```php
<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */
namespace App\Http\Api\Service\Copy;

use App\Http\Api\Service\BaseService;
use App\Model\Copy\CopyUserSetting;
use App\Model\User\UserAccountsAssets;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Psr\Log\LoggerInterface;

final class NetValueGuardianService extends BaseService
{
    #[Inject]
    private readonly LoggerInterface $logger;

    #[Inject]
    private readonly CopyOrderService $copyOrderService;

    /**
     * 检查并触发净值守护
     *
     * @param int $userId 用户ID
     */
    public function checkAndTrigger(int $userId): void
    {
        $settings = CopyUserSetting::where('follower_user_id', $userId)
            ->where('net_value_guardian', 1)
            ->where('status', 1)
            ->get();

        foreach ($settings as $setting) {
            if ($this->shouldTrigger($setting)) {
                $this->executeGuardian($setting);
            }
        }
    }

    /**
     * 判断是否应该触发
     */
    private function shouldTrigger($setting): bool
    {
        $account = $this->getAccountInfo($setting->follower_user_id);

        // 检查净亏损
        if ($setting->max_loss_amount && bccomp((string)$account['total_loss'], (string)$setting->max_loss_amount, 8) >= 0) {
            return true;
        }

        // 检查最小净值
        if ($setting->min_net_value && bccomp((string)$account['net_value'], (string)$setting->min_net_value, 8) <= 0) {
            return true;
        }

        return false;
    }

    /**
     * 执行净值守护
     */
    private function executeGuardian($setting): void
    {
        Db::beginTransaction();
        try {
            // 1. 停止跟单
            $setting->status = 3; // 已停止
            $setting->save();

            // 2. 平仓所有未完成订单
            $this->copyOrderService->closeAllFollowerOrders($setting->follower_user_id);

            // 3. 记录触发日志
            $this->logger->info('净值守护被触发', [
                'user_id' => $setting->follower_user_id,
                'expert_id' => $setting->expert_id,
                'expert_type' => $setting->expert_type
            ]);

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            $this->logger->error('净值守护执行失败', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取账户信息
     */
    private function getAccountInfo(int $userId): array
    {
        // TODO: 实现账户信息获取
        return [
            'total_loss' => '0.00',
            'net_value' => '0.00'
        ];
    }
}
```

## 11. 权限控制与安全机制

### 11.1 接口权限验证

```php
<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */
namespace App\Http\Api\Middleware;

use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Model\Copy\ContractExpert;
use App\Model\Copy\SpotExpert;
use App\Model\User\User;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Contract\ResponseInterface as HttpResponse;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;

class CopyTradeMiddleware implements MiddlewareInterface
{
    public function __construct(
        protected ContainerInterface $container,
        protected RequestInterface $request,
        protected HttpResponse $response
    ) {
    }

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $user = $this->request->getAttribute('user');
        $role = $request->getAttribute('copy_role', '');

        switch ($role) {
            case 'expert':
                if (!$this->isExpert($user)) {
                    throw new BusinessException(ResultCode::UNAUTHORIZED, '需要交易专家身份');
                }
                break;

            case 'follower':
                if (!$this->canFollow($user)) {
                    throw new BusinessException(ResultCode::UNAUTHORIZED, '账户状态异常，无法跟单');
                }
                break;
        }

        return $handler->handle($request);
    }

    /**
     * 检查是否为交易专家
     */
    private function isExpert(?User $user): bool
    {
        if (!$user) {
            return false;
        }

        // 检查合约专家
        if ($user->contract_expert_id) {
            $expert = ContractExpert::find($user->contract_expert_id);
            if ($expert && $expert->status === 2) {
                return true;
            }
        }

        // 检查现货专家
        if ($user->spot_expert_id) {
            $expert = SpotExpert::find($user->spot_expert_id);
            if ($expert && $expert->status === 2) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否可以跟单
     */
    private function canFollow(?User $user): bool
    {
        if (!$user) {
            return false;
        }

        // TODO: 检查用户状态、账户余额等
        return $user->status === 1;
    }
}
```

### 11.2 数据隔离机制

```php
class DataIsolationService
{
    public function getVisiblePositions(int $expertId, ?int $viewerId = null): Collection
    {
        $expert = Expert::find($expertId);

        // 尊享模式检查
        if ($expert->exclusive_mode) {
            if (!$this->isWhitelistedFollower($expertId, $viewerId)) {
                return collect(); // 返回空集合
            }
        }

        // 未结仓位保护
        if ($expert->position_protection && !$this->isFollower($expertId, $viewerId)) {
            // 返回1小时前的数据
            return $this->getDelayedPositions($expertId);
        }

        return $this->getCurrentPositions($expertId);
    }
}
```

## 12. 定时任务设计

### 12.1 任务调度配置

```php
class Kernel extends ConsoleKernel
{
    protected function schedule(Schedule $schedule)
    {
        // 等级调整 - 每周一早上6点
        $schedule->job(new AdjustExpertLevelsJob)
            ->weeklyOn(1, '06:00')
            ->withoutOverlapping();

        // 分润结算 - 根据系统配置的频率
        $schedule->job(new SettleProfitSharingJob)
            ->hourly()
            ->when(function() {
                return $this->shouldRunSettlement();
            });

        // 徽章刷新 - 每天凌晨2点
        $schedule->job(new RefreshExpertBadgesJob)
            ->dailyAt('02:00');

        // 数据统计更新 - 每5分钟
        $schedule->job(new UpdateExpertStatisticsJob)
            ->everyFiveMinutes();

        // 净值守护检查 - 每分钟
        $schedule->job(new CheckNetValueGuardianJob)
            ->everyMinute();
    }
}
```

### 12.2 队列优先级设计

```php
// 队列配置
'redis' => [
    'driver' => 'redis',
    'connection' => 'default',
    'queue' => [
        'critical', // 紧急队列：跟单执行
        'high',     // 高优先级：分润结算、净值守护
        'default',  // 默认队列：数据统计、通知
        'low'       // 低优先级：日志记录、报表生成
    ],
    'retry_after' => 90,
],

// 使用示例
dispatch(new ExecuteCopyOrderJob($order))->onQueue('critical');
dispatch(new CalculateProfitSharingJob($record))->onQueue('high');
dispatch(new UpdateStatisticsJob($expert))->onQueue('default');
```

## 13. 性能优化策略

### 13.1 查询优化

```php
class ExpertQueryOptimizer
{
    // 使用预加载减少N+1查询
    public function getExpertsWithStats(array $filters): Collection
    {
        return Expert::with([
            'user:id,display_name,avatar',
            'level:id,name,profit_ratio',
            'badges',
            'statistics' => function($query) {
                $query->selectRaw('
                    expert_id,
                    SUM(profit_amount) as total_profit,
                    COUNT(DISTINCT follower_user_id) as follower_count,
                    AVG(win_rate) as avg_win_rate
                ')
                ->groupBy('expert_id');
            }
        ])
        ->where($filters)
        ->orderByDesc('follower_count')
        ->paginate(20);
    }
}
```

### 13.2 缓存策略

```php
class CacheStrategy
{
    // 多级缓存
    public function getExpertInfo(int $expertId): array
    {
        // L1: 进程内存缓存
        if ($cached = $this->memoryCache->get($expertId)) {
            return $cached;
        }

        // L2: Redis缓存
        $cacheKey = "expert:info:{$expertId}";
        if ($cached = Redis::get($cacheKey)) {
            $this->memoryCache->set($expertId, $cached);
            return json_decode($cached, true);
        }

        // L3: 数据库查询
        $data = $this->buildExpertInfo($expertId);

        // 写入缓存
        Redis::setex($cacheKey, 300, json_encode($data));
        $this->memoryCache->set($expertId, $data);

        return $data;
    }
}
```

## 14. 监控与告警

### 14.1 关键指标监控

```php
class MonitoringService
{
    // 监控跟单执行成功率
    public function monitorCopyOrderSuccess(): void
    {
        $stats = DB::table('copy_order_logs')
            ->whereDate('created_at', today())
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN status = "success" THEN 1 ELSE 0 END) as success,
                SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed
            ')
            ->first();

        $successRate = $stats->total > 0 ? ($stats->success / $stats->total) * 100 : 0;

        if ($successRate < 95) {
            $this->sendAlert('跟单成功率低于95%', [
                'success_rate' => $successRate,
                'failed_count' => $stats->failed
            ]);
        }
    }
}
```

## 15. 附录

### 15.1 数据库索引优化建议

```sql
-- 高频查询索引
ALTER TABLE cpx_copy_contract_order ADD INDEX idx_expert_date (expert_id, created_at);
ALTER TABLE cpx_copy_spot_order ADD INDEX idx_follower_status (follower_user_id, status);
ALTER TABLE cpx_copy_user_setting ADD INDEX idx_status_mode (status, mode);

-- 复合索引优化
ALTER TABLE cpx_copy_contract_profit_sharing
ADD INDEX idx_expert_status_date (expert_id, status, created_at);
```

### 15.2 系统配置参数

```php
// 跟单系统核心配置
return [
    'copy_trade' => [
        // 基础配置
        'enabled' => env('COPY_TRADE_ENABLED', true),
        'min_investment' => env('COPY_MIN_INVESTMENT', 50),
        'max_investment' => env('COPY_MAX_INVESTMENT', 10000),

        // 智能比例模式
        'smart_ratio' => [
            'max_experts' => 50,
            'min_ratio' => 0.01,
            'max_ratio' => 10,
        ],

        // 多元探索模式
        'multi_explore' => [
            'max_experts' => null, // 无限制
            'min_currencies' => 3,
        ],

        // 性能配置
        'batch_size' => 100,
        'max_retries' => 3,
        'retry_delay' => 5,
    ]
];
```

## 16. 业务规则详解

### 16.1 交易专家资格要求

#### 16.1.1 申请条件

- **合约专家**：

  - 账户余额 ≥ 100 USDT（系统配置）
  - 无未处理的违规记录
  - KYC 认证等级 ≥ 2

- **现货专家**：
  - 现货账户余额 ≥ 100 USDT
  - 无未处理的违规记录
  - KYC 认证等级 ≥ 2

#### 16.1.2 展示条件

- 至少设置 1 个带单币对
- 至少存在 1 笔已平仓带单订单
- 账户总资产保持 ≥ 100 USDT
- 处于激活状态

### 16.2 跟单限制规则

#### 16.2.1 投资金额限制

- 最小投资额：50 USDT
- 最大投资额：10,000 USDT
- 智能比例模式最多跟随：50 个专家
- 多元探索模式：无数量限制

#### 16.2.2 风控限制

- 止损比例：0% - 50%
- 止盈比例：0% - 200%
- 滑点比例：0% - 5%
- 单币种最大跟单金额：由专家设置

### 16.3 分润结算规则

#### 16.3.1 触发条件

- 跟单订单平仓且产生盈利
- 专家跟随者数量 ≥ 系统最小值（默认 5 人）
- 非测试订单

#### 16.3.2 结算周期

- T+1 结算（每日凌晨结算前一日）
- 可配置为实时结算或周期结算
- 结算货币：USDT

### 16.4 等级升降规则

#### 16.4.1 升级条件（同时满足）

- 条件一：带单金额达标

  - L1→L2: 10,000 USDT
  - L2→L3: 50,000 USDT
  - L3→L4: 200,000 USDT
  - L4→L5: 1,000,000 USDT

- 条件二（满足其一）：
  - 跟单者总资金达标
  - 跟单人数达标

#### 16.4.2 降级条件

- 连续 4 周未达到当前等级维持条件
- 出现重大违规行为
- 胜率低于 30%连续 2 周

### 16.5 尊享模式规则

#### 16.5.1 开启条件

- 专家等级 ≥ L2
- 跟随者数量 ≥ 10 人
- 近 30 天胜率 ≥ 60%

#### 16.5.2 限制规则

- 每日可调整分润比例次数：3 次
- 分润比例范围：0% - 99%
- 邀请链接有效期：最长 90 天
- 单个链接最大使用次数：500 次

## 17. 错误处理规范

### 17.1 业务异常定义

```php
namespace App\Exception\Copy;

class CopyTradeException extends BusinessException
{
    // 专家相关错误码：1001-1099
    const EXPERT_NOT_FOUND = 1001;
    const EXPERT_NOT_ACTIVE = 1002;
    const EXPERT_APPLICATION_PENDING = 1003;
    const EXPERT_BALANCE_INSUFFICIENT = 1004;
    const EXPERT_ALREADY_EXISTS = 1005;

    // 跟单相关错误码：1101-1199
    const FOLLOW_BALANCE_INSUFFICIENT = 1101;
    const FOLLOW_EXPERT_LIMIT_EXCEEDED = 1102;
    const FOLLOW_ALREADY_EXISTS = 1103;
    const FOLLOW_MIN_AMOUNT_NOT_MET = 1104;
    const FOLLOW_MAX_AMOUNT_EXCEEDED = 1105;

    // 分润相关错误码：1201-1299
    const PROFIT_SHARING_NOT_ELIGIBLE = 1201;
    const PROFIT_RATIO_ADJUST_LIMIT = 1202;
    const PROFIT_SETTLEMENT_FAILED = 1203;

    // 尊享模式错误码：1301-1399
    const EXCLUSIVE_NOT_ELIGIBLE = 1301;
    const EXCLUSIVE_INVITATION_INVALID = 1302;
    const EXCLUSIVE_INVITATION_EXPIRED = 1303;
    const EXCLUSIVE_INVITATION_FULL = 1304;
}
```

### 17.2 异常处理流程

```php
class CopyOrderExceptionHandler
{
    public function handle($request, Throwable $e)
    {
        if ($e instanceof CopyTradeException) {
            return $this->handleBusinessException($e);
        }

        if ($e instanceof ValidationException) {
            return $this->handleValidationException($e);
        }

        // 记录未知异常
        $this->logError($e);

        return response()->json([
            'code' => 500,
            'message' => '系统繁忙，请稍后再试'
        ], 500);
    }

    private function handleBusinessException(CopyTradeException $e): JsonResponse
    {
        $messages = [
            CopyTradeException::EXPERT_NOT_FOUND => '交易专家不存在',
            CopyTradeException::EXPERT_NOT_ACTIVE => '交易专家未开启带单',
            CopyTradeException::FOLLOW_BALANCE_INSUFFICIENT => '账户余额不足',
            // ... 其他错误信息映射
        ];

        return response()->json([
            'code' => $e->getCode(),
            'message' => $messages[$e->getCode()] ?? $e->getMessage()
        ], 400);
    }
}
```

## 18. 状态机设计

### 18.1 交易专家申请状态流转

```mermaid
stateDiagram-v2
    [*] --> 待审核: 提交申请
    待审核 --> 审核通过: 管理员通过
    待审核 --> 审核拒绝: 管理员拒绝
    审核通过 --> 已激活: 完成设置
    已激活 --> 已暂停: 暂停带单
    已暂停 --> 已激活: 恢复带单
    已激活 --> 已撤销: 身份撤销
    审核拒绝 --> [*]
    已撤销 --> [*]
```

### 18.2 跟单订单状态流转

```mermaid
stateDiagram-v2
    [*] --> 跟单中: 创建跟单
    跟单中 --> 部分平仓: 部分平仓
    部分平仓 --> 跟单中: 继续持仓
    跟单中 --> 已完成: 全部平仓
    部分平仓 --> 已完成: 全部平仓
    跟单中 --> 已取消: 手动取消/触发净值守护
    已完成 --> [*]
    已取消 --> [*]
```

## 19. 数据一致性保障

### 19.1 事务处理策略

```php
<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */
namespace App\Http\Api\Service\Copy;

use App\Http\Api\Service\BaseService;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Psr\Log\LoggerInterface;

final class CopyOrderTransactionService extends BaseService
{
    #[Inject]
    private readonly LoggerInterface $logger;

    /**
     * 创建跟单订单（确保数据一致性）
     */
    public function createFollowOrder(array $expertOrder, array $followers): void
    {
        Db::transaction(function() use ($expertOrder, $followers) {
            // 1. 创建专家带单记录
            $expertRecord = $this->createExpertOrderRecord($expertOrder);

            // 2. 批量创建跟单订单
            foreach ($followers as $follower) {
                try {
                    // 检查余额
                    $this->checkFollowerBalance($follower);

                    // 冻结资金
                    $this->freezeFollowerFunds($follower);

                    // 创建跟单订单
                    $this->createFollowerOrder($follower, $expertRecord);

                } catch (\Exception $e) {
                    // 记录失败但不影响其他跟单者
                    $this->logFollowFailure($follower, $e);
                }
            }

            // 3. 更新统计数据
            $this->updateStatistics($expertRecord);
        }, 3); // 最多重试3次
    }
}
```

### 19.2 分布式锁应用

```php
class DistributedLockService
{
    /**
     * 分润比例调整（防止并发）
     */
    public function adjustProfitRatio(int $expertId, float $newRatio): bool
    {
        $lockKey = "profit_ratio_adjust:{$expertId}";
        $lock = Redis::lock($lockKey, 10);

        if (!$lock->acquire()) {
            throw new CopyTradeException('操作太频繁，请稍后再试');
        }

        try {
            // 检查今日调整次数
            if (!$this->canAdjustToday($expertId)) {
                throw new CopyTradeException('今日调整次数已达上限');
            }

            // 执行调整
            $this->doAdjust($expertId, $newRatio);

            return true;
        } finally {
            $lock->release();
        }
    }
}
```

## 20. 需求实现要点总结

### 20.1 关键技术要点

1. **多态关联**：使用 Laravel 的 morphTo/morphMany 实现灵活的专家类型关联
2. **JSON 字段**：推荐参数、跟单币种等复杂配置使用 JSON 存储
3. **冗余设计**：expert_user_id 等字段提升查询性能
4. **分表策略**：合约/现货分表降低单表压力
5. **缓存机制**：多级缓存提升响应速度

### 20.2 业务实现要点

1. **账户类型**：使用 COPY 类型（AccountType::COPY）作为跟单钱包
2. **系统配置**：通过 Helper::getSysSettingByTypeCode()获取动态配置
3. **审核机制**：支持配置是否需要审核
4. **资金管理**：合约专家需要划转资金到专门账户
5. **展示逻辑**：满足多个条件才在列表展示
6. **等级机制**：每周定时评估，支持升降级
7. **分润计算**：T+1 结算，支持尊享模式自定义比例
8. **净值守护**：实时监控，触发条件自动解除跟单

### 20.3 扩展性考虑

1. **新交易类型**：预留 type 字段支持期权等新类型
2. **多币种分润**：当前仅 USDT，但结构支持扩展
3. **策略扩展**：跟单模式可扩展新的策略类型
4. **国际化**：所有文本使用语言包，支持多语言

本文档作为跟单功能开发的核心参考，涵盖了从数据库设计到业务实现的完整技术方案。开发过程中如有疑问，请参考相应章节的详细说明。

## 21. 请求验证类示例

### 21.1 申请交易专家验证

申请交易专家验证规则：

**基本字段验证：**

- type：必填，1-合约专家，2-现货专家
- introduction：必填，20-500 字符
- transfer_amount：合约专家必填，最小 100 USDT

**验证规则说明：**

- 使用 Rule::requiredIf 实现条件必填
- 验证器继承 BaseFormRequest
- 支持自定义错误消息

### 21.2 创建跟单验证

创建跟单验证规则：

**通用字段验证：**

- expert_id：必填，必须存在
- expert_type：必填，完整类名
- mode：必填，1-智能比例，2-多元探索
- investment_amount：必填，50-10000 USDT
- stop_loss_ratio：选填，0-50%
- take_profit_ratio：选填，0-200%
- max_follow_amount：选填，最大跟单金额
- slippage_ratio：选填，0-5%

**多元探索模式额外字段：**

- copy_type：必填，1-固定额度，2-倍率
- fixed_amount：固定额度时必填
- multiplier：倍率模式时必填，0.01-10
- copy_currencies：必填，跟单币种数组
- net_value_guardian：选填，净值守护
- max_loss_amount：净值守护最大亏损
- min_net_value：净值守护最小净值
- copy_all_positions：选填，复制全部仓位
- margin_mode：选填，1-跟随专家，2-全仓，3-逐仓
- leverage_mode：选填，1-跟随专家，2-指定杠杆
- custom_leverage：指定杠杆时必填

**验证特点：**

- 根据跟单模式动态添加验证规则
- 使用枚举类确保类型安全

### 21.3 更新推荐参数验证

推荐参数验证规则：

**JSON 结构验证：**

- recommend_params：必填，数组格式
- fixed_amount：固定额度参数
  - enabled：必填，布尔值
  - amount：启用时必填，最小 0
  - stop_loss_rate：启用时必填，0-50%
  - take_profit_rate：启用时必填，0-200%
  - max_copy_amount：启用时必填，最小 0
- multiplier：倍率参数
  - enabled：必填，布尔值
  - multiplier：启用时必填，0.01-10
  - stop_loss_rate：启用时必填，0-50%
  - take_profit_rate：启用时必填，0-200%
  - max_copy_amount：启用时必填，最小 0

**自定义验证：**

- 至少启用一种推荐模式
- 使用 withValidator 添加复杂验证逻辑
- 使用 required_if 实现条件必填

## 22. 控制器类示例

### 22.1 交易专家控制器

交易专家控制器主要功能：

**控制器配置：**

- 路由前缀：/api/copy/expert
- 全局中间件：JwtAuthMiddleware
- 继承 AbstractController

**主要接口：**

1. **申请成为专家** (POST /apply)

   - 验证请求参数
   - 调用 expertService->applyExpert
   - 返回申请结果

2. **获取专家设置** (GET /settings/{type})

   - 需要专家身份验证
   - 支持合约/现货类型

3. **更新专家设置** (PUT /settings/{type})

   - 需要专家身份验证
   - 验证更新数据

4. **更新推荐参数** (PUT /recommend-params/{type})

   - 验证 JSON 格式参数
   - 保存推荐配置

5. **设置带单币种** (PUT /currencies/{type})

   - 支持 set/add/remove 操作
   - 验证币种有效性

6. **问题反馈** (POST /feedback)
   - 支持问题反馈和身份撤销
   - 合约专家撤销需指定退款账户

**中间件使用：**

- CopyTradeMiddleware:expert 验证专家身份
- 通过 Hyperf 注解实现路由绑定

### 22.2 跟单管理控制器

跟单管理控制器主要功能：

**控制器配置：**

- 路由前缀：/api/copy/follow
- 全局中间件：JwtAuthMiddleware
- 依赖注入：FollowService

**主要接口：**

1. **创建跟单** (POST /)

   - 验证跟单参数
   - 检查余额和资格
   - 创建跟单记录

2. **获取跟单列表** (GET /)

   - 支持筛选和分页
   - 返回用户跟单信息

3. **更新跟单设置** (PUT /{id})

   - 修改投资金额
   - 调整风控参数
   - 开启/关闭净值守护

4. **停止跟单** (DELETE /{id})

   - 支持选择是否平仓
   - 记录停止原因

5. **高级设置** (POST /advanced-settings)
   - 为不同币种设置参数
   - 仅多元探索模式使用

**返回统一格式：**

- 使用 success() 方法
- 包含 code、message、data

## 23. 系统配置项补充

### 23.1 最小跟单资金配置

在 system_setting_config 表中添加：

7. **合约最小跟单资金**

   - key: contract_min_follow_amount
   - input_type: number
   - 默认值: 50 USDT

8. **现货最小跟单资金**

   - key: spot_min_follow_amount
   - input_type: number
   - 默认值: 50 USDT

9. **分润最小跟单人数**

   - key: profit_sharing_min_followers
   - input_type: number
   - 默认值: 5

10. **分润结算周期**

    - key: profit_sharing_cycle
    - input_type: select
    - 可选项: realtime（实时）, daily（每日）, weekly（每周）
    - 默认值: daily

11. **智能比例最大跟随专家数**
    - key: smart_ratio_max_experts
    - input_type: number
    - 默认值: 50

### 23.2 订单执行配置

12. **跟单执行延迟时间**

    - key: copy_execution_delay
    - input_type: number
    - 默认值: 0 (毫秒)
    - 备注: 为避免市场操纵可设置延迟

13. **跟单重试次数**

    - key: copy_retry_times
    - input_type: number
    - 默认值: 3

14. **重试间隔时间**
    - key: copy_retry_interval
    - input_type: number
    - 默认值: 5 (秒)

## 24. 总结

本文档涵盖了跟单模块的完整开发方案，包括：

1. **数据库设计**：完整的表结构和多态关联设计
2. **业务逻辑**：详细的功能实现要点和流程
3. **API 接口**：完整的请求响应设计
4. **异常处理**：全面的错误处理机制
5. **性能优化**：缓存策略和查询优化
6. **系统配置**：灵活的参数配置

开发时请按照文档规范执行，如有疑问请参考对应章节。
