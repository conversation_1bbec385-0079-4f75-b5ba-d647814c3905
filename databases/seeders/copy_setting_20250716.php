<?php

declare(strict_types=1);

use Hyperf\Database\Seeders\Seeder;
use Hyperf\DbConnection\Db;
use Carbon\Carbon;

class CopySetting20250716 extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $groupId = Db::table('system_setting_config_group')
            ->where('code', 'copy_order_setting')
            ->value('id');

        if (!$groupId) {
            $groupId = Db::table('system_setting_config_group')->insertGetId([
                'name' => '跟单设置',
                'code' => 'copy_order_setting',
                'icon' => null,
                'created_by' => 1,
                'updated_by' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
                'remark' => '跟单模块相关配置'
            ]);
        }

        $configs = [
            // 1、合约专家申请是否需要审核
            [
                'group_id' => $groupId,
                'key' => 'contract_expert_need_review',
                'value' => '1',
                'name' => '合约专家申请是否需要审核',
                'input_type' => 'switch',
                'config_select_data' => json_encode([
                    ['label' => '需要审核', 'value' => '1'],
                    ['label' => '无需审核', 'value' => '0']
                ]),
                'sort' => 1,
                'remark' => '1-需要审核，0-无需审核',
                'created_by' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            // 2现货专家申请是否需要审核
            [
                'group_id' => $groupId,
                'key' => 'spot_expert_need_review',
                'value' => '1',
                'name' => '现货专家申请是否需要审核',
                'input_type' => 'switch',
                'config_select_data' => json_encode([
                    ['label' => '需要审核', 'value' => '1'],
                    ['label' => '无需审核', 'value' => '0']
                ]),
                'sort' => 2,
                'remark' => '1-需要审核，0-无需审核',
                'created_by' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            // 3合约专家合约账户最小划转资金
            [
                'group_id' => $groupId,
                'key' => 'contract_expert_min_transfer',
                'value' => '100',
                'name' => '合约专家合约账户最小划转资金',
                'input_type' => 'input',
                'config_select_data' => null,
                'sort' => 3,
                'remark' => '最小划转金额（USDT）',
                'created_by' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            // 4问题反馈类型预设
            [
                'group_id' => $groupId,
                'key' => 'feedback_problem_types',
                'value' => null,
                'name' => '问题反馈类型预设',
                'input_type' => 'keyValuePair',
                'config_select_data' => json_encode([
                    ['label' => '技术问题', 'value' => '技术问题'],
                    ['label' => '资金问题', 'value' => '资金问题'],
                    ['label' => '策略问题', 'value' => '策略问题'],
                    ['label' => '平台问题', 'value' => '平台问题'],
                    ['label' => '其他问题', 'value' => '其他问题']
                ]),
                'sort' => 4,
                'remark' => '问题反馈可选类型',
                'created_by' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            // 5合约交易专家展示条件
            [
                'group_id' => $groupId,
                'key' => 'contract_expert_display_conditions',
                'value' => null,
                'name' => '合约交易专家展示条件',
                'input_type' => 'keyValuePair',
                'config_select_data' => json_encode([
                    ['label' => 'currency_count', 'value' => 1],
                    ['label' => 'order_count', 'value' => 1],
                    ['label' => 'copy_account_assets', 'value' => 100]
                ]),
                'sort' => 5,
                'remark' => 'currency_count:币对数量，order_count:订单数量，copy_account_assets:账户资产（USDT）',
                'created_by' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            // 6现货交易专家展示条件
            [
                'group_id' => $groupId,
                'key' => 'spot_expert_display_conditions',
                'value' => null,
                'name' => '现货交易专家展示条件',
                'input_type' => 'keyValuePair',
                'config_select_data' => json_encode([
                    ['label' => 'currency_count', 'value' => 1],
                    ['label' => 'order_count', 'value' => 1],
                    ['label' => 'spot_account_assets', 'value' => 100]
                ]),
                'sort' => 6,
                'remark' => 'currency_count:币对数量，order_count:订单数量，spot_account_assets:账户资产（USDT）',
                'created_by' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            // 7合约跟单最小资金
            [
                'group_id' => $groupId,
                'key' => 'contract_min_follow_amount',
                'value' => '100',
                'name' => '合约跟单最小资金',
                'input_type' => 'input',
                'config_select_data' => null,
                'sort' => 7,
                'remark' => '合约最小跟单金额（USDT）',
                'created_by' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            // 8现货跟单最小资金
            [
                'group_id' => $groupId,
                'key' => 'spot_min_follow_amount',
                'value' => '100',
                'name' => '现货跟单最小资金',
                'input_type' => 'input',
                'config_select_data' => null,
                'sort' => 8,
                'remark' => '现货最小跟单金额（USDT）',
                'created_by' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]
        ];

        Db::table('system_setting_config')->insert($configs);
    }
}
