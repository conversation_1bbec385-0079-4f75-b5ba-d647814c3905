<?php

declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('system_setting_config', function (Blueprint $table) {
            // 检查字段是否存在且需要修改
            if (Schema::hasColumn('system_setting_config', 'key')) {
                // 临时关闭索引操作（可选，确保唯一索引操作安全）
                $table->dropUnique(['key']);
                $table->string('key', 64)->unique()->change(); // 修改长度并重建唯一索引
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('system_setting_config', function (Blueprint $table) {
            if (Schema::hasColumn('system_setting_config', 'key')) {
                $table->dropUnique(['key']);
                $table->string('key', 32)->unique()->change(); // 回滚到原始长度
            }
        });
    }
};
