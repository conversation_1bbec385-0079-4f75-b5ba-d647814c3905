<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_profit_rate_log', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('expert_id')->unsigned()->comment('专家ID');
            $table->string('expert_type', 255)->comment('专家模型类名（多态关联）');
            $table->bigInteger('expert_user_id')->unsigned()->comment('专家用户ID（冗余字段）');
            $table->tinyInteger('type')->comment('专家类型：1-合约，2-现货');
            $table->decimal('old_rate', 8, 2)->nullable()->comment('原分润比例 %');
            $table->decimal('new_rate', 8, 2)->comment('新分润比例 %');
            $table->tinyInteger('is_exclusive')->default(0)->comment('是否尊享模式：1-是，0-否');
            $table->timestamps();

            // 索引
            $table->index(['expert_id', 'expert_type'], 'idx_expert');
            $table->index('expert_user_id', 'idx_expert_user_id');

            $table->comment('分润比例修改记录表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_profit_rate_log');
    }
};
