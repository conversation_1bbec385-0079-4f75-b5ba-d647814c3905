<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_follow', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('user_id')->unsigned()->comment('关注者用户ID');
            $table->bigInteger('expert_id')->unsigned()->comment('专家ID');
            $table->string('expert_type', 255)->comment('专家模型类名（多态关联）');
            $table->bigInteger('expert_user_id')->unsigned()->comment('专家用户ID（冗余字段）');
            $table->tinyInteger('type')->comment('专家类型：1-合约，2-现货');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');

            // 索引
            $table->unique(['user_id', 'expert_id', 'type'], 'uk_user_expert');
            $table->index(['expert_id', 'expert_type'], 'idx_expert');

            $table->comment('关注表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_follow');
    }
};
