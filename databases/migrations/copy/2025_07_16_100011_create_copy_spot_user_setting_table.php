<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_spot_user_setting', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('follower_user_id')->unsigned()->comment('跟单者用户ID');
            $table->bigInteger('expert_id')->unsigned()->comment('专家ID');
            $table->bigInteger('expert_user_id')->unsigned()->comment('专家用户ID（冗余字段）');
            $table->decimal('fixed_amount', 20, 8)->nullable()->comment('固定额度（USDT）');
            $table->decimal('rate', 8, 2)->nullable()->comment('倍率 %');
            $table->decimal('stop_loss_rate', 8, 2)->nullable()->comment('止损比例 %');
            $table->decimal('take_profit_rate', 8, 2)->nullable()->comment('止盈比例 %');
            $table->decimal('max_follow_amount', 20, 8)->nullable()->comment('最大跟随金额');
            $table->tinyInteger('auto_new_pairs')->default(0)->comment('自动跟随新币对：1-是，0-否');
            $table->tinyInteger('is_exclusive')->default(0)->comment('是否尊享模式：1-是，0-否');
            $table->json('copy_currencies')->nullable()->comment('跟单币种配置（支持多币种）');
            $table->tinyInteger('status')->default(1)->comment('状态：1-跟单中，2-暂停');
            $table->timestamps();

            // 索引
            $table->unique(['follower_user_id', 'expert_id'], 'uk_follower_expert');
            $table->index('expert_id', 'idx_expert');
            $table->index('expert_user_id', 'idx_expert_user_id');
            $table->index('status', 'idx_status');

            $table->comment('用户跟单配置表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_spot_user_setting');
    }
};
