<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_contract_expert_statistics', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('expert_id')->unsigned()->comment('专家ID');
            $table->bigInteger('expert_user_id')->unsigned()->comment('专家用户ID（冗余字段）');

            // 总盈利、7日总盈利、30日总盈利、90日总盈利、180日总盈利
            $table->decimal('total_profit', 20, 8)->default(0)->comment('总盈利');
            $table->decimal('total_profit_7d', 20, 8)->default(0)->comment('7日总盈利');
            $table->decimal('total_profit_30d', 20, 8)->default(0)->comment('30日总盈利');
            $table->decimal('total_profit_90d', 20, 8)->default(0)->comment('90日总盈利');
            $table->decimal('total_profit_180d', 20, 8)->default(0)->comment('180日总盈利');

            // 总亏损、7日总亏损、30日总亏损、90日总亏损、180日总亏损
            $table->decimal('total_loss', 20, 8)->default(0)->comment('总亏损');
            $table->decimal('total_loss_7d', 20, 8)->default(0)->comment('7日总亏损');
            $table->decimal('total_loss_30d', 20, 8)->default(0)->comment('30日总亏损');
            $table->decimal('total_loss_90d', 20, 8)->default(0)->comment('90日总亏损');
            $table->decimal('total_loss_180d', 20, 8)->default(0)->comment('180日总亏损');

            // 盈利订单数（仓位单位）、7日盈利订单数（仓位单位）、30日盈利订单数（仓位单位）、90日盈利订单数（仓位单位）、180日盈利订单数（仓位单位）
            $table->integer('profit_order_count')->default(0)->comment('盈利订单数');
            $table->integer('profit_order_count_7d')->default(0)->comment('7日盈利订单数');
            $table->integer('profit_order_count_30d')->default(0)->comment('30日盈利订单数');
            $table->integer('profit_order_count_90d')->default(0)->comment('90日盈利订单数');
            $table->integer('profit_order_count_180d')->default(0)->comment('180日盈利订单数');

            // 亏损订单数（仓位单位）、7日亏损订单数（仓位单位）、30日亏损订单数（仓位单位）、90日亏损订单数（仓位单位）、180日亏损订单数（仓位单位）
            $table->integer('loss_order_count')->default(0)->comment('亏损订单数');
            $table->integer('loss_order_count_7d')->default(0)->comment('7日亏损订单数');
            $table->integer('loss_order_count_30d')->default(0)->comment('30日亏损订单数');
            $table->integer('loss_order_count_90d')->default(0)->comment('90日亏损订单数');
            $table->integer('loss_order_count_180d')->default(0)->comment('180日亏损订单数');

            // 平均盈利、7日平均盈利、30日平均盈利、90日平均盈利、180日平均盈利
            $table->decimal('average_profit', 20, 8)->default(0)->comment('平均盈利');
            $table->decimal('average_profit_7d', 20, 8)->default(0)->comment('7日平均盈利');
            $table->decimal('average_profit_30d', 20, 8)->default(0)->comment('30日平均盈利');
            $table->decimal('average_profit_90d', 20, 8)->default(0)->comment('90日平均盈利');
            $table->decimal('average_profit_180d', 20, 8)->default(0)->comment('180日平均盈利');

            // 平均亏损、7日平均亏损、30日平均亏损、90日平均亏损、180日平均亏损
            $table->decimal('average_loss', 20, 8)->default(0)->comment('平均亏损');
            $table->decimal('average_loss_7d', 20, 8)->default(0)->comment('7日平均亏损');
            $table->decimal('average_loss_30d', 20, 8)->default(0)->comment('30日平均亏损');
            $table->decimal('average_loss_90d', 20, 8)->default(0)->comment('90日平均亏损');
            $table->decimal('average_loss_180d', 20, 8)->default(0)->comment('180日平均亏损');

            // 盈亏金额
            $table->decimal('profit', 20, 8)->default(0)->comment('盈亏金额');
            $table->decimal('profit_7d', 20, 8)->default(0)->comment('7日盈亏金额');
            $table->decimal('profit_30d', 20, 8)->default(0)->comment('30日盈亏金额');
            $table->decimal('profit_90d', 20, 8)->default(0)->comment('90日盈亏金额');
            $table->decimal('profit_180d', 20, 8)->default(0)->comment('180日盈亏金额');

            // 胜率、7日胜率、30日胜率、90日胜率、180日胜率
            $table->decimal('win_rate', 8, 2)->default(0)->comment('胜率 %');
            $table->decimal('win_rate_7d', 8, 2)->default(0)->comment('7日胜率 %');
            $table->decimal('win_rate_30d', 8, 2)->default(0)->comment('30日胜率 %');
            $table->decimal('win_rate_90d', 8, 2)->default(0)->comment('90日胜率 %');
            $table->decimal('win_rate_180d', 8, 2)->default(0)->comment('180日胜率 %');

            // 收益率、7日收益率、30日收益率、90日收益率、180日收益率
            $table->decimal('profit_rate', 8, 2)->default(0)->comment('收益率 %');
            $table->decimal('profit_rate_7d', 8, 2)->default(0)->comment('7日收益率 %');
            $table->decimal('profit_rate_30d', 8, 2)->default(0)->comment('30日收益率 %');
            $table->decimal('profit_rate_90d', 8, 2)->default(0)->comment('90日收益率 %');
            $table->decimal('profit_rate_180d', 8, 2)->default(0)->comment('180日收益率 %');

            // 跟单者收益、7日跟单者收益、30日跟单者收益、90日跟单者收益、180日跟单者收益
            $table->decimal('follower_profit', 20, 8)->default(0)->comment('跟单者收益');
            $table->decimal('follower_profit_7d', 20, 8)->default(0)->comment('7日跟单者收益');
            $table->decimal('follower_profit_30d', 20, 8)->default(0)->comment('30日跟单者收益');
            $table->decimal('follower_profit_90d', 20, 8)->default(0)->comment('90日跟单者收益');
            $table->decimal('follower_profit_180d', 20, 8)->default(0)->comment('180日跟单者收益');

            // 最大回撤、7日最大回撤、30日最大回撤、90日最大回撤、180日最大回撤
            $table->decimal('max_drawdown', 20, 8)->default(0)->comment('最大回撤');
            $table->decimal('max_drawdown_7d', 20, 8)->default(0)->comment('7日最大回撤');
            $table->decimal('max_drawdown_30d', 20, 8)->default(0)->comment('30日最大回撤');
            $table->decimal('max_drawdown_90d', 20, 8)->default(0)->comment('90日最大回撤');
            $table->decimal('max_drawdown_180d', 20, 8)->default(0)->comment('180日最大回撤');

            // 资产管理规模
            $table->decimal('aum', 20, 8)->default(0)->comment('资产管理规模');

            // 交易频率、7日交易频率、30日交易频率、90日交易频率、180日交易频率
            $table->integer('trade_frequency')->default(0)->comment('交易频率');
            $table->integer('trade_frequency_7d')->default(0)->comment('7日交易频率');
            $table->integer('trade_frequency_30d')->default(0)->comment('30日交易频率');
            $table->integer('trade_frequency_90d')->default(0)->comment('90日交易频率');
            $table->integer('trade_frequency_180d')->default(0)->comment('180日交易频率');

            // 累计跟单人数
            $table->integer('total_follower_count')->default(0)->comment('累计跟单人数');

            $table->timestamps();

            // 索引
            $table->index('expert_id', 'idx_expert');
            $table->index('expert_user_id', 'idx_expert_user_id');

            $table->comment('合约交易专家统计表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_contract_expert_statistics');
    }
};
