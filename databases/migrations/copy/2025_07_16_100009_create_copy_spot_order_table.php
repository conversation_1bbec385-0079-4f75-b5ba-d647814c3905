<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_spot_order', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('follower_user_id')->unsigned()->comment('跟单者用户ID');
            $table->bigInteger('expert_id')->unsigned()->comment('专家ID');
            $table->bigInteger('expert_user_id')->unsigned()->comment('专家用户ID（冗余字段）');
            $table->tinyInteger('mode')->comment('跟单模式：1-智能比例，2-多元探索');
            $table->bigInteger('expert_order_id')->unsigned()->comment('专家订单ID（关联trade_spot_order）');
            $table->bigInteger('follower_order_id')->unsigned()->comment('跟单者订单ID（关联trade_spot_order）');
            $table->decimal('profit_sharing_rate', 8, 2)->comment('分润比例 %');
            $table->tinyInteger('is_exclusive')->default(0)->comment('是否尊享模式：1-是，0-否');
            $table->json('copy_settings_snapshot')->comment('跟单配置参数快照');
            $table->timestamps();

            // 索引
            $table->index('follower_user_id', 'idx_follower_user_id');
            $table->index('expert_id', 'idx_expert');
            $table->index('expert_user_id', 'idx_expert_user_id');
            $table->index('expert_order_id', 'idx_expert_order_id');
            $table->index('follower_order_id', 'idx_follower_order_id');

            $table->comment('现货跟单记录表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_spot_order');
    }
};
