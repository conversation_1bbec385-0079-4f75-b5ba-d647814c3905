<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_feedback', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('expert_id')->unsigned()->comment('专家ID');
            $table->string('expert_type', 255)->comment('专家模型类名（多态关联）');
            $table->bigInteger('expert_user_id')->unsigned()->comment('专家用户ID（冗余字段）');
            $table->tinyInteger('type')->comment('专家类型：1-合约，2-现货');
            $table->tinyInteger('feedback_type')->comment('反馈类型：1-问题反馈，2-身份撤销');
            $table->string('problem_type', 50)->comment('问题类型');
            $table->text('content')->comment('反馈内容');
            $table->tinyInteger('refund_account_type')->nullable()->comment('资金退回账户类型：1-现货账户，2-合约账户（仅合约专家撤销）');
            $table->decimal('refund_amount', 20, 8)->nullable()->comment('退回金额（仅身份撤销）');
            $table->timestamps();

            // 索引
            $table->index(['expert_id', 'expert_type'], 'idx_expert');

            $table->comment('问题反馈表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_feedback');
    }
};
