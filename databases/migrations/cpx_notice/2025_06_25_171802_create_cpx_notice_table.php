<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_notice', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键');
            $table->bigInteger('category_id')->default(0)->comment('分类ID');
            $table->text('title')->default('')->comment('标题');
            $table->text('content')->comment('内容');
            $table->bigInteger('look_num')->default(0)->comment('查看数');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
            $table->comment('公告表');
            $table->engine = 'InnoDB';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_notice');
    }
};
