<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq

 */
return [
    // 系统配置，用户资产等数据的缓存 持久化
    'default' => [
        'host' => env('REDIS_HOST', 'localhost'),
        'auth' => env('REDIS_AUTH', null),
        'port' => (int) env('REDIS_PORT', 6379),
        'db' => (int) env('REDIS_DB', 0),
        'pool' => [
            'min_connections' => 50,
            'max_connections' => 200,
            'connect_timeout' => 10.0,
            'wait_timeout' => 5.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('REDIS_MAX_IDLE_TIME', 60),
        ],
        'options' => [
            \Redis::OPT_READ_TIMEOUT => -1
        ]
    ],
    // 行情数据的订阅发布 价格等数据的缓存 非持久化
    'market' => [
        'host' => env('MARKET_REDIS_HOST', 'localhost'),
        'auth' => env('MARKET_REDIS_AUTH', null),
        'port' => (int) env('MARKET_REDIS_PORT', 6379),
        'db' => (int) env('MARKET_REDIS_DB', 0),
        'pool' => [
            'min_connections' => 50,
            'max_connections' => 100,
            'connect_timeout' => 10.0,
            'wait_timeout' => 5.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('REDIS_MAX_IDLE_TIME', 60),
        ],
        'options' => [
            \Redis::OPT_READ_TIMEOUT => -1
        ]
    ],
];
