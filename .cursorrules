你是经验丰富的PHP开发工程师，擅长Laravel和Hyperf框架，本项目使用 hyperf 框架开发，请按照以下规范开发代码：

# 开发规范

## PHP 代码风格

- 所有 PHP 文件必须使用 `<?php` 标签开始
- 所有 PHP 文件必须在开头添加 `declare(strict_types=1);`
- 所有 PHP 文件必须包含标准文件头注释，包含项目名称、链接、文档和联系方式
- 类名必须使用 PascalCase 命名法
- 方法名必须使用 camelCase 命名法
- 属性名、变量名必须使用 camelCase 命名法
- 常量必须使用全大写 SNAKE_CASE 命名法
- 所有类必须有命名空间声明
- 所有类必须有适当的 PHPDoc 注释
- 所有方法必须有返回类型声明
- 所有方法参数必须有类型声明
- 所有属性必须有类型声明
- 代码缩进使用 4 个空格
- 行尾不应有多余的空格
- 文件末尾必须有一个空行
- 使用单引号定义字符串，除非字符串中包含变量或单引号

## 控制器规范

- 控制器类必须继承 AbstractController
- 控制器方法应返回 Result 对象
- 控制器方法应使用 success()、error() 方法返回结果
- 控制器不应包含业务逻辑，应委托给对应的 Service 类
- 控制器方法应进行输入验证
- 控制器应遵循 RESTful API 设计原则

## 模型规范

- 模型类应继承 App\QueryBuilder\Model
- 模型属性应使用 PHPDoc 注释声明
- 模型应声明 $table 属性
- 模型关联方法应使用正确的返回类型声明
- 模型应使用 final 关键字声明（除非需要被继承）
- 模型应使用强类型枚举定义状态常量
- 枚举字段处理

对于表示有限选项的字段（如状态、类型等），建议使用枚举：

1. 在数据库中使用 tinyint 类型
2. 在字段注释中清晰说明可选值（例如："状态：1-启用，2-禁用"）
3. 系统将自动创建对应的枚举类
4. 在模型中自动添加类型转换

枚举字段的 DDL 格式示例：

```sql
`status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-启用，2-禁用',
```

生成的模型类型转换：

```php
protected array $casts = [
    'status' => \App\Model\User\Enums\Status::class,
];
```

生成的验证规则：

```php
use Hyperf\Validation\Rule;

'status' => ['required', Rule::in(array_column(\App\Model\User\Enums\Status::cases(), 'value'))],
```

- 枚举类示例（同时需要生成对应的语言文件）

```php
<?php

declare(strict_types=1);
/**
 * 策略平台API
 * {描述}枚举
 */

namespace App\Model\{Module}\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum Status: int
{
    use EnumConstantsTrait;

    /**
     * 启用
     */
    #[Message('user.enums.status.1')]
    case NORMAL = 1;

    /**
     * 禁用
     */
    #[Message('user.enums.status.2')]
    case DISABLED = 2;
}
```
枚举命名规范

- 枚举值使用全大写字母，下划线分隔
- 每个枚举值必须有中文注释
- 使用 `#[Message]` 注解定义国际化消息
- **重要**: 枚举类必须放在各模块模型目录下的 `Enums` 中，如 `app/Model/User/Enums/Status.php`

字段类型
- 表字段类型为 json 的字段，在模型中应使用 array 类型声明
- 考虑表字段为 null 的情况，在模型中应使用 ? 类型声明（通常参考对应的迁移文件，迁移文件中$table->timestamps()表示有 created_at 和 updated_at 字段且允许为 null）
- 一定一定要参考的完整示例：
```php
<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 币种模型
 */

namespace App\Model\Currency;

use App\QueryBuilder\Model;

/**
 * @property int $id 自增主键
 * @property array $name 币种名称（支持多语言）
 * @property string $symbol 币种代码
 * @property string|null $icon 币种图标
 * @property int $sort 排序
 * @property int $decimals 精度
 * @property \Carbon\Carbon|null $created_at 创建时间
 * @property \Carbon\Carbon|null $updated_at 更新时间
 */
final class Currency extends Model
{
    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 币种名称（支持多语言）
     */
    public const FIELD_NAME = 'name';
    /**
     * 币种代码
     */
    public const FIELD_SYMBOL = 'symbol';
    /**
     * 币种图标
     */
    public const FIELD_ICON = 'icon';
    /**
     * 排序
     */
    public const FIELD_SORT = 'sort';
    /**
     * 精度
     */
    public const FIELD_DECIMALS = 'decimals';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'currency';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 自增主键
        'name', // 币种名称（支持多语言）
        'symbol', // 币种代码
        'icon', // 币种图标
        'sort', // 排序
        'decimals', // 精度
        'created_at', // 创建时间
        'updated_at' // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 自增主键
        'name' => 'array', // 币种名称（支持多语言）
        'sort' => 'integer', // 排序
        'decimals' => 'integer', // 精度
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime' // 更新时间
    ];

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取币种名称（多语言）
     */
    public function getName(): array
    {
        return $this->name;
    }

    /**
     * 设置币种名称（多语言）
     *
     * @param array $value 币种名称数组
     */
    public function setName($value): static
    {
        $this->name = $value;
        return $this;
    }

    /**
     * 获取币种代码
     */
    public function getSymbol(): string
    {
        return $this->symbol;
    }

    /**
     * 设置币种代码
     *
     * @param string $value 币种代码
     */
    public function setSymbol($value): static
    {
        $this->symbol = $value;
        return $this;
    }

    /**
     * 获取币种图标
     */
    public function getIcon(): ?string
    {
        return $this->icon;
    }

    /**
     * 设置币种图标
     *
     * @param string $value 币种图标URL
     */
    public function setIcon($value): static
    {
        $this->icon = $value;
        return $this;
    }

    /**
     * 获取排序值
     */
    public function getSort(): int
    {
        return $this->sort;
    }

    /**
     * 设置排序值
     *
     * @param int $value 排序值
     */
    public function setSort($value): static
    {
        $this->sort = $value;
        return $this;
    }

    /**
     * 获取精度
     */
    public function getDecimals(): int
    {
        return $this->decimals;
    }

    /**
     * 设置精度
     *
     * @param int $value 精度值
     */
    public function setDecimals($value): static
    {
        $this->decimals = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param \Carbon\Carbon|string $value 创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param \Carbon\Carbon|string $value 更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }
}
```

## 服务规范

- 服务类应包含业务逻辑
- 服务类应使用依赖注入获取依赖
- 服务方法应有明确的返回类型
- 服务类应处理异常并转换为业务异常
- 服务类不应直接处理 HTTP 请求和响应
- Api 端服务类: 放在 `app/Http/Api/Service/{Module}/` 目录下，继承 `\App\Http\Api\Service\BaseService`。继承后可通过 ` $this->userId() ` 获取当前登录用户ID, ` $this->user() ` 获取当前登录用户信息（缓存中）, ` $this->getUser() ` 获取当前登录用户信息（数据库中）
- Admin 端服务类: 放在 `app/Http/Admin/Service/{Module}/` 目录下

## 异常处理规范

- 异常类应继承适当的基础异常类
- 异常应包含错误码和错误消息
- 业务异常应使用 BusinessException 类
- 正常状态异常应使用 NormalStatusException 类
- 抛出异常示例：
```php
use App\Exception\BusinessException;
use App\Http\Common\ResultCode;

throw new BusinessException(ResultCode::FORBIDDEN, 'xxx');
```

## 配置文件规范

- 配置文件应返回数组
- 配置键应使用 snake_case 命名法
- 配置文件应包含注释说明配置项的用途
- 敏感配置应使用环境变量

## 目录结构规范

- 控制器应放在 app/Http/{Admin|Api}/Controller/xxx 目录下
- 模型应放在 app/Model 目录下
- 服务应放在 app/Http/{Admin|Api}/Service/xxx 目录下, 不要放在 app/Service 目录下
- 异常应放在 app/Exception 目录下
- 中间件应放在 app/Http/{Admin|Api}/Middleware 目录下
- 请求验证类应放在 app/Http/{Admin|Api}/Request/xxx 目录下
- 资源应放在 app/Http/{Admin|Api}/Resource/xxx 目录下, 不要放在 app/Resource 目录下（若无明确要求使用资源构造器请不要使用资源类）
- 命令应放在 app/Command 目录下
- 数据库迁移文件应放在 databases/migrations 目录下

## 注释规范

- 类应有完整的 PHPDoc 注释，包括描述、属性和方法
- 公共方法应有 PHPDoc 注释，包括描述、参数和返回值
- 复杂的私有方法应有注释说明其用途
- 代码中的复杂逻辑应有行内注释说明
- TODO 和 FIXME 注释应包含具体的任务描述和责任人

## 测试规范

- 测试类应继承 HyperfTests\TestCase
- 测试方法名应以 test 开头
- 测试应包含断言
- 测试应覆盖正常和异常情况
- 测试应使用 Mock 对象隔离依赖

## 获取数据列表的接口尽量使用 apielf/hyperf-query-builder 扩展。文档参考：vendor/apielf/hyperf-query-builder/README.md
- 类命名空间:

|class                   |namespace                      |
|------------------------|-------------------------------|
|AllowedFilter           |ApiElf\QueryBuilder            |
|AllowedInclude          |ApiElf\QueryBuilder            |
|AllowedSort             |ApiElf\QueryBuilder            |
|ConfigProvider          |ApiElf\QueryBuilder            |
|QueryBuilder            |ApiElf\QueryBuilder            |
|QueryBuilderRequest     |ApiElf\QueryBuilder            |
|AddsFieldsToQuery       |ApiElf\QueryBuilder\Concerns   |
|AddsIncludesToQuery     |ApiElf\QueryBuilder\Concerns   |
|FiltersQuery            |ApiElf\QueryBuilder\Concerns   |
|SortsQuery              |ApiElf\QueryBuilder\Concerns   |
|InvalidFilterQuery      |ApiElf\QueryBuilder\Exceptions |
|Filter                  |ApiElf\QueryBuilder\Filters    |
|FiltersCallback         |ApiElf\QueryBuilder\Filters    |
|FiltersExact            |ApiElf\QueryBuilder\Filters    |
|FiltersPartial          |ApiElf\QueryBuilder\Filters    |
|FiltersScope            |ApiElf\QueryBuilder\Filters    |
|FiltersTrashed          |ApiElf\QueryBuilder\Filters    |
|IgnoresValueTrait       |ApiElf\QueryBuilder\Filters    |
|IncludeCount            |ApiElf\QueryBuilder\Includes   |
|IncludeInterface        |ApiElf\QueryBuilder\Includes   |
|IncludeRelationship     |ApiElf\QueryBuilder\Includes   |
|Sort                    |ApiElf\QueryBuilder\Sorts      |
|SortField               |ApiElf\QueryBuilder\Sorts      |


- 获取数据列表的接口应使用 QueryBuilder 查询数据
- 获取数据列表的接口应使用 Paginator 分页数据
- 获取数据列表的接口应使用 Filter 过滤数据（注意分辨是否需要使用精确过滤: AllowedFilter::exact()）
- 获取数据列表的接口应使用 Sort 排序数据
- 获取数据列表的接口应使用 Include 包含关联数据
- 获取数据列表的接口应使用 Append 追加数据

## 编写接口规范
- 遵循 Restful 规范
- 使用 Hyperf\HttpServer\Annotation 注解
- 控制器、验证器中都不要使用 Swagger 注解，不需要生成 Swagger 接口文档
- 列表接口：#[GetMapping('list')]
- 详情接口：#[GetMapping('{id}')]
- 创建接口：#[PostMapping('')]
- 更新接口：#[PutMapping('{id}')]
- 删除接口：#[DeleteMapping('{id}')]
- 批量删除接口：#[DeleteMapping('batch/delete')]
- 创建管理端接口时需要添加中间件：

#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]

- 创建管理端（Admin 模块）接口时需要控制权限，使用注解：

|接口     |权限注解                                        |
|--------|-----------------------------------------------|
|列表     |#[Permission(code: 'xxx:index')]    |
|创建     |#[Permission(code: 'xxx:create')]   |
|更新     |#[Permission(code: 'xxx:update')]   |
|删除     |#[Permission(code: 'xxx:delete')]   |
|批量删除  |#[Permission(code: 'xxx:delete')]   |

- Api 模块接口不需要控制权限，但需要默认添加登录中间件（命名空间：App\Http\Api\Middleware\TokenMiddleware）：

#[Middleware(middleware: TokenMiddleware::class, priority: 100)]

- 不要使用 Repository 层！筛选过滤在 Service 层使用 apielf/hyperf-query-builder 扩展直接实现

事务处理规范

**重要**: 仅当有多条数据库写入/更新操作时才使用事务

```php
// ✅ 正确：多条数据库操作需要事务
Db::beginTransaction();
try {
    // 创建用户
    $user = new User();
    $user->save();

    // 创建用户VIP等级记录
    $userVipLevel = new UserVipLevel();
    $userVipLevel->user_id = $user->id;
    $userVipLevel->save();

    Db::commit();
} catch (\Throwable $th) {
    Db::rollBack();
    throw new BusinessException(ResultCode::FAIL, '操作失败，' . $th->getMessage());
}

// ✅ 正确：单条数据库操作不需要事务
$user = User::query()->find($id);
$user->status = Status::ACTIVE;
$user->save();

// ❌ 错误：单条操作使用事务（多余）
Db::beginTransaction();
try {
    $user->save();
    Db::commit();
} catch (\Throwable $th) {
    Db::rollBack();
    throw new BusinessException(ResultCode::FAIL, '操作失败');
}
```
控制器基本结构
- Api 端示例：

```php
<?php

declare(strict_types=1);

namespace App\Http\Api\Controller\{Module};

use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\HttpServer\Annotation\PostMapping;

#[Controller(prefix: 'api/{module}')]
#[Middleware(middleware: TokenMiddleware::class, priority: 100)]
final class {Entity}Controller extends AbstractController
{
    #[Inject]
    private readonly {Entity}Service ${entity}Service;

    /**
     * {方法描述}
     */
    #[PostMapping('{action}')]
    public function {action}({Request} $request): Result
    {
        return $this->success(
            $this->{entity}Service->{action}($request->all())
        );
    }
}
```

- Admin 端示例：
```php
<?php

declare(strict_types=1);

namespace App\Http\Admin\Controller\{Module};

use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use App\Http\Admin\Controller\AbstractController;
use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\HttpServer\Annotation\PostMapping;

#[Controller(prefix: 'admin/{module}')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
final class {Entity}Controller extends AbstractController
{
    #[Inject]
    private readonly {Entity}Service ${entity}Service;

    /**
     * {方法描述}
     */
    #[PostMapping('{action}')]
    public function {action}({Request} $request): Result
    {
        return $this->success(
            $this->{entity}Service->{action}($request->all())
        );
    }
}
```

## 8. 请求验证规范

### 8.1 请求类基本结构

当验证器继承 `\App\Http\{Admin|Api}\Request\BaseFormRequest` 时，支持 `{控制器方法名}Rules` 方式定义各控制器方法的验证器：

```php
<?php

declare(strict_types=1);

namespace App\Http\{Admin|Api}\Request\{Module};

use App\Http\{Admin|Api}\Request\BaseFormRequest;

class {Entity}Request extends BaseFormRequest
{
    /**
     * 注册验证规则 - 对应控制器的 register 方法
     */
    public function registerRules(): array
    {
        return [
            'email' => ['required', 'email', 'unique:cpx_user,email'],
            'password' => ['required', 'string', 'min:6'],
            'code' => ['required', 'string'],
        ];
    }

    /**
     * 登录验证规则 - 对应控制器的 login 方法
     */
    public function loginRules(): array
    {
        return [
            'username' => ['required', 'string'],
            'password' => ['required', 'string'],
        ];
    }

    /**
     * 列表验证规则 - 对应控制器的 list 方法
     */
    public function listRules(): array
    {
        return [
            'page' => ['integer', 'min:1'],
            'page_size' => ['integer', 'min:1', 'max:100'],
        ];
    }

    /**
     * 字段映射名称
     */
    public function attributes(): array
    {
        return [
            'email' => '邮箱',
            'password' => '密码',
            'code' => '验证码',
        ];
    }
}
```

### 8.2 验证规则命名规范

- 方法名格式：`{控制器方法名}Rules()`
- 如控制器方法为 `registerByEmail`，则验证方法为 `registerByEmailRules()`
- 如控制器方法为 `bindPhone`，则验证方法为 `bindPhoneRules()`

## 9. 分页列表接口规范

### 9.1 使用 QueryBuilder 构建查询

**重要**: 分页列表接口必须使用 `App\QueryBuilder\QueryBuilder` 构建查询：

```php
use App\QueryBuilder\QueryBuilder;

/**
 * 获取用户列表
 */
public function list(RequestInterface $request): mixed
{
    // 构建固定查询条件
    $query = User::query()->where('status', Status::NORMAL);

    return QueryBuilder::for($query, $request)
        ->filters(['username', 'email', 'phone']) // 自动包含时间和ID过滤器
        ->allowedSorts(['id', 'username', 'created_at'])
        ->defaultSort('-created_at')
        // ->page() // 强制分页，page_size <= 0 时默认每页 15 条
        ->pagex(); // 支持 page_size=-1 获取所有记录（支持不分页场景）
}
```

### 9.2 QueryBuilder 固定条件规范

- **固定条件先构建**: QueryBuilder 的固定查询条件应该先构建好，再传入 `QueryBuilder::for()`
- **正确示例**: `QueryBuilder::for($query, $request)` 其中 `$query` 是预先构建的查询
- **错误示例**: 在 `QueryBuilder::for()` 后使用 `where()` 方法

### 9.3 QueryBuilder 核心功能

#### 9.3.1 filters() 方法

- 自动包含 ID 和时间范围过滤器
- 支持的时间过滤器：
  - `CreatedAtOfDay` - 指定日期当天
  - `CreatedAtBefore` - 创建时间之前
  - `CreatedAtAfter` - 创建时间之后
  - `CreatedAtBetween` - 创建时间区间
  - `UpdatedAtBefore` - 更新时间之前
  - `UpdatedAtAfter` - 更新时间之后
  - `UpdatedAtBetween` - 更新时间区间

#### 9.3.2 pagex() 方法

- 支持通过 `page_size=-1` 获取所有记录
- 当 `page_size <= 0` 时，`page()` 方法默认每页 15 条
- **避免重复接口**: 不要单独创建不分页接口，统一使用 `pagex()` 方法

#### 9.3.3 查询参数示例

```
?filter[username]=admin&filter[CreatedAtBetween]=2024-01-01,2024-12-31&sort=-created_at&page=1&page_size=15
```

## 10. 数据库迁移规范

### 10.1 迁移文件结构

```php
<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_user', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('用户ID，主键');
            $table->string('username', 50)->unique()->comment('用户名');
            $table->string('email', 100)->nullable()->unique()->comment('邮箱');
            $table->timestamps();

            // 索引
            $table->index(['username']);
            $table->index(['email']);

            $table->comment('交易所用户表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_user');
    }
};
```

### 10.2 字段定义规范

- 每个字段必须有 `comment()` 注释
- 字符串字段必须指定长度
- 外键字段使用 `bigInteger()` 类型
- 必要时添加索引

## 11. 注释规范

### 11.1 类注释

```php
/**
 * @property int $id 用户ID，主键
 * @property string $username 用户名
 * @property Status $status 状态
 * @property Carbon|null $created_at 创建时间
 */
```

### 11.2 方法注释

```php
/**
 * 用户邮箱注册
 *
 * @param array $data 注册数据
 * @return array 注册结果
 * @throws BusinessException 业务异常
 */
public function registerByEmail(array $data): array
```

### 11.3 行内注释

```php
// 事务
Db::beginTransaction();

// 创建用户
$username = 'CPX-' . strtoupper(substr(md5(uniqid() . microtime(true)), 0, 7));

// 如果父级是代理商，则当前用户为代理商直客
$this->agentClientCreate($user, $parent->agent_id, $inviterInfo['agentInviteCode']);
```

## 12. 异常处理规范

### 12.1 业务异常

```php
throw new BusinessException(ResultCode::FAIL, '注册失败，' . $th->getMessage());
```

### 12.2 异常捕获

```php
try {
    // 业务逻辑
} catch (\Throwable $th) {
    // 异常处理
    throw new BusinessException(ResultCode::FAIL, $th->getMessage());
}
```

## 13. 代码格式规范

### 13.1 缩进和空格

- 使用 4 个空格缩进
- 方法之间空一行
- 逻辑块之间空一行

### 13.2 方法链调用

```php
$user = User::query()
    ->where('status', Status::NORMAL)
    ->where('email', $email)
    ->first();
```

## 14. 安全规范

### 14.1 数据验证

- 所有用户输入必须验证
- 使用 FormRequest 进行参数验证
- 敏感操作需要额外验证

### 14.2 数据库操作

- 使用 Eloquent ORM，避免原生 SQL
- 重要操作使用事务
- 敏感字段需要加密存储

## 15. 日志记录规范

### 15.1 日志使用原则

- **默认不记录日志**: 若无特别说明，不要在代码中记录日志
- **仅在必要时记录**: 只在关键业务节点、错误处理、调试需要时记录日志
- **避免过度日志**: 避免在普通的 CRUD 操作中记录日志
- **日志辅助函数**: 需要记录日志时必须使用辅助函数：logger()
```php
logger('xxx/xxx.log')->info('xxxx');
logger('xxx/xxx.log')->error('xxxx');
```

### 15.2 需要记录日志的场景

- 重要业务操作（如用户注册、VIP 等级变更、资金操作）
- 异常和错误处理
- 安全相关操作（如登录、权限验证失败）
- 外部 API 调用
- 定时任务执行结果

### 15.3 不需要记录日志的场景

- 普通的查询操作
- 简单的数据更新
- 常规的业务逻辑处理
- 数据验证和格式化

## hyperf 文档（如果有需要查阅文档，请参考 hyperf 文档）

### 前言
- [项目介绍](#/README)

### 版本管理
- [版本计划](https://hyperf.wiki/3.1/#/zh-cn/release-planning)
- [版本说明](https://hyperf.wiki/3.1/#/zh-cn/versions)
- [版本更新记录](https://hyperf.wiki/3.1/#/zh-cn/changelog/3.1)

### 快速入门
- [安装](https://hyperf.wiki/3.1/#/zh-cn/quick-start/install)
- [快速开始](https://hyperf.wiki/3.1/#/zh-cn/quick-start/overview)
- [常见问题](https://hyperf.wiki/3.1/#/zh-cn/quick-start/questions)
- [编程须知](https://hyperf.wiki/3.1/#/zh-cn/quick-start/important)

### 核心架构
- [生命周期](https://hyperf.wiki/3.1/#/zh-cn/lifecycle)
- [协程](https://hyperf.wiki/3.1/#/zh-cn/coroutine)
- [配置](https://hyperf.wiki/3.1/#/zh-cn/config)
- [注解](https://hyperf.wiki/3.1/#/zh-cn/annotation)
- [依赖注入](https://hyperf.wiki/3.1/#/zh-cn/di)
- [事件机制](https://hyperf.wiki/3.1/#/zh-cn/event)
- [AOP 面向切面编程](https://hyperf.wiki/3.1/#/zh-cn/aop)

### 基础功能
- [路由](https://hyperf.wiki/3.1/#/zh-cn/router)
- [中间件](https://hyperf.wiki/3.1/#/zh-cn/middleware/middleware)
- [控制器](https://hyperf.wiki/3.1/#/zh-cn/controller)
- [请求](https://hyperf.wiki/3.1/#/zh-cn/request)
- [响应](https://hyperf.wiki/3.1/#/zh-cn/response)
- [异常处理](https://hyperf.wiki/3.1/#/zh-cn/exception-handler)
- [缓存](https://hyperf.wiki/3.1/#/zh-cn/cache)
- [日志](https://hyperf.wiki/3.1/#/zh-cn/logger)
- [分页器](https://hyperf.wiki/3.1/#/zh-cn/paginator)
- [命令行](https://hyperf.wiki/3.1/#/zh-cn/command)
- [自动化测试](https://hyperf.wiki/3.1/#/zh-cn/testing)
- [视图](https://hyperf.wiki/3.1/#/zh-cn/view)
- [视图引擎](https://hyperf.wiki/3.1/#/zh-cn/view-engine)
- [国际化](https://hyperf.wiki/3.1/#/zh-cn/translation)
- [验证器](https://hyperf.wiki/3.1/#/zh-cn/validation)
- [Session 会话管理](https://hyperf.wiki/3.1/#/zh-cn/session)
- [文件系统](https://hyperf.wiki/3.1/#/zh-cn/filesystem)

### 数据库模型
- [快速开始](https://hyperf.wiki/3.1/#/zh-cn/db/quick-start)
- [查询构造器](https://hyperf.wiki/3.1/#/zh-cn/db/querybuilder)
- [模型](https://hyperf.wiki/3.1/#/zh-cn/db/model)
- [创建脚本](https://hyperf.wiki/3.1/#/zh-cn/db/gen)
- [模型关系](https://hyperf.wiki/3.1/#/zh-cn/db/relationship)
- [查询分页](https://hyperf.wiki/3.1/#/zh-cn/db/paginator)
- [模型事件](https://hyperf.wiki/3.1/#/zh-cn/db/event)
- [模型缓存](https://hyperf.wiki/3.1/#/zh-cn/db/model-cache)
- [数据库迁移](https://hyperf.wiki/3.1/#/zh-cn/db/migration)
- [修改器](https://hyperf.wiki/3.1/#/zh-cn/db/mutators)
- [极简 DB 组件](https://hyperf.wiki/3.1/#/zh-cn/db/db)
- [API 资源构造器](https://hyperf.wiki/3.1/#/zh-cn/db/resource)
- [模型全文检索](https://hyperf.wiki/3.1/#/zh-cn/scout)

### Hyperf 生态
- [Nano](https://github.com/hyperf/nano)
- [GoTask](https://github.com/hyperf/gotask)
- [Box](https://hyperf.wiki/3.1/#/zh-cn/eco/box)

### 微服务
- [架构理念](https://hyperf.wiki/3.1/#/zh-cn/microservice)
- [JSON RPC 服务](https://hyperf.wiki/3.1/#/zh-cn/json-rpc)
- [gRPC 服务](https://hyperf.wiki/3.1/#/zh-cn/grpc)
- [多路复用 RPC 服务](https://hyperf.wiki/3.1/#/zh-cn/rpc-multiplex)
- [服务注册](https://hyperf.wiki/3.1/#/zh-cn/service-register)
- [服务重试](https://hyperf.wiki/3.1/#/zh-cn/retry)
- [服务熔断及降级](https://hyperf.wiki/3.1/#/zh-cn/circuit-breaker)
- [服务限流](https://hyperf.wiki/3.1/#/zh-cn/rate-limit)
- [配置中心](https://hyperf.wiki/3.1/#/zh-cn/config-center)
- [调用链追踪](https://hyperf.wiki/3.1/#/zh-cn/tracer)
- [服务监控](https://hyperf.wiki/3.1/#/zh-cn/metric)
- [分布式事务](https://hyperf.wiki/3.1/#/zh-cn/distributed-transaction)
- [Snowflake](https://hyperf.wiki/3.1/#/zh-cn/snowflake)

### 网络服务
- [TCP/UDP 服务](https://hyperf.wiki/3.1/#/zh-cn/tcp-server)
- [WebSocket 服务](https://hyperf.wiki/3.1/#/zh-cn/websocket-server)
- [Socket.io 服务](https://hyperf.wiki/3.1/#/zh-cn/socketio-server)
- [协程风格服务](https://hyperf.wiki/3.1/#/zh-cn/coroutine-server)

### 消息队列
- [Redis 异步队列](https://hyperf.wiki/3.1/#/zh-cn/async-queue)
- [AMQP](https://hyperf.wiki/3.1/#/zh-cn/amqp)
- [Nats](https://hyperf.wiki/3.1/#/zh-cn/nats)
- [NSQ](https://hyperf.wiki/3.1/#/zh-cn/nsq)
- [Kafka](https://hyperf.wiki/3.1/#/zh-cn/kafka)

### 客户端
- [Redis 协程客户端](https://hyperf.wiki/3.1/#/zh-cn/redis)
- [Guzzle HTTP 协程客户端](https://hyperf.wiki/3.1/#/zh-cn/guzzle)
- [Elasticsearch 协程客户端](https://hyperf.wiki/3.1/#/zh-cn/elasticsearch)
- [Consul 协程客户端](https://hyperf.wiki/3.1/#/zh-cn/consul)
- [ETCD 协程客户端](https://hyperf.wiki/3.1/#/zh-cn/etcd)
- [WebSocket 协程客户端](https://hyperf.wiki/3.1/#/zh-cn/websocket-client)
- [Nacos](https://hyperf.wiki/3.1/#/zh-cn/nacos)
- [Jet](https://hyperf.wiki/3.1/#/zh-cn/jet)

### 其它组件
- [连接池](https://hyperf.wiki/3.1/#/zh-cn/pool)
- [自定义进程](https://hyperf.wiki/3.1/#/zh-cn/process)
- [辅助类](https://hyperf.wiki/3.1/#/zh-cn/support)
- [定时任务](https://hyperf.wiki/3.1/#/zh-cn/crontab)
- [Task 机制](https://hyperf.wiki/3.1/#/zh-cn/task)
- [枚举类](https://hyperf.wiki/3.1/#/zh-cn/constants)
- [信号处理器](https://hyperf.wiki/3.1/#/zh-cn/signal)
- [ReactiveX](https://hyperf.wiki/3.1/#/zh-cn/reactive-x)
- [Watcher](https://hyperf.wiki/3.1/#/zh-cn/watcher)
- [开发者工具](https://hyperf.wiki/3.1/#/zh-cn/devtool)
- [Phar 打包器](https://hyperf.wiki/3.1/#/zh-cn/phar)
- [DAG](https://hyperf.wiki/3.1/#/zh-cn/dag)
- [RPN - 逆波兰表示法](https://hyperf.wiki/3.1/#/zh-cn/rpn)
- [Swagger 文档](https://hyperf.wiki/3.1/#/zh-cn/swagger)

### 应用部署
- [Docker Swarm 集群搭建](https://hyperf.wiki/3.1/#/zh-cn/tutorial/docker-swarm)
- [DaoCloud Devops 搭建](https://hyperf.wiki/3.1/#/zh-cn/tutorial/daocloud)
- [Supervisor 部署](https://hyperf.wiki/3.1/#/zh-cn/tutorial/supervisor)
- [Nginx 反向代理](https://hyperf.wiki/3.1/#/zh-cn/tutorial/nginx)
- [阿里云日志服务](https://hyperf.wiki/3.1/#/zh-cn/tutorial/aliyun-logger)

### Awesome Hyperf
- [协程组件库](https://hyperf.wiki/3.1/#/zh-cn/awesome-components)

### 组件开发指南
- [指南前言](https://hyperf.wiki/3.1/#/zh-cn/component-guide/intro)
- [创建新的组件](https://hyperf.wiki/3.1/#/zh-cn/component-guide/create)
- [ConfigProvider 机制](https://hyperf.wiki/3.1/#/zh-cn/component-guide/configprovider)

### 历史版本更新记录
- [3.0 更新记录](https://hyperf.wiki/3.1/#/zh-cn/changelog/3.0)
- [2.2 更新记录](https://hyperf.wiki/3.1/#/zh-cn/changelog/2.2)
- [2.1 更新记录](https://hyperf.wiki/3.1/#/zh-cn/changelog/2.1)
- [2.0 更新记录](https://hyperf.wiki/3.1/#/zh-cn/changelog/2.0)
- [1.1 更新记录](https://hyperf.wiki/3.1/#/zh-cn/changelog/1.1)
- [1.0 更新记录](https://hyperf.wiki/3.1/#/zh-cn/changelog/1.0)

### 版本升级指南
- [1.1 升级指南](https://hyperf.wiki/3.1/#/zh-cn/upgrade/1.1)
- [2.0 升级指南](https://hyperf.wiki/3.1/#/zh-cn/upgrade/2.0)
- [2.1 升级指南](https://hyperf.wiki/3.1/#/zh-cn/upgrade/2.1)
- [2.2 升级指南](https://hyperf.wiki/3.1/#/zh-cn/upgrade/2.2)
- [3.0 升级指南](https://hyperf.wiki/3.1/#/zh-cn/upgrade/3.0)
- [3.1 升级指南](https://hyperf.wiki/3.1/#/zh-cn/upgrade/3.1)